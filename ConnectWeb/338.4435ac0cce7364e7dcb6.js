"use strict";(self.webpackChunkhealth_teams=self.webpackChunkhealth_teams||[]).push([[338],{1338:(_,a,o)=>{o.r(a),o.d(a,{FamilyAuthModule:()=>u});var c=o(8583),Z=o(4494),t=o(639);function l(e,i){if(1&e){const n=t.EpF();t.TgZ(0,"section",1),t.TgZ(1,"div",2),t.TgZ(2,"div",3),t.TgZ(3,"div",4),t.TgZ(4,"div",5),t.TgZ(5,"div",6),t._<PERSON><PERSON>(6,"img",7),t.qZ<PERSON>(),t.TgZ(7,"div",8),t.TgZ(8,"h1",9),t._u<PERSON>(9,"Hello <PERSON>,"),t._<PERSON><PERSON>(10,"br"),t._u<PERSON>(11," Welcome to Health Teams"),t.q<PERSON><PERSON>(),t.q<PERSON><PERSON>(),t.Tg<PERSON>(12,"div",10),t._<PERSON><PERSON>(13,"img",11),t.qZA(),t.qZA(),t.qZA(),t.TgZ(14,"div",12),t.TgZ(15,"div",13),t.TgZ(16,"div",14),t.TgZ(17,"div",15),t.TgZ(18,"h1",9),t._uU(19,"Let\u2019s get your account setup. "),t._UZ(20,"br"),t._uU(21,"Are the below details correct?"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(22,"div",16),t.TgZ(23,"form"),t.TgZ(24,"div",3),t.TgZ(25,"div",17),t.TgZ(26,"div",18),t.TgZ(27,"label",19),t._uU(28,"First Name"),t.qZA(),t._UZ(29,"input",20),t.qZA(),t.qZA(),t.TgZ(30,"div",17),t.TgZ(31,"div",18),t.TgZ(32,"label",19),t._uU(33,"Last Name"),t.qZA(),t._UZ(34,"input",20),t.qZA(),t.qZA(),t.TgZ(35,"div",21),t.TgZ(36,"div",18),t.TgZ(37,"label",19),t._uU(38,"Email Address"),t.qZA(),t._UZ(39,"input",22),t.qZA(),t.qZA(),t.TgZ(40,"div",23),t.TgZ(41,"div",18),t.TgZ(42,"label",19),t._uU(43,"Country"),t.qZA(),t.TgZ(44,"select",24),t.TgZ(45,"option",25),t._uU(46,"+61"),t.qZA(),t.TgZ(47,"option",25),t._uU(48,"+91"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(49,"div",26),t.TgZ(50,"div",18),t.TgZ(51,"label",19),t._uU(52,"Mobile Number"),t.qZA(),t._UZ(53,"input",20),t.qZA(),t.qZA(),t.TgZ(54,"div",21),t.TgZ(55,"div",18),t.TgZ(56,"label",19),t._uU(57,"Connected Resident"),t.qZA(),t._UZ(58,"input",20),t.qZA(),t.qZA(),t.TgZ(59,"div",21),t.TgZ(60,"div",18),t.TgZ(61,"label",19),t._uU(62,"6 Digit Code"),t.qZA(),t._UZ(63,"input",20),t.qZA(),t.qZA(),t.TgZ(64,"div",21),t.TgZ(65,"div",27),t.TgZ(66,"a",28),t.NdJ("click",function(){t.CHM(n);const g=t.oxw();return g.step1=!1,g.step2=!0}),t._uU(67,"Correct "),t._UZ(68,"i",29),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA()}}function p(e,i){if(1&e){const n=t.EpF();t.TgZ(0,"section",1),t.TgZ(1,"div",2),t.TgZ(2,"div",3),t.TgZ(3,"div",4),t.TgZ(4,"div",5),t.TgZ(5,"div",6),t._UZ(6,"img",7),t.qZA(),t.TgZ(7,"div",8),t.TgZ(8,"h1",9),t._uU(9,"Hello Janice,"),t._UZ(10,"br"),t._uU(11," Welcome to Health Teams"),t.qZA(),t.qZA(),t.TgZ(12,"div",10),t._UZ(13,"img",11),t.qZA(),t.qZA(),t.qZA(),t.TgZ(14,"div",12),t.TgZ(15,"div",13),t.TgZ(16,"div",14),t.TgZ(17,"div",15),t.TgZ(18,"h1",9),t._uU(19,"Now let\u2019s set "),t._UZ(20,"br"),t._uU(21," your login details"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(22,"div",16),t.TgZ(23,"form"),t.TgZ(24,"div",3),t.TgZ(25,"div",30),t.TgZ(26,"label",31),t._UZ(27,"i",32),t._UZ(28,"input",33),t.TgZ(29,"span",34),t._uU(30,"Upload Profile "),t._UZ(31,"br"),t._uU(32," Photo"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(33,"div",21),t.TgZ(34,"div",18),t.TgZ(35,"label",19),t._uU(36,"Email Address"),t.qZA(),t._UZ(37,"input",22),t.qZA(),t.qZA(),t.TgZ(38,"div",21),t.TgZ(39,"div",18),t.TgZ(40,"label",19),t._uU(41,"Password"),t.qZA(),t._UZ(42,"input",35),t.qZA(),t.qZA(),t.TgZ(43,"div",21),t.TgZ(44,"div",18),t.TgZ(45,"label",19),t._uU(46,"Confirm Password"),t.qZA(),t._UZ(47,"input",35),t.qZA(),t.qZA(),t.TgZ(48,"div",21),t.TgZ(49,"div",27),t.TgZ(50,"a",28),t.NdJ("click",function(){t.CHM(n);const g=t.oxw();return g.step2=!1,g.step3=!0}),t._uU(51,"Finish Setup "),t._UZ(52,"i",29),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA()}}function d(e,i){1&e&&(t.TgZ(0,"section",1),t.TgZ(1,"div",2),t.TgZ(2,"div",36),t.TgZ(3,"div",37),t.TgZ(4,"div",5),t.TgZ(5,"div",6),t._UZ(6,"img",7),t.qZA(),t.TgZ(7,"div",8),t.TgZ(8,"h1",9),t._uU(9,"You\u2019re account is setup and, "),t._UZ(10,"br"),t._uU(11," connected to Janice Bauer "),t._UZ(12,"br"),t._UZ(13,"br"),t._uU(14,"Welcome aboard, Aaron"),t.qZA(),t.qZA(),t.TgZ(15,"div",21),t.TgZ(16,"div",27),t.TgZ(17,"a",38),t._uU(18,"Go To Dashboard"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(19,"div",10),t._UZ(20,"img",11),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA())}const s=[{path:"",redirectTo:"register",pathMatch:"full"},{path:"register",component:(()=>{class e{constructor(){this.step1=!0,this.step2=!1,this.step3=!1}ngOnInit(){}}return e.\u0275fac=function(n){return new(n||e)},e.\u0275cmp=t.Xpm({type:e,selectors:[["app-family-register"]],decls:3,vars:3,consts:[["class","bg-light reg-section",4,"ngIf"],[1,"bg-light","reg-section"],[1,"container-fluid"],[1,"row"],[1,"col-sm-12","col-md-12","col-lg-6"],[1,"register-bannerwrapper","dflexcol","justifycenter","aligncenter"],[1,"brand-logo-wrapper"],["src","./assets/images/logo/logo.png","alt",""],[1,"reg-header"],[1,"text-center"],[1,"reg-imgwrap","dflexrow","justifycenter"],["src","./assets/images/group-2.png","alt","",1,"frame-wrap"],[1,"col-sm-12","col-md-12","col-lg-6","padd0"],[1,"form-main-container"],[1,"reg-form-section","dflexcol","justifycenter","align-center"],[1,"form-heading-wrap"],[1,"form-input-container"],[1,"col-12","col-sm-6"],[1,"input-group"],[1,"label"],["type","text",1,"form-control"],[1,"col-12"],["type","email",1,"form-control"],[1,"col-sm-12","col-md-12","col-lg-4"],[1,"form-control"],["value",""],[1,"col-sm-12","col-md-12","col-lg-8"],[1,"reg-btn-wrap","dflexrow","justifycenter"],["routerLink","/dashboard",1,"btn","btn-orange",3,"click"],[1,"ri-check-fill"],[1,"col-12","dflexrow","justifycenter"],[1,"selectPic"],[1,"ri-upload-2-line"],["type","file","size","60",1,"selectPic-input"],[1,"upld"],["type","password",1,"form-control"],[1,"row","justify-content-center"],[1,"col-6"],["routerLink","/family",1,"btn","btn-orange"]],template:function(n,r){1&n&&(t.YNc(0,l,69,0,"section",0),t.YNc(1,p,53,0,"section",0),t.YNc(2,d,21,0,"section",0)),2&n&&(t.Q6J("ngIf",r.step1),t.xp6(1),t.Q6J("ngIf",r.step2),t.xp6(1),t.Q6J("ngIf",r.step3))},directives:[c.O5,Z.yS],styles:[".reg-section[_ngcontent-%COMP%]{position:relative}.register-bannerwrapper[_ngcontent-%COMP%]{position:relative;padding:40px}.brand-logo-wrapper[_ngcontent-%COMP%]{position:relative;margin-top:20px}.brand-logo-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:220px}.reg-header[_ngcontent-%COMP%]{position:relative;margin-top:30px}.reg-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;font-weight:900;color:#2fc4ca}.reg-imgwrap[_ngcontent-%COMP%]{position:relative;margin-top:53px;width:100%}.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:85%}.form-main-container[_ngcontent-%COMP%]{padding:40px;background:#F7F7F7;height:100%;border-radius:30px 0 0 30px}.reg-form-section[_ngcontent-%COMP%]{position:relative}.form-heading-wrap[_ngcontent-%COMP%]{margin-top:100px;position:relative}.form-heading-wrap[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;font-weight:900;color:#2fc4ca}.padd0[_ngcontent-%COMP%]{padding:0}.form-input-container[_ngcontent-%COMP%]{position:relative;padding:41px 14.5%}.register-text[_ngcontent-%COMP%]{position:relative;margin-top:8px}.register-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:14px;font-weight:700;line-height:20px}.reg-btn-wrap[_ngcontent-%COMP%]{position:relative;margin-top:28px}@media (min-width: 300px) and (max-width: 768px){.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%}.form-heading-wrap[_ngcontent-%COMP%]{margin-top:0}.form-main-container[_ngcontent-%COMP%]{padding:40px 20px}.form-input-container[_ngcontent-%COMP%]{padding:41px 10px}}@media (min-width: 768px) and (max-width: 1050px){.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%}.form-main-container[_ngcontent-%COMP%]{padding:40px 10px}}.selectPic[_ngcontent-%COMP%]{margin-bottom:30px;position:relative;background:#ffffff;height:116px;width:116px;border-radius:50%;cursor:pointer;display:flex;flex-direction:column;justify-content:center;align-items:center;border:2px solid #E2E2E2}.selectPic-input[type=file][_ngcontent-%COMP%]{display:none}.selectPic[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px}.upld[_ngcontent-%COMP%]{font-size:12px;text-align:center;margin-top:10px}.form-input-container[_ngcontent-%COMP%]{padding:30px 14.5%}"]}),e})()}];let m=(()=>{class e{}return e.\u0275fac=function(n){return new(n||e)},e.\u0275mod=t.oAB({type:e}),e.\u0275inj=t.cJS({imports:[[Z.Bz.forChild(s)],Z.Bz]}),e})(),u=(()=>{class e{}return e.\u0275fac=function(n){return new(n||e)},e.\u0275mod=t.oAB({type:e}),e.\u0275inj=t.cJS({imports:[[c.ez,m]]}),e})()}}]);