"use strict";(self.webpackChunkhealth_teams=self.webpackChunkhealth_teams||[]).push([[429],{7277:()=>{!function(e){const n=e.performance;function i(I){n&&n.mark&&n.mark(I)}function r(I,p){n&&n.measure&&n.measure(I,p)}i("Zone");const c=e.__Zone_symbol_prefix||"__zone_symbol__";function u(I){return c+I}const f=!0===e[u("forceDuplicateZoneCheck")];if(e.Zone){if(f||"function"!=typeof e.Zone.__symbol__)throw new Error("Zone already loaded.");return e.Zone}let _=(()=>{class I{constructor(t,o){this._parent=t,this._name=o?o.name||"unnamed":"<root>",this._properties=o&&o.properties||{},this._zoneDelegate=new T(this,this._parent&&this._parent._zoneDelegate,o)}static assertZonePatched(){if(e.Promise!==J.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let t=I.current;for(;t.parent;)t=t.parent;return t}static get current(){return G.zone}static get currentTask(){return te}static __load_patch(t,o,y=!1){if(J.hasOwnProperty(t)){if(!y&&f)throw Error("Already loaded patch: "+t)}else if(!e["__Zone_disable_"+t]){const P="Zone:"+t;i(P),J[t]=o(e,I,le),r(P,P)}}get parent(){return this._parent}get name(){return this._name}get(t){const o=this.getZoneWith(t);if(o)return o._properties[t]}getZoneWith(t){let o=this;for(;o;){if(o._properties.hasOwnProperty(t))return o;o=o._parent}return null}fork(t){if(!t)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,t)}wrap(t,o){if("function"!=typeof t)throw new Error("Expecting function got: "+t);const y=this._zoneDelegate.intercept(this,t,o),P=this;return function(){return P.runGuarded(y,this,arguments,o)}}run(t,o,y,P){G={parent:G,zone:this};try{return this._zoneDelegate.invoke(this,t,o,y,P)}finally{G=G.parent}}runGuarded(t,o=null,y,P){G={parent:G,zone:this};try{try{return this._zoneDelegate.invoke(this,t,o,y,P)}catch(K){if(this._zoneDelegate.handleError(this,K))throw K}}finally{G=G.parent}}runTask(t,o,y){if(t.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(t.zone||z).name+"; Execution: "+this.name+")");if(t.state===j&&(t.type===R||t.type===M))return;const P=t.state!=X;P&&t._transitionTo(X,O),t.runCount++;const K=te;te=t,G={parent:G,zone:this};try{t.type==M&&t.data&&!t.data.isPeriodic&&(t.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,t,o,y)}catch(l){if(this._zoneDelegate.handleError(this,l))throw l}}finally{t.state!==j&&t.state!==Y&&(t.type==R||t.data&&t.data.isPeriodic?P&&t._transitionTo(O,X):(t.runCount=0,this._updateTaskCount(t,-1),P&&t._transitionTo(j,X,j))),G=G.parent,te=K}}scheduleTask(t){if(t.zone&&t.zone!==this){let y=this;for(;y;){if(y===t.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${t.zone.name}`);y=y.parent}}t._transitionTo(q,j);const o=[];t._zoneDelegates=o,t._zone=this;try{t=this._zoneDelegate.scheduleTask(this,t)}catch(y){throw t._transitionTo(Y,q,j),this._zoneDelegate.handleError(this,y),y}return t._zoneDelegates===o&&this._updateTaskCount(t,1),t.state==q&&t._transitionTo(O,q),t}scheduleMicroTask(t,o,y,P){return this.scheduleTask(new m(v,t,o,y,P,void 0))}scheduleMacroTask(t,o,y,P,K){return this.scheduleTask(new m(M,t,o,y,P,K))}scheduleEventTask(t,o,y,P,K){return this.scheduleTask(new m(R,t,o,y,P,K))}cancelTask(t){if(t.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(t.zone||z).name+"; Execution: "+this.name+")");t._transitionTo(A,O,X);try{this._zoneDelegate.cancelTask(this,t)}catch(o){throw t._transitionTo(Y,A),this._zoneDelegate.handleError(this,o),o}return this._updateTaskCount(t,-1),t._transitionTo(j,A),t.runCount=0,t}_updateTaskCount(t,o){const y=t._zoneDelegates;-1==o&&(t._zoneDelegates=null);for(let P=0;P<y.length;P++)y[P]._updateTaskCount(t.type,o)}}return I.__symbol__=u,I})();const g={name:"",onHasTask:(I,p,t,o)=>I.hasTask(t,o),onScheduleTask:(I,p,t,o)=>I.scheduleTask(t,o),onInvokeTask:(I,p,t,o,y,P)=>I.invokeTask(t,o,y,P),onCancelTask:(I,p,t,o)=>I.cancelTask(t,o)};class T{constructor(p,t,o){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this.zone=p,this._parentDelegate=t,this._forkZS=o&&(o&&o.onFork?o:t._forkZS),this._forkDlgt=o&&(o.onFork?t:t._forkDlgt),this._forkCurrZone=o&&(o.onFork?this.zone:t._forkCurrZone),this._interceptZS=o&&(o.onIntercept?o:t._interceptZS),this._interceptDlgt=o&&(o.onIntercept?t:t._interceptDlgt),this._interceptCurrZone=o&&(o.onIntercept?this.zone:t._interceptCurrZone),this._invokeZS=o&&(o.onInvoke?o:t._invokeZS),this._invokeDlgt=o&&(o.onInvoke?t:t._invokeDlgt),this._invokeCurrZone=o&&(o.onInvoke?this.zone:t._invokeCurrZone),this._handleErrorZS=o&&(o.onHandleError?o:t._handleErrorZS),this._handleErrorDlgt=o&&(o.onHandleError?t:t._handleErrorDlgt),this._handleErrorCurrZone=o&&(o.onHandleError?this.zone:t._handleErrorCurrZone),this._scheduleTaskZS=o&&(o.onScheduleTask?o:t._scheduleTaskZS),this._scheduleTaskDlgt=o&&(o.onScheduleTask?t:t._scheduleTaskDlgt),this._scheduleTaskCurrZone=o&&(o.onScheduleTask?this.zone:t._scheduleTaskCurrZone),this._invokeTaskZS=o&&(o.onInvokeTask?o:t._invokeTaskZS),this._invokeTaskDlgt=o&&(o.onInvokeTask?t:t._invokeTaskDlgt),this._invokeTaskCurrZone=o&&(o.onInvokeTask?this.zone:t._invokeTaskCurrZone),this._cancelTaskZS=o&&(o.onCancelTask?o:t._cancelTaskZS),this._cancelTaskDlgt=o&&(o.onCancelTask?t:t._cancelTaskDlgt),this._cancelTaskCurrZone=o&&(o.onCancelTask?this.zone:t._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const y=o&&o.onHasTask;(y||t&&t._hasTaskZS)&&(this._hasTaskZS=y?o:g,this._hasTaskDlgt=t,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=p,o.onScheduleTask||(this._scheduleTaskZS=g,this._scheduleTaskDlgt=t,this._scheduleTaskCurrZone=this.zone),o.onInvokeTask||(this._invokeTaskZS=g,this._invokeTaskDlgt=t,this._invokeTaskCurrZone=this.zone),o.onCancelTask||(this._cancelTaskZS=g,this._cancelTaskDlgt=t,this._cancelTaskCurrZone=this.zone))}fork(p,t){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,p,t):new _(p,t)}intercept(p,t,o){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,p,t,o):t}invoke(p,t,o,y,P){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,p,t,o,y,P):t.apply(o,y)}handleError(p,t){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,p,t)}scheduleTask(p,t){let o=t;if(this._scheduleTaskZS)this._hasTaskZS&&o._zoneDelegates.push(this._hasTaskDlgtOwner),o=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,p,t),o||(o=t);else if(t.scheduleFn)t.scheduleFn(t);else{if(t.type!=v)throw new Error("Task is missing scheduleFn.");d(t)}return o}invokeTask(p,t,o,y){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,p,t,o,y):t.callback.apply(o,y)}cancelTask(p,t){let o;if(this._cancelTaskZS)o=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,p,t);else{if(!t.cancelFn)throw Error("Task is not cancelable");o=t.cancelFn(t)}return o}hasTask(p,t){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,p,t)}catch(o){this.handleError(p,o)}}_updateTaskCount(p,t){const o=this._taskCounts,y=o[p],P=o[p]=y+t;if(P<0)throw new Error("More tasks executed then were scheduled.");0!=y&&0!=P||this.hasTask(this.zone,{microTask:o.microTask>0,macroTask:o.macroTask>0,eventTask:o.eventTask>0,change:p})}}class m{constructor(p,t,o,y,P,K){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=p,this.source=t,this.data=y,this.scheduleFn=P,this.cancelFn=K,!o)throw new Error("callback is not defined");this.callback=o;const l=this;this.invoke=p===R&&y&&y.useG?m.invokeTask:function(){return m.invokeTask.call(e,l,this,arguments)}}static invokeTask(p,t,o){p||(p=this),re++;try{return p.runCount++,p.zone.runTask(p,t,o)}finally{1==re&&L(),re--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(j,q)}_transitionTo(p,t,o){if(this._state!==t&&this._state!==o)throw new Error(`${this.type} '${this.source}': can not transition to '${p}', expecting state '${t}'${o?" or '"+o+"'":""}, was '${this._state}'.`);this._state=p,p==j&&(this._zoneDelegates=null)}toString(){return this.data&&void 0!==this.data.handleId?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const S=u("setTimeout"),D=u("Promise"),Z=u("then");let E,B=[],V=!1;function d(I){if(0===re&&0===B.length)if(E||e[D]&&(E=e[D].resolve(0)),E){let p=E[Z];p||(p=E.then),p.call(E,L)}else e[S](L,0);I&&B.push(I)}function L(){if(!V){for(V=!0;B.length;){const I=B;B=[];for(let p=0;p<I.length;p++){const t=I[p];try{t.zone.runTask(t,null,null)}catch(o){le.onUnhandledError(o)}}}le.microtaskDrainDone(),V=!1}}const z={name:"NO ZONE"},j="notScheduled",q="scheduling",O="scheduled",X="running",A="canceling",Y="unknown",v="microTask",M="macroTask",R="eventTask",J={},le={symbol:u,currentZoneFrame:()=>G,onUnhandledError:F,microtaskDrainDone:F,scheduleMicroTask:d,showUncaughtError:()=>!_[u("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:F,patchMethod:()=>F,bindArguments:()=>[],patchThen:()=>F,patchMacroTask:()=>F,patchEventPrototype:()=>F,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>F,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>F,wrapWithCurrentZone:()=>F,filterProperties:()=>[],attachOriginToPatched:()=>F,_redefineProperty:()=>F,patchCallbacks:()=>F};let G={parent:null,zone:new _(null,null)},te=null,re=0;function F(){}r("Zone","Zone"),e.Zone=_}("undefined"!=typeof window&&window||"undefined"!=typeof self&&self||global);const ue=Object.getOwnPropertyDescriptor,he=Object.defineProperty,de=Object.getPrototypeOf,Be=Object.create,ut=Array.prototype.slice,Se="addEventListener",Oe="removeEventListener",Ze=Zone.__symbol__(Se),Ie=Zone.__symbol__(Oe),se="true",ie="false",ke=Zone.__symbol__("");function Le(e,n){return Zone.current.wrap(e,n)}function Me(e,n,i,r,c){return Zone.current.scheduleMacroTask(e,n,i,r,c)}const x=Zone.__symbol__,Pe="undefined"!=typeof window,pe=Pe?window:void 0,$=Pe&&pe||"object"==typeof self&&self||global,ht=[null];function Ae(e,n){for(let i=e.length-1;i>=0;i--)"function"==typeof e[i]&&(e[i]=Le(e[i],n+"_"+i));return e}function Fe(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&void 0===e.set)}const Ue="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,Re=!("nw"in $)&&void 0!==$.process&&"[object process]"==={}.toString.call($.process),je=!Re&&!Ue&&!(!Pe||!pe.HTMLElement),We=void 0!==$.process&&"[object process]"==={}.toString.call($.process)&&!Ue&&!(!Pe||!pe.HTMLElement),Ce={},qe=function(e){if(!(e=e||$.event))return;let n=Ce[e.type];n||(n=Ce[e.type]=x("ON_PROPERTY"+e.type));const i=this||e.target||$,r=i[n];let c;if(je&&i===pe&&"error"===e.type){const u=e;c=r&&r.call(this,u.message,u.filename,u.lineno,u.colno,u.error),!0===c&&e.preventDefault()}else c=r&&r.apply(this,arguments),null!=c&&!c&&e.preventDefault();return c};function Xe(e,n,i){let r=ue(e,n);if(!r&&i&&ue(i,n)&&(r={enumerable:!0,configurable:!0}),!r||!r.configurable)return;const c=x("on"+n+"patched");if(e.hasOwnProperty(c)&&e[c])return;delete r.writable,delete r.value;const u=r.get,f=r.set,_=n.substr(2);let g=Ce[_];g||(g=Ce[_]=x("ON_PROPERTY"+_)),r.set=function(T){let m=this;!m&&e===$&&(m=$),m&&(m[g]&&m.removeEventListener(_,qe),f&&f.apply(m,ht),"function"==typeof T?(m[g]=T,m.addEventListener(_,qe,!1)):m[g]=null)},r.get=function(){let T=this;if(!T&&e===$&&(T=$),!T)return null;const m=T[g];if(m)return m;if(u){let S=u&&u.call(this);if(S)return r.set.call(this,S),"function"==typeof T.removeAttribute&&T.removeAttribute(n),S}return null},he(e,n,r),e[c]=!0}function Ye(e,n,i){if(n)for(let r=0;r<n.length;r++)Xe(e,"on"+n[r],i);else{const r=[];for(const c in e)"on"==c.substr(0,2)&&r.push(c);for(let c=0;c<r.length;c++)Xe(e,r[c],i)}}const ne=x("originalInstance");function ve(e){const n=$[e];if(!n)return;$[x(e)]=n,$[e]=function(){const c=Ae(arguments,e);switch(c.length){case 0:this[ne]=new n;break;case 1:this[ne]=new n(c[0]);break;case 2:this[ne]=new n(c[0],c[1]);break;case 3:this[ne]=new n(c[0],c[1],c[2]);break;case 4:this[ne]=new n(c[0],c[1],c[2],c[3]);break;default:throw new Error("Arg list too long.")}},ae($[e],n);const i=new n(function(){});let r;for(r in i)"XMLHttpRequest"===e&&"responseBlob"===r||function(c){"function"==typeof i[c]?$[e].prototype[c]=function(){return this[ne][c].apply(this[ne],arguments)}:he($[e].prototype,c,{set:function(u){"function"==typeof u?(this[ne][c]=Le(u,e+"."+c),ae(this[ne][c],u)):this[ne][c]=u},get:function(){return this[ne][c]}})}(r);for(r in n)"prototype"!==r&&n.hasOwnProperty(r)&&($[e][r]=n[r])}function ce(e,n,i){let r=e;for(;r&&!r.hasOwnProperty(n);)r=de(r);!r&&e[n]&&(r=e);const c=x(n);let u=null;if(r&&(!(u=r[c])||!r.hasOwnProperty(c))&&(u=r[c]=r[n],Fe(r&&ue(r,n)))){const _=i(u,c,n);r[n]=function(){return _(this,arguments)},ae(r[n],u)}return u}function _t(e,n,i){let r=null;function c(u){const f=u.data;return f.args[f.cbIdx]=function(){u.invoke.apply(this,arguments)},r.apply(f.target,f.args),u}r=ce(e,n,u=>function(f,_){const g=i(f,_);return g.cbIdx>=0&&"function"==typeof _[g.cbIdx]?Me(g.name,_[g.cbIdx],g,c):u.apply(f,_)})}function ae(e,n){e[x("OriginalDelegate")]=n}let $e=!1,He=!1;function mt(){if($e)return He;$e=!0;try{const e=pe.navigator.userAgent;(-1!==e.indexOf("MSIE ")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/"))&&(He=!0)}catch(e){}return He}Zone.__load_patch("ZoneAwarePromise",(e,n,i)=>{const r=Object.getOwnPropertyDescriptor,c=Object.defineProperty,f=i.symbol,_=[],g=!0===e[f("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],T=f("Promise"),m=f("then");i.onUnhandledError=l=>{if(i.showUncaughtError()){const s=l&&l.rejection;s?console.error("Unhandled Promise rejection:",s instanceof Error?s.message:s,"; Zone:",l.zone.name,"; Task:",l.task&&l.task.source,"; Value:",s,s instanceof Error?s.stack:void 0):console.error(l)}},i.microtaskDrainDone=()=>{for(;_.length;){const l=_.shift();try{l.zone.runGuarded(()=>{throw l.throwOriginal?l.rejection:l})}catch(s){Z(s)}}};const D=f("unhandledPromiseRejectionHandler");function Z(l){i.onUnhandledError(l);try{const s=n[D];"function"==typeof s&&s.call(this,l)}catch(s){}}function B(l){return l&&l.then}function V(l){return l}function E(l){return t.reject(l)}const d=f("state"),L=f("value"),z=f("finally"),j=f("parentPromiseValue"),q=f("parentPromiseState"),X=null,A=!0,Y=!1;function M(l,s){return a=>{try{G(l,s,a)}catch(h){G(l,!1,h)}}}const le=f("currentTaskTrace");function G(l,s,a){const h=function(){let l=!1;return function(a){return function(){l||(l=!0,a.apply(null,arguments))}}}();if(l===a)throw new TypeError("Promise resolved with itself");if(l[d]===X){let w=null;try{("object"==typeof a||"function"==typeof a)&&(w=a&&a.then)}catch(C){return h(()=>{G(l,!1,C)})(),l}if(s!==Y&&a instanceof t&&a.hasOwnProperty(d)&&a.hasOwnProperty(L)&&a[d]!==X)re(a),G(l,a[d],a[L]);else if(s!==Y&&"function"==typeof w)try{w.call(a,h(M(l,s)),h(M(l,!1)))}catch(C){h(()=>{G(l,!1,C)})()}else{l[d]=s;const C=l[L];if(l[L]=a,l[z]===z&&s===A&&(l[d]=l[q],l[L]=l[j]),s===Y&&a instanceof Error){const k=n.currentTask&&n.currentTask.data&&n.currentTask.data.__creationTrace__;k&&c(a,le,{configurable:!0,enumerable:!1,writable:!0,value:k})}for(let k=0;k<C.length;)F(l,C[k++],C[k++],C[k++],C[k++]);if(0==C.length&&s==Y){l[d]=0;let k=a;try{throw new Error("Uncaught (in promise): "+function(l){return l&&l.toString===Object.prototype.toString?(l.constructor&&l.constructor.name||"")+": "+JSON.stringify(l):l?l.toString():Object.prototype.toString.call(l)}(a)+(a&&a.stack?"\n"+a.stack:""))}catch(b){k=b}g&&(k.throwOriginal=!0),k.rejection=a,k.promise=l,k.zone=n.current,k.task=n.currentTask,_.push(k),i.scheduleMicroTask()}}}return l}const te=f("rejectionHandledHandler");function re(l){if(0===l[d]){try{const s=n[te];s&&"function"==typeof s&&s.call(this,{rejection:l[L],promise:l})}catch(s){}l[d]=Y;for(let s=0;s<_.length;s++)l===_[s].promise&&_.splice(s,1)}}function F(l,s,a,h,w){re(l);const C=l[d],k=C?"function"==typeof h?h:V:"function"==typeof w?w:E;s.scheduleMicroTask("Promise.then",()=>{try{const b=l[L],N=!!a&&z===a[z];N&&(a[j]=b,a[q]=C);const H=s.run(k,void 0,N&&k!==E&&k!==V?[]:[b]);G(a,!0,H)}catch(b){G(a,!1,b)}},a)}const p=function(){};class t{static toString(){return"function ZoneAwarePromise() { [native code] }"}static resolve(s){return G(new this(null),A,s)}static reject(s){return G(new this(null),Y,s)}static race(s){let a,h,w=new this((b,N)=>{a=b,h=N});function C(b){a(b)}function k(b){h(b)}for(let b of s)B(b)||(b=this.resolve(b)),b.then(C,k);return w}static all(s){return t.allWithCallback(s)}static allSettled(s){return(this&&this.prototype instanceof t?this:t).allWithCallback(s,{thenCallback:h=>({status:"fulfilled",value:h}),errorCallback:h=>({status:"rejected",reason:h})})}static allWithCallback(s,a){let h,w,C=new this((H,U)=>{h=H,w=U}),k=2,b=0;const N=[];for(let H of s){B(H)||(H=this.resolve(H));const U=b;try{H.then(Q=>{N[U]=a?a.thenCallback(Q):Q,k--,0===k&&h(N)},Q=>{a?(N[U]=a.errorCallback(Q),k--,0===k&&h(N)):w(Q)})}catch(Q){w(Q)}k++,b++}return k-=2,0===k&&h(N),C}constructor(s){const a=this;if(!(a instanceof t))throw new Error("Must be an instanceof Promise.");a[d]=X,a[L]=[];try{s&&s(M(a,A),M(a,Y))}catch(h){G(a,!1,h)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return t}then(s,a){let h=this.constructor[Symbol.species];(!h||"function"!=typeof h)&&(h=this.constructor||t);const w=new h(p),C=n.current;return this[d]==X?this[L].push(C,w,s,a):F(this,C,w,s,a),w}catch(s){return this.then(null,s)}finally(s){let a=this.constructor[Symbol.species];(!a||"function"!=typeof a)&&(a=t);const h=new a(p);h[z]=z;const w=n.current;return this[d]==X?this[L].push(w,h,s,s):F(this,w,h,s,s),h}}t.resolve=t.resolve,t.reject=t.reject,t.race=t.race,t.all=t.all;const o=e[T]=e.Promise;e.Promise=t;const y=f("thenPatched");function P(l){const s=l.prototype,a=r(s,"then");if(a&&(!1===a.writable||!a.configurable))return;const h=s.then;s[m]=h,l.prototype.then=function(w,C){return new t((b,N)=>{h.call(this,b,N)}).then(w,C)},l[y]=!0}return i.patchThen=P,o&&(P(o),ce(e,"fetch",l=>function(l){return function(s,a){let h=l.apply(s,a);if(h instanceof t)return h;let w=h.constructor;return w[y]||P(w),h}}(l))),Promise[n.__symbol__("uncaughtPromiseErrors")]=_,t}),Zone.__load_patch("toString",e=>{const n=Function.prototype.toString,i=x("OriginalDelegate"),r=x("Promise"),c=x("Error"),u=function(){if("function"==typeof this){const T=this[i];if(T)return"function"==typeof T?n.call(T):Object.prototype.toString.call(T);if(this===Promise){const m=e[r];if(m)return n.call(m)}if(this===Error){const m=e[c];if(m)return n.call(m)}}return n.call(this)};u[i]=n,Function.prototype.toString=u;const f=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":f.call(this)}});let me=!1;if("undefined"!=typeof window)try{const e=Object.defineProperty({},"passive",{get:function(){me=!0}});window.addEventListener("test",e,e),window.removeEventListener("test",e,e)}catch(e){me=!1}const Et={useG:!0},ee={},Ke={},Je=new RegExp("^"+ke+"(\\w+)(true|false)$"),xe=x("propagationStopped");function Qe(e,n){const i=(n?n(e):e)+ie,r=(n?n(e):e)+se,c=ke+i,u=ke+r;ee[e]={},ee[e][ie]=c,ee[e][se]=u}function Tt(e,n,i){const r=i&&i.add||Se,c=i&&i.rm||Oe,u=i&&i.listeners||"eventListeners",f=i&&i.rmAll||"removeAllListeners",_=x(r),g="."+r+":",S=function(E,d,L){if(E.isRemoved)return;const z=E.callback;"object"==typeof z&&z.handleEvent&&(E.callback=q=>z.handleEvent(q),E.originalDelegate=z),E.invoke(E,d,[L]);const j=E.options;j&&"object"==typeof j&&j.once&&d[c].call(d,L.type,E.originalDelegate?E.originalDelegate:E.callback,j)},D=function(E){if(!(E=E||e.event))return;const d=this||E.target||e,L=d[ee[E.type][ie]];if(L)if(1===L.length)S(L[0],d,E);else{const z=L.slice();for(let j=0;j<z.length&&(!E||!0!==E[xe]);j++)S(z[j],d,E)}},Z=function(E){if(!(E=E||e.event))return;const d=this||E.target||e,L=d[ee[E.type][se]];if(L)if(1===L.length)S(L[0],d,E);else{const z=L.slice();for(let j=0;j<z.length&&(!E||!0!==E[xe]);j++)S(z[j],d,E)}};function B(E,d){if(!E)return!1;let L=!0;d&&void 0!==d.useG&&(L=d.useG);const z=d&&d.vh;let j=!0;d&&void 0!==d.chkDup&&(j=d.chkDup);let q=!1;d&&void 0!==d.rt&&(q=d.rt);let O=E;for(;O&&!O.hasOwnProperty(r);)O=de(O);if(!O&&E[r]&&(O=E),!O||O[_])return!1;const X=d&&d.eventNameToString,A={},Y=O[_]=O[r],v=O[x(c)]=O[c],M=O[x(u)]=O[u],R=O[x(f)]=O[f];let J;function le(s,a){return!me&&"object"==typeof s&&s?!!s.capture:me&&a?"boolean"==typeof s?{capture:s,passive:!0}:s?"object"==typeof s&&!1!==s.passive?Object.assign(Object.assign({},s),{passive:!0}):s:{passive:!0}:s}d&&d.prepend&&(J=O[x(d.prepend)]=O[d.prepend]);const p=L?function(s){if(!A.isExisting)return Y.call(A.target,A.eventName,A.capture?Z:D,A.options)}:function(s){return Y.call(A.target,A.eventName,s.invoke,A.options)},t=L?function(s){if(!s.isRemoved){const a=ee[s.eventName];let h;a&&(h=a[s.capture?se:ie]);const w=h&&s.target[h];if(w)for(let C=0;C<w.length;C++)if(w[C]===s){w.splice(C,1),s.isRemoved=!0,0===w.length&&(s.allRemoved=!0,s.target[h]=null);break}}if(s.allRemoved)return v.call(s.target,s.eventName,s.capture?Z:D,s.options)}:function(s){return v.call(s.target,s.eventName,s.invoke,s.options)},y=d&&d.diff?d.diff:function(s,a){const h=typeof a;return"function"===h&&s.callback===a||"object"===h&&s.originalDelegate===a},P=Zone[x("UNPATCHED_EVENTS")],K=e[x("PASSIVE_EVENTS")],l=function(s,a,h,w,C=!1,k=!1){return function(){const b=this||e;let N=arguments[0];d&&d.transferEventName&&(N=d.transferEventName(N));let H=arguments[1];if(!H)return s.apply(this,arguments);if(Re&&"uncaughtException"===N)return s.apply(this,arguments);let U=!1;if("function"!=typeof H){if(!H.handleEvent)return s.apply(this,arguments);U=!0}if(z&&!z(s,H,b,arguments))return;const Q=me&&!!K&&-1!==K.indexOf(N),oe=le(arguments[2],Q);if(P)for(let _e=0;_e<P.length;_e++)if(N===P[_e])return Q?s.call(b,N,H,oe):s.apply(this,arguments);const Ge=!!oe&&("boolean"==typeof oe||oe.capture),st=!(!oe||"object"!=typeof oe)&&oe.once,At=Zone.current;let ze=ee[N];ze||(Qe(N,X),ze=ee[N]);const it=ze[Ge?se:ie];let De,ge=b[it],ct=!1;if(ge){if(ct=!0,j)for(let _e=0;_e<ge.length;_e++)if(y(ge[_e],H))return}else ge=b[it]=[];const at=b.constructor.name,lt=Ke[at];lt&&(De=lt[N]),De||(De=at+a+(X?X(N):N)),A.options=oe,st&&(A.options.once=!1),A.target=b,A.capture=Ge,A.eventName=N,A.isExisting=ct;const be=L?Et:void 0;be&&(be.taskData=A);const fe=At.scheduleEventTask(De,H,be,h,w);return A.target=null,be&&(be.taskData=null),st&&(oe.once=!0),!me&&"boolean"==typeof fe.options||(fe.options=oe),fe.target=b,fe.capture=Ge,fe.eventName=N,U&&(fe.originalDelegate=H),k?ge.unshift(fe):ge.push(fe),C?b:void 0}};return O[r]=l(Y,g,p,t,q),J&&(O.prependListener=l(J,".prependListener:",function(s){return J.call(A.target,A.eventName,s.invoke,A.options)},t,q,!0)),O[c]=function(){const s=this||e;let a=arguments[0];d&&d.transferEventName&&(a=d.transferEventName(a));const h=arguments[2],w=!!h&&("boolean"==typeof h||h.capture),C=arguments[1];if(!C)return v.apply(this,arguments);if(z&&!z(v,C,s,arguments))return;const k=ee[a];let b;k&&(b=k[w?se:ie]);const N=b&&s[b];if(N)for(let H=0;H<N.length;H++){const U=N[H];if(y(U,C))return N.splice(H,1),U.isRemoved=!0,0===N.length&&(U.allRemoved=!0,s[b]=null,"string"==typeof a)&&(s[ke+"ON_PROPERTY"+a]=null),U.zone.cancelTask(U),q?s:void 0}return v.apply(this,arguments)},O[u]=function(){const s=this||e;let a=arguments[0];d&&d.transferEventName&&(a=d.transferEventName(a));const h=[],w=et(s,X?X(a):a);for(let C=0;C<w.length;C++){const k=w[C];h.push(k.originalDelegate?k.originalDelegate:k.callback)}return h},O[f]=function(){const s=this||e;let a=arguments[0];if(a){d&&d.transferEventName&&(a=d.transferEventName(a));const h=ee[a];if(h){const k=s[h[ie]],b=s[h[se]];if(k){const N=k.slice();for(let H=0;H<N.length;H++){const U=N[H];this[c].call(this,a,U.originalDelegate?U.originalDelegate:U.callback,U.options)}}if(b){const N=b.slice();for(let H=0;H<N.length;H++){const U=N[H];this[c].call(this,a,U.originalDelegate?U.originalDelegate:U.callback,U.options)}}}}else{const h=Object.keys(s);for(let w=0;w<h.length;w++){const k=Je.exec(h[w]);let b=k&&k[1];b&&"removeListener"!==b&&this[f].call(this,b)}this[f].call(this,"removeListener")}if(q)return this},ae(O[r],Y),ae(O[c],v),R&&ae(O[f],R),M&&ae(O[u],M),!0}let V=[];for(let E=0;E<n.length;E++)V[E]=B(n[E],i);return V}function et(e,n){if(!n){const u=[];for(let f in e){const _=Je.exec(f);let g=_&&_[1];if(g&&(!n||g===n)){const T=e[f];if(T)for(let m=0;m<T.length;m++)u.push(T[m])}}return u}let i=ee[n];i||(Qe(n),i=ee[n]);const r=e[i[ie]],c=e[i[se]];return r?c?r.concat(c):r.slice():c?c.slice():[]}function yt(e,n){const i=e.Event;i&&i.prototype&&n.patchMethod(i.prototype,"stopImmediatePropagation",r=>function(c,u){c[xe]=!0,r&&r.apply(c,u)})}function gt(e,n,i,r,c){const u=Zone.__symbol__(r);if(n[u])return;const f=n[u]=n[r];n[r]=function(_,g,T){return g&&g.prototype&&c.forEach(function(m){const S=`${i}.${r}::`+m,D=g.prototype;if(D.hasOwnProperty(m)){const Z=e.ObjectGetOwnPropertyDescriptor(D,m);Z&&Z.value?(Z.value=e.wrapWithCurrentZone(Z.value,S),e._redefineProperty(g.prototype,m,Z)):D[m]&&(D[m]=e.wrapWithCurrentZone(D[m],S))}else D[m]&&(D[m]=e.wrapWithCurrentZone(D[m],S))}),f.call(n,_,g,T)},e.attachOriginToPatched(n[r],f)}const Ve=["absolutedeviceorientation","afterinput","afterprint","appinstalled","beforeinstallprompt","beforeprint","beforeunload","devicelight","devicemotion","deviceorientation","deviceorientationabsolute","deviceproximity","hashchange","languagechange","message","mozbeforepaint","offline","online","paint","pageshow","pagehide","popstate","rejectionhandled","storage","unhandledrejection","unload","userproximity","vrdisplayconnected","vrdisplaydisconnected","vrdisplaypresentchange"],wt=["encrypted","waitingforkey","msneedkey","mozinterruptbegin","mozinterruptend"],tt=["load"],nt=["blur","error","focus","load","resize","scroll","messageerror"],Dt=["bounce","finish","start"],rt=["loadstart","progress","abort","error","load","progress","timeout","loadend","readystatechange"],Ee=["upgradeneeded","complete","abort","success","error","blocked","versionchange","close"],St=["close","error","open","message"],Ot=["error","message"],Te=["abort","animationcancel","animationend","animationiteration","auxclick","beforeinput","blur","cancel","canplay","canplaythrough","change","compositionstart","compositionupdate","compositionend","cuechange","click","close","contextmenu","curechange","dblclick","drag","dragend","dragenter","dragexit","dragleave","dragover","drop","durationchange","emptied","ended","error","focus","focusin","focusout","gotpointercapture","input","invalid","keydown","keypress","keyup","load","loadstart","loadeddata","loadedmetadata","lostpointercapture","mousedown","mouseenter","mouseleave","mousemove","mouseout","mouseover","mouseup","mousewheel","orientationchange","pause","play","playing","pointercancel","pointerdown","pointerenter","pointerleave","pointerlockchange","mozpointerlockchange","webkitpointerlockerchange","pointerlockerror","mozpointerlockerror","webkitpointerlockerror","pointermove","pointout","pointerover","pointerup","progress","ratechange","reset","resize","scroll","seeked","seeking","select","selectionchange","selectstart","show","sort","stalled","submit","suspend","timeupdate","volumechange","touchcancel","touchmove","touchstart","touchend","transitioncancel","transitionend","waiting","wheel"].concat(["webglcontextrestored","webglcontextlost","webglcontextcreationerror"],["autocomplete","autocompleteerror"],["toggle"],["afterscriptexecute","beforescriptexecute","DOMContentLoaded","freeze","fullscreenchange","mozfullscreenchange","webkitfullscreenchange","msfullscreenchange","fullscreenerror","mozfullscreenerror","webkitfullscreenerror","msfullscreenerror","readystatechange","visibilitychange","resume"],Ve,["beforecopy","beforecut","beforepaste","copy","cut","paste","dragstart","loadend","animationstart","search","transitionrun","transitionstart","webkitanimationend","webkitanimationiteration","webkitanimationstart","webkittransitionend"],["activate","afterupdate","ariarequest","beforeactivate","beforedeactivate","beforeeditfocus","beforeupdate","cellchange","controlselect","dataavailable","datasetchanged","datasetcomplete","errorupdate","filterchange","layoutcomplete","losecapture","move","moveend","movestart","propertychange","resizeend","resizestart","rowenter","rowexit","rowsdelete","rowsinserted","command","compassneedscalibration","deactivate","help","mscontentzoom","msmanipulationstatechanged","msgesturechange","msgesturedoubletap","msgestureend","msgesturehold","msgesturestart","msgesturetap","msgotpointercapture","msinertiastart","mslostpointercapture","mspointercancel","mspointerdown","mspointerenter","mspointerhover","mspointerleave","mspointermove","mspointerout","mspointerover","mspointerup","pointerout","mssitemodejumplistitemremoved","msthumbnailclick","stop","storagecommit"]);function ot(e,n,i){if(!i||0===i.length)return n;const r=i.filter(u=>u.target===e);if(!r||0===r.length)return n;const c=r[0].ignoreProperties;return n.filter(u=>-1===c.indexOf(u))}function W(e,n,i,r){e&&Ye(e,ot(e,n,i),r)}Zone.__load_patch("util",(e,n,i)=>{i.patchOnProperties=Ye,i.patchMethod=ce,i.bindArguments=Ae,i.patchMacroTask=_t;const r=n.__symbol__("BLACK_LISTED_EVENTS"),c=n.__symbol__("UNPATCHED_EVENTS");e[c]&&(e[r]=e[c]),e[r]&&(n[r]=n[c]=e[r]),i.patchEventPrototype=yt,i.patchEventTarget=Tt,i.isIEOrEdge=mt,i.ObjectDefineProperty=he,i.ObjectGetOwnPropertyDescriptor=ue,i.ObjectCreate=Be,i.ArraySlice=ut,i.patchClass=ve,i.wrapWithCurrentZone=Le,i.filterProperties=ot,i.attachOriginToPatched=ae,i._redefineProperty=Object.defineProperty,i.patchCallbacks=gt,i.getGlobalObjects=()=>({globalSources:Ke,zoneSymbolEventNames:ee,eventNames:Te,isBrowser:je,isMix:We,isNode:Re,TRUE_STR:se,FALSE_STR:ie,ZONE_SYMBOL_PREFIX:ke,ADD_EVENT_LISTENER_STR:Se,REMOVE_EVENT_LISTENER_STR:Oe})});const Ne=x("zoneTask");function ye(e,n,i,r){let c=null,u=null;i+=r;const f={};function _(T){const m=T.data;return m.args[0]=function(){return T.invoke.apply(this,arguments)},m.handleId=c.apply(e,m.args),T}function g(T){return u.call(e,T.data.handleId)}c=ce(e,n+=r,T=>function(m,S){if("function"==typeof S[0]){const D={isPeriodic:"Interval"===r,delay:"Timeout"===r||"Interval"===r?S[1]||0:void 0,args:S},Z=S[0];S[0]=function(){try{return Z.apply(this,arguments)}finally{D.isPeriodic||("number"==typeof D.handleId?delete f[D.handleId]:D.handleId&&(D.handleId[Ne]=null))}};const B=Me(n,S[0],D,_,g);if(!B)return B;const V=B.data.handleId;return"number"==typeof V?f[V]=B:V&&(V[Ne]=B),V&&V.ref&&V.unref&&"function"==typeof V.ref&&"function"==typeof V.unref&&(B.ref=V.ref.bind(V),B.unref=V.unref.bind(V)),"number"==typeof V||V?V:B}return T.apply(e,S)}),u=ce(e,i,T=>function(m,S){const D=S[0];let Z;"number"==typeof D?Z=f[D]:(Z=D&&D[Ne],Z||(Z=D)),Z&&"string"==typeof Z.type?"notScheduled"!==Z.state&&(Z.cancelFn&&Z.data.isPeriodic||0===Z.runCount)&&("number"==typeof D?delete f[D]:D&&(D[Ne]=null),Z.zone.cancelTask(Z)):T.apply(e,S)})}Zone.__load_patch("legacy",e=>{const n=e[Zone.__symbol__("legacyPatch")];n&&n()}),Zone.__load_patch("queueMicrotask",(e,n,i)=>{i.patchMethod(e,"queueMicrotask",r=>function(c,u){n.current.scheduleMicroTask("queueMicrotask",u[0])})}),Zone.__load_patch("timers",e=>{const n="set",i="clear";ye(e,n,i,"Timeout"),ye(e,n,i,"Interval"),ye(e,n,i,"Immediate")}),Zone.__load_patch("requestAnimationFrame",e=>{ye(e,"request","cancel","AnimationFrame"),ye(e,"mozRequest","mozCancel","AnimationFrame"),ye(e,"webkitRequest","webkitCancel","AnimationFrame")}),Zone.__load_patch("blocking",(e,n)=>{const i=["alert","prompt","confirm"];for(let r=0;r<i.length;r++)ce(e,i[r],(u,f,_)=>function(g,T){return n.current.run(u,e,T,_)})}),Zone.__load_patch("EventTarget",(e,n,i)=>{(function(e,n){n.patchEventPrototype(e,n)})(e,i),function(e,n){if(Zone[n.symbol("patchEventTarget")])return;const{eventNames:i,zoneSymbolEventNames:r,TRUE_STR:c,FALSE_STR:u,ZONE_SYMBOL_PREFIX:f}=n.getGlobalObjects();for(let g=0;g<i.length;g++){const T=i[g],D=f+(T+u),Z=f+(T+c);r[T]={},r[T][u]=D,r[T][c]=Z}const _=e.EventTarget;_&&_.prototype&&n.patchEventTarget(e,[_&&_.prototype])}(e,i);const r=e.XMLHttpRequestEventTarget;r&&r.prototype&&i.patchEventTarget(e,[r.prototype])}),Zone.__load_patch("MutationObserver",(e,n,i)=>{ve("MutationObserver"),ve("WebKitMutationObserver")}),Zone.__load_patch("IntersectionObserver",(e,n,i)=>{ve("IntersectionObserver")}),Zone.__load_patch("FileReader",(e,n,i)=>{ve("FileReader")}),Zone.__load_patch("on_property",(e,n,i)=>{!function(e,n){if(Re&&!We||Zone[e.symbol("patchEvents")])return;const i="undefined"!=typeof WebSocket,r=n.__Zone_ignore_on_properties;if(je){const f=window,_=function(){try{const e=pe.navigator.userAgent;if(-1!==e.indexOf("MSIE ")||-1!==e.indexOf("Trident/"))return!0}catch(e){}return!1}()?[{target:f,ignoreProperties:["error"]}]:[];W(f,Te.concat(["messageerror"]),r&&r.concat(_),de(f)),W(Document.prototype,Te,r),void 0!==f.SVGElement&&W(f.SVGElement.prototype,Te,r),W(Element.prototype,Te,r),W(HTMLElement.prototype,Te,r),W(HTMLMediaElement.prototype,wt,r),W(HTMLFrameSetElement.prototype,Ve.concat(nt),r),W(HTMLBodyElement.prototype,Ve.concat(nt),r),W(HTMLFrameElement.prototype,tt,r),W(HTMLIFrameElement.prototype,tt,r);const g=f.HTMLMarqueeElement;g&&W(g.prototype,Dt,r);const T=f.Worker;T&&W(T.prototype,Ot,r)}const c=n.XMLHttpRequest;c&&W(c.prototype,rt,r);const u=n.XMLHttpRequestEventTarget;u&&W(u&&u.prototype,rt,r),"undefined"!=typeof IDBIndex&&(W(IDBIndex.prototype,Ee,r),W(IDBRequest.prototype,Ee,r),W(IDBOpenDBRequest.prototype,Ee,r),W(IDBDatabase.prototype,Ee,r),W(IDBTransaction.prototype,Ee,r),W(IDBCursor.prototype,Ee,r)),i&&W(WebSocket.prototype,St,r)}(i,e)}),Zone.__load_patch("customElements",(e,n,i)=>{!function(e,n){const{isBrowser:i,isMix:r}=n.getGlobalObjects();(i||r)&&e.customElements&&"customElements"in e&&n.patchCallbacks(n,e.customElements,"customElements","define",["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback"])}(e,i)}),Zone.__load_patch("XHR",(e,n)=>{!function(T){const m=T.XMLHttpRequest;if(!m)return;const S=m.prototype;let Z=S[Ze],B=S[Ie];if(!Z){const v=T.XMLHttpRequestEventTarget;if(v){const M=v.prototype;Z=M[Ze],B=M[Ie]}}const V="readystatechange",E="scheduled";function d(v){const M=v.data,R=M.target;R[u]=!1,R[_]=!1;const J=R[c];Z||(Z=R[Ze],B=R[Ie]),J&&B.call(R,V,J);const le=R[c]=()=>{if(R.readyState===R.DONE)if(!M.aborted&&R[u]&&v.state===E){const te=R[n.__symbol__("loadfalse")];if(0!==R.status&&te&&te.length>0){const re=v.invoke;v.invoke=function(){const F=R[n.__symbol__("loadfalse")];for(let I=0;I<F.length;I++)F[I]===v&&F.splice(I,1);!M.aborted&&v.state===E&&re.call(v)},te.push(v)}else v.invoke()}else!M.aborted&&!1===R[u]&&(R[_]=!0)};return Z.call(R,V,le),R[i]||(R[i]=v),A.apply(R,M.args),R[u]=!0,v}function L(){}function z(v){const M=v.data;return M.aborted=!0,Y.apply(M.target,M.args)}const j=ce(S,"open",()=>function(v,M){return v[r]=0==M[2],v[f]=M[1],j.apply(v,M)}),O=x("fetchTaskAborting"),X=x("fetchTaskScheduling"),A=ce(S,"send",()=>function(v,M){if(!0===n.current[X]||v[r])return A.apply(v,M);{const R={target:v,url:v[f],isPeriodic:!1,args:M,aborted:!1},J=Me("XMLHttpRequest.send",L,R,d,z);v&&!0===v[_]&&!R.aborted&&J.state===E&&J.invoke()}}),Y=ce(S,"abort",()=>function(v,M){const R=function(v){return v[i]}(v);if(R&&"string"==typeof R.type){if(null==R.cancelFn||R.data&&R.data.aborted)return;R.zone.cancelTask(R)}else if(!0===n.current[O])return Y.apply(v,M)})}(e);const i=x("xhrTask"),r=x("xhrSync"),c=x("xhrListener"),u=x("xhrScheduled"),f=x("xhrURL"),_=x("xhrErrorBeforeScheduled")}),Zone.__load_patch("geolocation",e=>{e.navigator&&e.navigator.geolocation&&function(e,n){const i=e.constructor.name;for(let r=0;r<n.length;r++){const c=n[r],u=e[c];if(u){if(!Fe(ue(e,c)))continue;e[c]=(_=>{const g=function(){return _.apply(this,Ae(arguments,i+"."+c))};return ae(g,_),g})(u)}}}(e.navigator.geolocation,["getCurrentPosition","watchPosition"])}),Zone.__load_patch("PromiseRejectionEvent",(e,n)=>{function i(r){return function(c){et(e,r).forEach(f=>{const _=e.PromiseRejectionEvent;if(_){const g=new _(r,{promise:c.promise,reason:c.rejection});f.invoke(g)}})}}e.PromiseRejectionEvent&&(n[x("unhandledPromiseRejectionHandler")]=i("unhandledrejection"),n[x("rejectionHandledHandler")]=i("rejectionhandled"))})},7435:(we,ue,he)=>{he(7277)}},we=>{we(we.s=7435)}]);