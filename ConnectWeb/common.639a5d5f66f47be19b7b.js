"use strict";(self.webpackChunkhealth_teams=self.webpackChunkhealth_teams||[]).push([[592],{4370:(_,o,n)=>{n.d(o,{e:()=>f});var t=n(639),d=n(9603),c=n(8583),e=n(3667);function s(a,g){1&a&&(t.TgZ(0,"div",4),t.TgZ(1,"div",5),t.Tg<PERSON>(2,"div",6),t.Tg<PERSON>(3,"div",7),t.TgZ(4,"h1",8),t._u<PERSON>(5,"Calendar"),t.qZ<PERSON>(),t.Tg<PERSON>(6,"div",9),t._u<PERSON>(7,"Tuesday August 3rd, 2021"),t.qZ<PERSON>(),t.qZ<PERSON>(),t.qZ<PERSON>(),t.TgZ(8,"div",6),t.Tg<PERSON>(9,"div",10),t.Tg<PERSON>(10,"div",11),t.Tg<PERSON>(11,"div",12),t._u<PERSON>(12,"Sun"),t.qZ<PERSON>(),t.Tg<PERSON>(13,"div",13),t._uU(14,"1"),t.qZA(),t.qZA(),t.TgZ(15,"div",11),t.TgZ(16,"div",12),t._uU(17,"Mon"),t.qZA(),t.TgZ(18,"div",13),t._uU(19,"2"),t.qZA(),t.qZA(),t.TgZ(20,"div",11),t.TgZ(21,"div",12),t._uU(22,"Tue"),t.qZA(),t.TgZ(23,"div",13),t._uU(24,"3"),t.qZA(),t.qZA(),t.TgZ(25,"div",14),t.TgZ(26,"div",12),t._uU(27,"Wed"),t.qZA(),t.TgZ(28,"div",13),t._uU(29,"4"),t.qZA(),t.qZA(),t.TgZ(30,"div",11),t.TgZ(31,"div",12),t._uU(32,"Thu"),t.qZA(),t.TgZ(33,"div",13),t._uU(34,"5"),t.qZA(),t.qZA(),t.TgZ(35,"div",11),t.TgZ(36,"div",12),t._uU(37,"Fri"),t.qZA(),t.TgZ(38,"div",13),t._uU(39,"6"),t.qZA(),t.qZA(),t.TgZ(40,"div",11),t.TgZ(41,"div",12),t._uU(42,"Sat"),t.qZA(),t.TgZ(43,"div",13),t._uU(44,"7"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(45,"div",6),t.TgZ(46,"div",15),t._UZ(47,"p-calendar",16),t.qZA(),t.qZA(),t.TgZ(48,"div",6),t.TgZ(49,"div",17),t.TgZ(50,"h2",18),t._uU(51,"Quick View"),t.qZA(),t.TgZ(52,"div",9),t._uU(53,"Tuesday August 3rd, 2021"),t.qZA(),t.qZA(),t.TgZ(54,"div",19),t.TgZ(55,"div",20),t.TgZ(56,"div",21),t.TgZ(57,"span",22),t._uU(58,"9:00"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(59,"div",20),t.TgZ(60,"div",21),t._uU(61,"Raynold Christoph"),t.qZA(),t.qZA(),t.TgZ(62,"div",20),t.TgZ(63,"div",21),t._uU(64,"Take Vitals"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(65,"div",19),t.TgZ(66,"div",20),t.TgZ(67,"div",21),t.TgZ(68,"span",22),t._uU(69,"9:00"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(70,"div",20),t.TgZ(71,"div",21),t._uU(72,"Raynold Christoph"),t.qZA(),t.qZA(),t.TgZ(73,"div",20),t.TgZ(74,"div",21),t._uU(75,"Take Vitals"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA()),2&a&&(t.xp6(47),t.Q6J("inline",!0))}function i(a,g){if(1&a){const r=t.EpF();t.TgZ(0,"i",23),t.NdJ("click",function(){return t.CHM(r),t.oxw().closeCalendar()}),t.qZA()}}function v(a,g){if(1&a){const r=t.EpF();t.TgZ(0,"i",24),t.NdJ("click",function(){return t.CHM(r),t.oxw().openCalendar()}),t.qZA()}}const Z=function(a){return{width:a}};let f=(()=>{class a{constructor(r){this.calendarService=r,this.closeCalendar=()=>{this.calendarService.triggerCalendar(!1),this.calendarService.triggerDWidth("100"),this.calendarService.triggerDPadding("20"),this.calendarService.triggerFSadding("14")},this.openCalendar=()=>{this.calendarService.triggerCalendar(!0),this.calendarService.triggerDWidth("100"),this.calendarService.triggerDPadding("20"),this.calendarService.triggerFSadding("14")}}ngOnInit(){}}return a.\u0275fac=function(r){return new(r||a)(t.Y36(d.o))},a.\u0275cmp=t.Xpm({type:a,selectors:[["app-calendar"]],decls:4,vars:6,consts:[[1,"calendar-section",3,"ngStyle"],["class","container-fluid",4,"ngIf"],["class","ri-close-line closecalendar",3,"click",4,"ngIf"],["class","ri-arrow-left-line closecalendar",3,"click",4,"ngIf"],[1,"container-fluid"],[1,"row"],[1,"col-12"],[1,"page-header"],[1,"text-light"],[1,"subheading","text-light"],[1,"weekdayslist"],[1,"weeknamecontainer"],[1,"weekname"],[1,"weekdaycount"],[1,"weeknamecontainer","activeday"],[1,"caneldar-container"],[3,"inline"],[1,"section-header"],[1,"secheading"],[1,"taskbar-container","dflexrow","justifybetween","flexwrap",2,"background","#ffffff"],[1,"tasbar-item"],[1,"itembar-text"],[1,"taskstrong"],[1,"ri-close-line","closecalendar",3,"click"],[1,"ri-arrow-left-line","closecalendar",3,"click"]],template:function(r,l){1&r&&(t.TgZ(0,"section",0),t.YNc(1,s,76,1,"div",1),t.YNc(2,i,1,0,"i",2),t.YNc(3,v,1,0,"i",3),t.qZA()),2&r&&(t.Q6J("ngStyle",t.VKq(4,Z,l.calendarService.isCalendarActive?"340px":"70px")),t.xp6(1),t.Q6J("ngIf",l.calendarService.isCalendarActive),t.xp6(1),t.Q6J("ngIf",l.calendarService.isCalendarActive),t.xp6(1),t.Q6J("ngIf",!l.calendarService.isCalendarActive))},directives:[c.PC,c.O5,e.f],styles:[".calendar-section[_ngcontent-%COMP%]{position:relative;background:#2FC4CA;border-radius:40px 0 0 40px;top:0;right:0;padding:30px 15px;height:100vh;position:fixed;z-index:1;box-shadow:#3c40434d 0 1px 2px,#3c404326 0 2px 6px 2px}@media (min-width: 300px) and (max-width: 600px){.calendar-section[_ngcontent-%COMP%]{overflow-y:scroll}}.text-light[_ngcontent-%COMP%]{color:#fff}.weekdayslist[_ngcontent-%COMP%]{margin-top:20px;display:flex;justify-content:space-between}.weeknamecontainer[_ngcontent-%COMP%]{position:relative;display:flex;flex-direction:column;align-items:center;padding:15px 5px;border-radius:25px}.activeday[_ngcontent-%COMP%]{background:#219399;color:#fff}.weekname[_ngcontent-%COMP%]{position:relative}.weekdaycount[_ngcontent-%COMP%]{color:#fff;margin-top:10px}.caneldar-container[_ngcontent-%COMP%]{margin-top:70px}.closecalendar[_ngcontent-%COMP%]{position:absolute;top:15px;right:15px;cursor:pointer;font-size:24px}.itembar-text[_ngcontent-%COMP%]{font-size:12px}"]}),a})()},4459:(_,o,n)=>{n.d(o,{O:()=>c});var t=n(6215),d=n(639);let c=(()=>{class e{constructor(){this.isAddTaskActive=!1,this.addTaskInitialState=new t.X(this.isAddTaskActive),this.addTaskChangedState=this.addTaskInitialState.asObservable(),this.triggerAddTaskRoom=i=>{this.isAddTaskActive=i,this.addtAskRoomState(this.isAddTaskActive)}}addtAskRoomState(i){return this.addTaskInitialState.next(i),this.isAddTaskActive=i,i}}return e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=d.Yz7({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},6687:(_,o,n)=>{n.d(o,{V:()=>c});var t=n(6215),d=n(639);let c=(()=>{class e{constructor(){this.isconnectFacilityActive=!1,this.addTaskInitialState=new t.X(this.isconnectFacilityActive),this.addTaskChangedState=this.addTaskInitialState.asObservable(),this.triggerConnectFacilitykRoom=i=>{this.isconnectFacilityActive=i,this.connectFacilityState(this.isconnectFacilityActive)}}connectFacilityState(i){return this.addTaskInitialState.next(i),this.isconnectFacilityActive=i,i}}return e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=d.Yz7({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},2178:(_,o,n)=>{n.d(o,{c:()=>c});var t=n(6215),d=n(639);let c=(()=>{class e{constructor(){this.isDoctorAccountFacilityActive=!1,this.facilityInfoInitialState=new t.X(this.isDoctorAccountFacilityActive),this.facilityInfoChangedState=this.facilityInfoInitialState.asObservable(),this.triggerFacilityInfo=i=>{this.isDoctorAccountFacilityActive=i,this.facilityInfoState(this.isDoctorAccountFacilityActive)}}facilityInfoState(i){return this.facilityInfoInitialState.next(i),this.isDoctorAccountFacilityActive=i,i}}return e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=d.Yz7({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},7872:(_,o,n)=>{n.d(o,{e:()=>c});var t=n(6215),d=n(639);let c=(()=>{class e{constructor(){this.isResidentInfoActive=!1,this.facilityInfoInitialState=new t.X(this.isResidentInfoActive),this.facilityInfoChangedState=this.facilityInfoInitialState.asObservable(),this.triggerFacilityInfo=i=>{this.isResidentInfoActive=i,this.facilityInfoState(this.isResidentInfoActive)}}facilityInfoState(i){return this.facilityInfoInitialState.next(i),this.isResidentInfoActive=i,i}}return e.\u0275fac=function(i){return new(i||e)},e.\u0275prov=d.Yz7({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);