"use strict";(self.webpackChunkhealth_teams=self.webpackChunkhealth_teams||[]).push([[899],{9899:(h,l,c)=>{c.r(l),c.d(l,{FacilityAuthModule:()=>v});var s=c(8583),p=c(4494),t=c(639);function g(e,n){1&e&&(t.TgZ(0,"h1"),t._uU(1,"Facility Setup"),t.qZA())}function d(e,n){1&e&&(t.TgZ(0,"h1"),t._uU(1,"Setup Doctors"),t.qZA())}function Z(e,n){if(1&e){const i=t.EpF();t.TgZ(0,"div",19),t.TgZ(1,"div",20),t.TgZ(2,"div",2),t.TgZ(3,"div",21),t.TgZ(4,"div",22),t._u<PERSON>(5,"General"),t.q<PERSON><PERSON>(),t.q<PERSON><PERSON>(),t.Tg<PERSON>(6,"div",21),t.Tg<PERSON>(7,"div",23),t.TgZ(8,"label",24),t._uU(9,"Facility Name"),t.qZA(),t._UZ(10,"input",25),t.qZA(),t.qZA(),t.TgZ(11,"div",26),t.TgZ(12,"div",23),t.TgZ(13,"label",24),t._uU(14,"Facility ID"),t.qZA(),t._UZ(15,"input",25),t.qZA(),t.qZA(),t.TgZ(16,"div",26),t.TgZ(17,"div",23),t.TgZ(18,"label",24),t._uU(19,"Beds"),t.qZA(),t._UZ(20,"input",25),t.qZA(),t.qZA(),t.TgZ(21,"div",21),t.TgZ(22,"div",23),t.TgZ(23,"label",24),t._uU(24,"Default Pharmacy"),t.qZA(),t._UZ(25,"input",25),t.qZA(),t.qZA(),t.TgZ(26,"div",21),t.TgZ(27,"div",27),t._uU(28,"Facility Manager"),t.qZA(),t.qZA(),t.TgZ(29,"div",21),t.TgZ(30,"div",23),t.TgZ(31,"label",24),t._uU(32,"Facility Manager"),t.qZA(),t._UZ(33,"input",25),t.qZA(),t.qZA(),t.TgZ(34,"div",21),t.TgZ(35,"div",23),t.TgZ(36,"label",24),t._uU(37,"Managers Email"),t.qZA(),t._UZ(38,"input",28),t.qZA(),t.qZA(),t.TgZ(39,"div",29),t.TgZ(40,"div",23),t.TgZ(41,"label",24),t._uU(42,"Country"),t.qZA(),t.TgZ(43,"select",30),t.TgZ(44,"option",31),t._UZ(45,"img",32),t.TgZ(46,"span"),t._uU(47,"Australia"),t.qZA(),t.qZA(),t.TgZ(48,"option",31),t._uU(49,"India"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(50,"div",33),t.TgZ(51,"div",23),t.TgZ(52,"label",24),t._uU(53,"Mobile Number"),t.qZA(),t._UZ(54,"input",25),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(55,"div",20),t.TgZ(56,"div",2),t.TgZ(57,"div",21),t.TgZ(58,"div",22),t._uU(59,"Address"),t.qZA(),t.qZA(),t.TgZ(60,"div",21),t.TgZ(61,"div",23),t.TgZ(62,"label",24),t._uU(63,"Street Address"),t.qZA(),t._UZ(64,"input",25),t.qZA(),t.qZA(),t.TgZ(65,"div",21),t.TgZ(66,"div",23),t.TgZ(67,"label",24),t._uU(68,"Suburb"),t.qZA(),t._UZ(69,"input",25),t.qZA(),t.qZA(),t.TgZ(70,"div",34),t.TgZ(71,"div",23),t.TgZ(72,"label",24),t._uU(73,"State"),t.qZA(),t.TgZ(74,"select",30),t.TgZ(75,"option",31),t._uU(76,"Melbourne"),t.qZA(),t.TgZ(77,"option",31),t._uU(78,"Sydney"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(79,"div",26),t.TgZ(80,"div",23),t.TgZ(81,"label",24),t._uU(82,"Post Code"),t.qZA(),t._UZ(83,"input",25),t.qZA(),t.qZA(),t.TgZ(84,"div",21),t.TgZ(85,"div",23),t.TgZ(86,"label",24),t._uU(87,"Country"),t.qZA(),t.TgZ(88,"select",30),t.TgZ(89,"option",31),t._uU(90,"Australia"),t.qZA(),t.TgZ(91,"option",31),t._uU(92,"India"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(93,"div",21),t.TgZ(94,"div",27),t._uU(95,"Parent Facility Name"),t.qZA(),t.qZA(),t.TgZ(96,"div",21),t.TgZ(97,"span",35),t._uU(98,"Is this a parent facility?"),t.qZA(),t.TgZ(99,"div",36),t.TgZ(100,"label",37),t._uU(101,"Yes "),t._UZ(102,"input",38),t._UZ(103,"span",39),t.qZA(),t.TgZ(104,"label",37),t._uU(105,"No "),t._UZ(106,"input",38),t._UZ(107,"span",39),t.qZA(),t.qZA(),t.qZA(),t.TgZ(108,"div",21),t.TgZ(109,"div",23),t.TgZ(110,"label",24),t._uU(111,"Parent Facility Name"),t.qZA(),t._UZ(112,"input",25),t.TgZ(113,"div",40),t.TgZ(114,"label",37),t._uU(115,"The River Group "),t._UZ(116,"input",41),t._UZ(117,"span",39),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(118,"div",21),t.TgZ(119,"div",42),t.TgZ(120,"button",43),t.NdJ("click",function(){return t.CHM(i),t.oxw().goToStep2()}),t._uU(121,"Next "),t._UZ(122,"i",44),t.qZA(),t.qZA(),t.qZA(),t.qZA()}}function u(e,n){1&e&&(t.TgZ(0,"div",54),t.TgZ(1,"p"),t._uU(2,"Add Doctors connected to this Facility. You can always add more later"),t.qZA(),t.qZA())}function _(e,n){1&e&&(t.TgZ(0,"div",55),t.TgZ(1,"div",56),t.TgZ(2,"div",57),t.TgZ(3,"div",58),t.TgZ(4,"div",59),t._uU(5,"Doctors Name"),t.qZA(),t.qZA(),t.TgZ(6,"div",60),t.TgZ(7,"div",59),t._uU(8,"Provider Number"),t.qZA(),t.qZA(),t.TgZ(9,"div",61),t.TgZ(10,"div",59),t._uU(11,"Specialty"),t.qZA(),t.qZA(),t.TgZ(12,"div",61),t.TgZ(13,"div",59),t._uU(14,"HealthLink EDI"),t.qZA(),t.qZA(),t.TgZ(15,"div",60),t.TgZ(16,"div",59),t._uU(17,"Phone Number"),t.qZA(),t.qZA(),t.TgZ(18,"div",58),t.TgZ(19,"div",59),t._uU(20,"Email Address"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(21,"div",56),t.TgZ(22,"div",62),t.TgZ(23,"div",58),t._UZ(24,"input",25),t.qZA(),t.TgZ(25,"div",60),t._UZ(26,"input",25),t.qZA(),t.TgZ(27,"div",61),t._UZ(28,"input",25),t.qZA(),t.TgZ(29,"div",61),t._UZ(30,"input",25),t.qZA(),t.TgZ(31,"div",60),t._UZ(32,"input",25),t.qZA(),t.TgZ(33,"div",58),t._UZ(34,"input",25),t.qZA(),t.TgZ(35,"div",61),t.TgZ(36,"div",63),t._UZ(37,"i",64),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(38,"div",56),t.TgZ(39,"div",62),t.TgZ(40,"div",58),t._UZ(41,"input",25),t.qZA(),t.TgZ(42,"div",60),t._UZ(43,"input",25),t.qZA(),t.TgZ(44,"div",61),t._UZ(45,"input",25),t.qZA(),t.TgZ(46,"div",61),t._UZ(47,"input",25),t.qZA(),t.TgZ(48,"div",60),t._UZ(49,"input",25),t.qZA(),t.TgZ(50,"div",58),t._UZ(51,"input",25),t.qZA(),t.TgZ(52,"div",61),t.TgZ(53,"div",63),t._UZ(54,"i",64),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA())}function m(e,n){if(1&e){const i=t.EpF();t.TgZ(0,"div",45),t.TgZ(1,"div",21),t.YNc(2,u,3,0,"div",46),t.qZA(),t.TgZ(3,"div",21),t.YNc(4,_,55,0,"div",47),t.TgZ(5,"div",48),t.TgZ(6,"button",49),t.NdJ("click",function(){return t.CHM(i),t.oxw().addDoctor=!0}),t._uU(7),t._UZ(8,"i",50),t.qZA(),t.qZA(),t.qZA(),t.TgZ(9,"div",51),t.TgZ(10,"div",52),t.TgZ(11,"button",43),t.NdJ("click",function(){return t.CHM(i),t.oxw().goToStep3()}),t._uU(12,"Next "),t._UZ(13,"i",44),t.qZA(),t.TgZ(14,"div",53),t.NdJ("click",function(){return t.CHM(i),t.oxw().goToStep3()}),t._uU(15,"Skip this step"),t.qZA(),t.qZA(),t.qZA(),t.qZA()}if(2&e){const i=t.oxw();t.xp6(2),t.Q6J("ngIf",!i.addDoctor),t.xp6(2),t.Q6J("ngIf",i.addDoctor),t.xp6(3),t.hij("",i.addDoctor?"Add Another":"Add Doctor"," ")}}function f(e,n){if(1&e){const i=t.EpF();t.TgZ(0,"div",65),t.TgZ(1,"div",21),t.TgZ(2,"div",66),t.TgZ(3,"div",67),t.TgZ(4,"div",68),t.TgZ(5,"h2",69),t._uU(6,"Setup Login Credentials"),t.qZA(),t.TgZ(7,"label",70),t._UZ(8,"i",71),t._UZ(9,"input",72),t.TgZ(10,"span",73),t._uU(11,"Upload Profile "),t._UZ(12,"br"),t._uU(13," Photo"),t.qZA(),t.qZA(),t.TgZ(14,"div",74),t.TgZ(15,"div",23),t.TgZ(16,"label",24),t._uU(17,"Email Address"),t.qZA(),t._UZ(18,"input",25),t.qZA(),t.TgZ(19,"div",23),t.TgZ(20,"label",24),t._uU(21,"Password"),t.qZA(),t._UZ(22,"input",75),t.qZA(),t.TgZ(23,"div",23),t.TgZ(24,"label",24),t._uU(25,"Confirm Password"),t.qZA(),t._UZ(26,"input",75),t.qZA(),t.qZA(),t.TgZ(27,"div",52),t.TgZ(28,"button",43),t.NdJ("click",function(){return t.CHM(i),t.oxw().goToStep4()}),t._uU(29,"Finish Setup "),t._UZ(30,"i",76),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA()}}function T(e,n){1&e&&(t.TgZ(0,"div",65),t.TgZ(1,"div",21),t.TgZ(2,"div",66),t.TgZ(3,"div",67),t.TgZ(4,"div",77),t.TgZ(5,"h2",78),t._uU(6,"Welcome aboard, "),t._UZ(7,"br"),t._uU(8,"Riverwood Care"),t.qZA(),t._UZ(9,"img",79),t.TgZ(10,"div",52),t.TgZ(11,"a",80),t._uU(12,"Go To Dashboard"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t._UZ(13,"img",81),t.qZA())}const a=function(e){return{"background-color":e}},A=[{path:"",redirectTo:"register",pathMatch:"full"},{path:"register",component:(()=>{class e{constructor(){this.currentStep=1,this.step1=!0,this.step2=!1,this.step3=!1,this.step4=!1,this.addDoctor=!1,this.goToStep2=()=>{this.step1=!1,this.step2=!0},this.goToStep3=()=>{this.step2=!1,this.step3=!0},this.goToStep4=()=>{this.step3=!1,this.step4=!0}}ngOnInit(){}}return e.\u0275fac=function(i){return new(i||e)},e.\u0275cmp=t.Xpm({type:e,selectors:[["app-facility-register"]],decls:36,vars:22,consts:[[1,"auth-section"],[1,"container-fluid"],[1,"row"],[1,"col-12","col-sm-3","col-md-2","col-lg-2"],[1,"auth-icon-wrap"],["src","./assets/images/logo/logo-icon.png","alt",""],[1,"auth-steper-heading","text-center"],[1,"steper-container"],[1,"step"],[1,"circle",3,"ngStyle"],[1,"step","step-active"],[1,"colplete"],[1,"col-12","col-sm-9","col-md-10","col-lg-10","pr0"],[1,"auth-form-setion"],[1,"page-header"],[4,"ngIf"],["class","row form-spacer-fm",4,"ngIf"],["class","row form-spacer-fms2",4,"ngIf"],["class","row step3-spacer-fms2",4,"ngIf"],[1,"row","form-spacer-fm"],[1,"col-12","co-ms-6","col-md-6","col-lg-6"],[1,"col-12"],[1,"formsubhead"],[1,"input-group"],[1,"label"],["type","text",1,"form-control"],[1,"col-6"],[1,"formsubhead","child-form-spacer"],["type","email",1,"form-control"],[1,"col-sm-12","col-md-12","col-lg-4"],[1,"form-control"],["value",""],["src","./assets/images/icons/australia.png","alt",""],[1,"col-sm-12","col-md-12","col-lg-8"],[1,"col-sm-12","col-md-12","col-lg-6"],[1,"questxt"],[1,"monit-check","dflexrow","justifystart","aligncenter","checkspacer"],[1,"checkbox"],["type","radio","name","Type"],[1,"checboxcheckmark"],[1,"parentfacility-check"],["type","checkbox","name","Type"],[1,"reg-btn-wrap","dflexrow","justifystart"],[1,"btn","btn-orange",3,"click"],[1,"ri-arrow-right-line"],[1,"row","form-spacer-fms2"],["class","add-doctor-txt",4,"ngIf"],["class","add-dr-list-container",4,"ngIf"],[1,"reg-btn-wrap","dflexrow","justifystart","adddrspacer"],[1,"btn","btn-brand",3,"click"],[1,"ri-add-circle-line"],[1,"col-12","adddr"],[1,"reg-btn-wrap","dflexrow","justifystart","aligncenter"],[1,"skiptxt",3,"click"],[1,"add-doctor-txt"],[1,"add-dr-list-container"],[1,"add-dr-list"],[1,"add-dr-listwrap"],[1,"w20"],[1,"item-txt"],[1,"w15"],[1,"w10"],[1,"add-dr-listwrap","add-dr-bg"],[1,"item-txt","trashdr"],[1,"ri-delete-bin-line"],[1,"row","step3-spacer-fms2"],[1,"row","justify-content-center"],[1,"col-4"],[1,"step-3-form"],[1,"step3-head"],[1,"selectPic"],[1,"ri-upload-2-line"],["type","file","size","60",1,"selectPic-input"],[1,"upld"],[1,"step-3-inputs"],["type","password",1,"form-control"],[1,"ri-check-fill"],[1,"step-4-sec"],[1,"step4-head"],["src","./assets/images/icons/check.png","alt","",1,"s4imgspcr"],["routerLink","/facility/dashboard",1,"btn","btn-orange"],["src","./assets/images/group.png","alt","",1,"finishimg"]],template:function(i,o){1&i&&(t.TgZ(0,"div",0),t.TgZ(1,"div",1),t.TgZ(2,"div",2),t.TgZ(3,"div",3),t.TgZ(4,"div",4),t._UZ(5,"img",5),t.qZA(),t.TgZ(6,"div",6),t._uU(7,"Facility Setup"),t.qZA(),t.TgZ(8,"div",7),t.TgZ(9,"div",8),t.TgZ(10,"div"),t.TgZ(11,"div",9),t._uU(12),t.qZA(),t.qZA(),t.qZA(),t.TgZ(13,"div",10),t.TgZ(14,"div"),t.TgZ(15,"div",9),t._uU(16),t.qZA(),t.qZA(),t.qZA(),t.TgZ(17,"div",8),t.TgZ(18,"div"),t.TgZ(19,"div",9),t._uU(20),t.qZA(),t.qZA(),t.qZA(),t.TgZ(21,"div",8),t.TgZ(22,"div"),t.TgZ(23,"div",9),t._uU(24),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(25,"span",11),t._uU(26,"Complete"),t.qZA(),t.qZA(),t.TgZ(27,"div",12),t.TgZ(28,"div",13),t.TgZ(29,"div",14),t.YNc(30,g,2,0,"h1",15),t.YNc(31,d,2,0,"h1",15),t.qZA(),t.YNc(32,Z,123,0,"div",16),t.YNc(33,m,16,3,"div",17),t.YNc(34,f,31,0,"div",18),t.YNc(35,T,14,0,"div",18),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA()),2&i&&(t.xp6(11),t.Q6J("ngStyle",t.VKq(14,a,o.step1?"#2FC4CA":"#E2E2E2")),t.xp6(1),t.hij(" ",o.step1?"1":""," "),t.xp6(3),t.Q6J("ngStyle",t.VKq(16,a,o.step2?"#2FC4CA":"#E2E2E2")),t.xp6(1),t.hij(" ",o.step2?"2":""," "),t.xp6(3),t.Q6J("ngStyle",t.VKq(18,a,o.step3?"#2FC4CA":"#E2E2E2")),t.xp6(1),t.hij(" ",o.step3?"3":""," "),t.xp6(3),t.Q6J("ngStyle",t.VKq(20,a,o.step4?"#2FC4CA":"#E2E2E2")),t.xp6(1),t.hij(" ",""," "),t.xp6(6),t.Q6J("ngIf",o.step1),t.xp6(1),t.Q6J("ngIf",o.step2),t.xp6(1),t.Q6J("ngIf",o.step1),t.xp6(1),t.Q6J("ngIf",o.step2),t.xp6(1),t.Q6J("ngIf",o.step3),t.xp6(1),t.Q6J("ngIf",o.step4))},directives:[s.PC,s.O5,p.yS],styles:['.auth-section[_ngcontent-%COMP%]{background:#ffffff;position:relative;width:100%}.auth-icon-wrap[_ngcontent-%COMP%]{position:relative;padding:30px;display:flex;justify-content:center;align-items:center}.auth-steper-heading[_ngcontent-%COMP%]{margin-top:15px;font-size:20px;color:#2fc4ca;font-weight:900}.auth-form-setion[_ngcontent-%COMP%]{background:#F7F7F7;padding:30px;min-height:100vh;border-radius:40px 0 0 40px}.pr0[_ngcontent-%COMP%]{padding-right:0}.formsubhead[_ngcontent-%COMP%]{font-size:16px;margin-top:20px;margin-bottom:30px;font-weight:700}.child-form-spacer[_ngcontent-%COMP%]{margin-top:10px}.form-spacer-fm[_ngcontent-%COMP%]{margin-top:20px}.questxt[_ngcontent-%COMP%]{font-size:14px}.checkbox[_ngcontent-%COMP%]{padding-left:25px}.checboxcheckmark[_ngcontent-%COMP%]{width:14px;height:14px}.checkbox[_ngcontent-%COMP%]   .checboxcheckmark[_ngcontent-%COMP%]:after{left:4px;top:2px;width:5px;height:6px}.checkspacer[_ngcontent-%COMP%]{margin:20px 0}.checkspacer[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{margin-right:30px}.parentfacility-check[_ngcontent-%COMP%]{padding:10px;background-color:#fff;font-size:14px!important}.add-doctor-txt[_ngcontent-%COMP%]{position:relative}.form-spacer-fms2[_ngcontent-%COMP%]{margin-top:40px}.adddr[_ngcontent-%COMP%]{margin-top:30px}.skiptxt[_ngcontent-%COMP%]{color:#c4c4c4;text-decoration:underline;margin-left:25px;cursor:pointer}.btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:22px}.add-dr-list-container[_ngcontent-%COMP%]{position:relative}.add-dr-list[_ngcontent-%COMP%]{width:100%;display:flex;margin-bottom:15px}.add-dr-listwrap[_ngcontent-%COMP%]{width:100%;display:flex;padding:15px}.add-dr-bg[_ngcontent-%COMP%]{background:#ffffff;border-radius:10px}.add-dr-listwrap[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:90%}.w30[_ngcontent-%COMP%]{width:30%}.w20[_ngcontent-%COMP%]{width:20%}.w15[_ngcontent-%COMP%]{width:15%}.w10[_ngcontent-%COMP%]{width:10%}.item-txt[_ngcontent-%COMP%]{font-size:12px;color:#219399;font-weight:600;margin-left:15px}.adddrspacer[_ngcontent-%COMP%]{margin-top:30px}.trashdr[_ngcontent-%COMP%]{height:100%;display:flex;align-items:center}.trashdr[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:22px;color:red;cursor:pointer}.step-3-wrapper[_ngcontent-%COMP%]{position:relative}.step-3-form[_ngcontent-%COMP%]{position:relative;display:flex;flex-direction:column;justify-content:flex-start;align-items:center}.step3-head[_ngcontent-%COMP%]{font-size:24px;font-weight:900}.selectPic[_ngcontent-%COMP%]{margin-top:30px;position:relative;background:#ffffff;height:116px;width:116px;border-radius:50%;cursor:pointer;display:flex;flex-direction:column;justify-content:center;align-items:center;border:2px solid #E2E2E2}.selectPic-input[type=file][_ngcontent-%COMP%]{display:none}.selectPic[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px}.upld[_ngcontent-%COMP%]{font-size:12px;text-align:center;margin-top:10px}.step-3-inputs[_ngcontent-%COMP%]{margin-top:30px;width:100%}.step3-spacer-fms2[_ngcontent-%COMP%]{margin-top:100px}.step-4-sec[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center}.s4imgspcr[_ngcontent-%COMP%]{margin:40px 0}.step4-head[_ngcontent-%COMP%]{font-size:24px;font-weight:900;color:#2fc4ca;text-align:center}.finishimg[_ngcontent-%COMP%]{position:absolute;bottom:0;max-width:460px;left:34%}.steper-container[_ngcontent-%COMP%]{position:relative;margin-top:30px;display:flex;flex-direction:column;align-items:center}.step[_ngcontent-%COMP%]{position:relative;min-height:1em;color:gray}.title[_ngcontent-%COMP%]{line-height:1.5em;font-weight:bold}.caption[_ngcontent-%COMP%]{font-size:.8em}.step[_ngcontent-%COMP%] + .step[_ngcontent-%COMP%]{margin-top:1.5em}.step[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:first-child{position:static;height:80px}.step[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:not(:first-child){margin-left:1.5em;padding-left:1em}.circle[_ngcontent-%COMP%]{position:relative;width:1.5em;height:1.5em;line-height:1.5em;border-radius:100%;color:#fff;text-align:center;box-shadow:0 0 0 3px #fff}.circle[_ngcontent-%COMP%]:after{content:" ";position:absolute;display:block;top:-12px;right:50%;bottom:1px;left:50%;height:180%;width:1px;transform:scaleY(2);transform-origin:50% -100%;background-color:#00000040}.step[_ngcontent-%COMP%]:last-child   .circle[_ngcontent-%COMP%]:after{display:none}.step.step-active[_ngcontent-%COMP%]{color:#2fc4ca}.step.step-active[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{background-color:#2fc4ca}.colplete[_ngcontent-%COMP%]{display:block;text-align:center;margin-top:-50px;color:#e2e2e2}']}),e})()},{path:"facility-doctor-setup",component:(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(i){return new(i||e)},e.\u0275cmp=t.Xpm({type:e,selectors:[["app-facility-doctor-setup"]],decls:2,vars:0,template:function(i,o){1&i&&(t.TgZ(0,"p"),t._uU(1,"facility-doctor-setup works!"),t.qZA())},styles:[""]}),e})()},{path:"facility-add-doctor",component:(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(i){return new(i||e)},e.\u0275cmp=t.Xpm({type:e,selectors:[["app-facility-add-doctor"]],decls:2,vars:0,template:function(i,o){1&i&&(t.TgZ(0,"p"),t._uU(1,"facility-add-doctor works!"),t.qZA())},styles:[""]}),e})()},{path:"facility-password",component:(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(i){return new(i||e)},e.\u0275cmp=t.Xpm({type:e,selectors:[["app-facility-password"]],decls:2,vars:0,template:function(i,o){1&i&&(t.TgZ(0,"p"),t._uU(1,"facility-password works!"),t.qZA())},styles:[""]}),e})()},{path:"facility-register-success",component:(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(i){return new(i||e)},e.\u0275cmp=t.Xpm({type:e,selectors:[["app-facility-register-success"]],decls:2,vars:0,template:function(i,o){1&i&&(t.TgZ(0,"p"),t._uU(1,"facility-register-success works!"),t.qZA())},styles:[""]}),e})()},{path:"facility-login",component:(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(i){return new(i||e)},e.\u0275cmp=t.Xpm({type:e,selectors:[["app-facility-login"]],decls:2,vars:0,template:function(i,o){1&i&&(t.TgZ(0,"p"),t._uU(1,"facility-login works!"),t.qZA())},styles:[""]}),e})()}];let q=(()=>{class e{}return e.\u0275fac=function(i){return new(i||e)},e.\u0275mod=t.oAB({type:e}),e.\u0275inj=t.cJS({imports:[[p.Bz.forChild(A)],p.Bz]}),e})(),v=(()=>{class e{}return e.\u0275fac=function(i){return new(i||e)},e.\u0275mod=t.oAB({type:e}),e.\u0275inj=t.cJS({imports:[[s.ez,q]]}),e})()}}]);