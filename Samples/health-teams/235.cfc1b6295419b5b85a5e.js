"use strict";(self.webpackChunkhealth_teams=self.webpackChunkhealth_teams||[]).push([[235],{235:(b,Z,c)=>{c.r(Z),c.d(Z,{PatientModule:()=>U});var p=c(8583),g=c(4494),t=c(639);let d=(()=>{class n{constructor(e){this.router=e,this.bookAppointment=()=>{this.router.navigate(["/patient/book-appointment"])},this.captureMonitoringData=()=>{this.router.navigate(["/patient/monitoring"])}}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)(t.Y36(g.F0))},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-patient-dashboard"]],decls:115,vars:0,consts:[[1,"resid-info-spacer"],[1,"row"],[1,"col-12","col-sm-12","col-md-12","col-lg-12"],[1,"section-header"],[1,"secheading"],[1,"col-md-5"],[1,"survery_grid"],[1,"item-surveys"],[1,"col-md-4"],["src","./assets/images/booking/1.png","alt","",1,"main-image"],[1,"col-md-8"],[1,"category"],["src","./assets/images/icons/calendar-2.png","alt",""],["src","./assets/images/icons/clock.png","alt",""],["src","./assets/images/icons/person.png","alt",""],["src","./assets/images/booking/2.png","alt","",1,"main-image"],[1,"faces"],["href","#"],["src","./assets/images/survey/1.png","alt",""],["src","./assets/images/survey/2.png","alt",""],["src","./assets/images/survey/3.png","alt",""],["src","./assets/images/survey/4.png","alt",""],["src","./assets/images/survey/5.png","alt",""],["href","#",1,"btn"],[1,"col-md-7"],[1,"left_block"],[1,"box-grid",3,"click"],[1,"ri-add-fill"],[1,"ri-heart-pulse-line"],[1,"right_block"],[1,"box-grid"],[1,"col-md-6"]],template:function(e,i){1&e&&(t.TgZ(0,"div",0),t.TgZ(1,"div",1),t.TgZ(2,"div",2),t.TgZ(3,"div",3),t.TgZ(4,"h2",4),t._uU(5,"Hello Janice"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(6,"div",2),t.TgZ(7,"div",1),t.TgZ(8,"div",5),t.TgZ(9,"h3"),t._uU(10,"Upcoming Appointments"),t.qZA(),t.TgZ(11,"div",6),t.TgZ(12,"div",7),t.TgZ(13,"div",1),t.TgZ(14,"div",8),t._UZ(15,"img",9),t.qZA(),t.TgZ(16,"div",10),t.TgZ(17,"h4"),t._uU(18,"Next Vital Check"),t.qZA(),t.TgZ(19,"ul",11),t.TgZ(20,"li"),t.TgZ(21,"p"),t._UZ(22,"img",12),t._uU(23," Tuesday, Aug 3rd"),t.qZA(),t.qZA(),t.TgZ(24,"li"),t.TgZ(25,"p"),t._UZ(26,"img",13),t._uU(27," 3:30am"),t.qZA(),t.qZA(),t.TgZ(28,"li"),t.TgZ(29,"p"),t._UZ(30,"img",14),t._uU(31," Doctor Mckenzie"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(32,"div",7),t.TgZ(33,"div",1),t.TgZ(34,"div",8),t._UZ(35,"img",15),t.qZA(),t.TgZ(36,"div",10),t.TgZ(37,"h4"),t._uU(38,"Next Vital Check"),t.qZA(),t.TgZ(39,"ul",11),t.TgZ(40,"li"),t.TgZ(41,"p"),t._UZ(42,"img",12),t._uU(43," Tuesday, Aug 3rd"),t.qZA(),t.qZA(),t.TgZ(44,"li"),t.TgZ(45,"p"),t._UZ(46,"img",13),t._uU(47," 9:00am"),t.qZA(),t.qZA(),t.TgZ(48,"li"),t.TgZ(49,"p"),t._UZ(50,"img",14),t._uU(51," Tristan Hunt"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(52,"div",7),t.TgZ(53,"h4"),t._uU(54,"How are you feeling today?"),t.qZA(),t.TgZ(55,"ul",16),t.TgZ(56,"li"),t.TgZ(57,"a",17),t._UZ(58,"img",18),t.qZA(),t.qZA(),t.TgZ(59,"li"),t.TgZ(60,"a",17),t._UZ(61,"img",19),t.qZA(),t.qZA(),t.TgZ(62,"li"),t.TgZ(63,"a",17),t._UZ(64,"img",20),t.qZA(),t.qZA(),t.TgZ(65,"li"),t.TgZ(66,"a",17),t._UZ(67,"img",21),t.qZA(),t.qZA(),t.TgZ(68,"li"),t.TgZ(69,"a",17),t._UZ(70,"img",22),t.qZA(),t.qZA(),t.qZA(),t.TgZ(71,"a",23),t._uU(72,"Next"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(73,"div",24),t.TgZ(74,"h3"),t._uU(75,"Quick Links"),t.qZA(),t.TgZ(76,"div",1),t.TgZ(77,"div",10),t.TgZ(78,"div",25),t.TgZ(79,"ul"),t.TgZ(80,"li"),t.TgZ(81,"div",26),t.NdJ("click",function(){return i.bookAppointment()}),t._UZ(82,"i",27),t.TgZ(83,"p"),t._uU(84,"Book Appointment"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(85,"li"),t.TgZ(86,"div",26),t.NdJ("click",function(){return i.captureMonitoringData()}),t._UZ(87,"i",28),t.TgZ(88,"p"),t._uU(89,"Capture Monitoring Data"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(90,"div",8),t.TgZ(91,"div",29),t.TgZ(92,"ul"),t.TgZ(93,"li"),t.TgZ(94,"div",30),t.TgZ(95,"p"),t._uU(96,"Call Nurse"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(97,"li"),t.TgZ(98,"div",30),t.TgZ(99,"p"),t._uU(100,"Call Guardian"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(101,"div",1),t.TgZ(102,"div",31),t.TgZ(103,"h3"),t._uU(104,"To Do"),t.qZA(),t.TgZ(105,"div",6),t.TgZ(106,"div",7),t.TgZ(107,"h4"),t._uU(108,"Scripts"),t.qZA(),t.TgZ(109,"p"),t._uU(110,"1 script needs filling"),t.qZA(),t.TgZ(111,"a",23),t._uU(112,"View Script"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(113,"div",31),t._uU(114," \xa0 "),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA())},styles:[".resid-info-spacer[_ngcontent-%COMP%]{padding:30px;width:100%}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{margin-top:0}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .secheading[_ngcontent-%COMP%]{margin-bottom:22px;font-size:26px;color:#2fc4ca}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#a2a2a2}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#2fc4ca;font-weight:600}h3[_ngcontent-%COMP%]{margin-bottom:22px;font-size:14px}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]{background:#ffffff;padding:30px;border-radius:18px;margin-bottom:15px;margin-top:10px}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#2fc4ca;margin-bottom:20px}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   .faces[_ngcontent-%COMP%]{padding-left:0;margin-bottom:0}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   .faces[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{list-style-type:none;display:inline-block;margin-right:10px}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   .faces[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:38px}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{background:#2FC4CA;width:-moz-fit-content;width:fit-content;margin-top:20px}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]{width:140px;height:140px;vertical-align:middle;position:relative;left:-41%;top:-2%}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   .category[_ngcontent-%COMP%]{padding-left:0;margin-bottom:0}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   .category[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{list-style-type:none;padding-top:7px}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   .category[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:14px;vertical-align:middle;margin-right:5px}.left_block[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{padding-left:0;margin-bottom:20px;margin-top:0}.left_block[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{list-style-type:none;display:inline-grid;width:45%;margin:0 7px 7px;text-align:center;color:#fff}.left_block[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .box-grid[_ngcontent-%COMP%]{border-radius:20px;height:180px;padding:10px;display:flex;flex-direction:column;justify-content:center;align-items:center;cursor:pointer}.left_block[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .box-grid[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{height:42px;font-weight:600}.left_block[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .box-grid[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:25px;margin-bottom:10px}.left_block[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:nth-child(1)   .box-grid[_ngcontent-%COMP%]{background:#FCBF41}.left_block[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:nth-child(2)   .box-grid[_ngcontent-%COMP%]{background:#219399}.right_block[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{padding-left:0;margin-bottom:20px;margin-top:0}.right_block[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{list-style-type:none;margin:0 0 14px;text-align:center;color:#fff}.right_block[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .box-grid[_ngcontent-%COMP%]{border-radius:20px;height:80px;display:flex;flex-direction:column;justify-content:center;align-items:center;background:#219399}.right_block[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .box-grid[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-weight:600}"]}),n})();var s=c(3667);let _=(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-patient-book-appointment"]],decls:49,vars:1,consts:[[1,"resid-info-spacer"],[1,"row","pd00"],[1,"col-12","col-sm-12","col-md-12","col-lg-12"],[1,"dflexrow","justifybetween","aligncenter","flexwrap"],[1,"page-header"],[1,"apptype"],[1,"select_grid"],[1,"row"],[1,"col-md-3"],[1,"type_wrap"],[1,"ri-checkbox-circle-fill"],["src","./assets/images/booking/1.png","alt",""],[1,"type_wrap","active"],["src","./assets/images/booking/2.png","alt",""],[1,"form-area"],[1,"col-md-6","appointmentcalendar"],[1,"calheader"],[3,"inline"],[1,"col-md-6"],["rows","12","placeholder","My hip is still hurting",1,"form-control"],[1,"col-12","btn-spacer-app"],[1,"btn","btn-brand","hderbtn","btn-sm-wrap"]],template:function(e,i){1&e&&(t.TgZ(0,"div",0),t.TgZ(1,"div",1),t.TgZ(2,"div",2),t.TgZ(3,"div",3),t.TgZ(4,"div",4),t.TgZ(5,"h1"),t._uU(6,"Book an Appointment"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(7,"h3",5),t._uU(8,"Appointment Type"),t.qZA(),t.TgZ(9,"div",6),t.TgZ(10,"div",7),t.TgZ(11,"div",8),t.TgZ(12,"div",9),t._UZ(13,"i",10),t._UZ(14,"img",11),t.TgZ(15,"p"),t._uU(16,"My Doctor"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(17,"div",8),t.TgZ(18,"div",12),t._UZ(19,"i",10),t._UZ(20,"img",11),t.TgZ(21,"p"),t._uU(22,"Facility Doctor"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(23,"div",8),t.TgZ(24,"div",9),t._UZ(25,"i",10),t._UZ(26,"img",11),t.TgZ(27,"p"),t._uU(28,"Ad Hoc Doctor"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(29,"div",8),t.TgZ(30,"div",9),t._UZ(31,"i",10),t._UZ(32,"img",13),t.TgZ(33,"p"),t._uU(34,"Vital Check"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(35,"div",2),t.TgZ(36,"div",14),t.TgZ(37,"div",7),t.TgZ(38,"div",15),t.TgZ(39,"h3",16),t._uU(40,"When would you like to book?"),t.qZA(),t._UZ(41,"p-calendar",17),t.qZA(),t.TgZ(42,"div",18),t.TgZ(43,"h3",16),t._uU(44,"Reason for Appointment"),t.qZA(),t._UZ(45,"textarea",19),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(46,"div",20),t.TgZ(47,"button",21),t._uU(48,"Request Appointment"),t.qZA(),t.qZA(),t.qZA(),t.qZA()),2&e&&(t.xp6(41),t.Q6J("inline",!0))},directives:[s.f],styles:['.resid-info-spacer[_ngcontent-%COMP%]{padding:30px;min-height:100vh;width:100%}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{margin-top:0}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .secheading[_ngcontent-%COMP%]{margin-bottom:15px;font-size:26px}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#a2a2a2}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#2fc4ca;font-weight:600}.calheader[_ngcontent-%COMP%]{font-size:18px;margin-bottom:30px}h3[_ngcontent-%COMP%]{margin-bottom:14px;font-size:14px}.form-area[_ngcontent-%COMP%]{padding-top:50px}.form-area[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border:1px solid #E2E2E2}textarea[_ngcontent-%COMP%]{font-family:"Mulish",sans-serif;padding:18px 20px;height:98px}.select_grid[_ngcontent-%COMP%]{padding-bottom:40px;padding-top:10px}.select_grid[_ngcontent-%COMP%]   .type_wrap[_ngcontent-%COMP%]{text-align:center;padding:35px 24px;border-radius:15px;cursor:pointer;position:relative;border:1px solid transparent}.select_grid[_ngcontent-%COMP%]   .type_wrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:80px;margin-bottom:8px}.select_grid[_ngcontent-%COMP%]   .type_wrap[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{display:none;position:absolute;left:10%;top:10%;font-size:22px;color:#2fc4ca}.select_grid[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{background:#ffffff;border:1px solid #2FC4CA}.select_grid[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{display:block}.pd00[_ngcontent-%COMP%]{margin:0}textarea[_ngcontent-%COMP%]{height:auto}.btn-spacer-app[_ngcontent-%COMP%]{margin-top:30px}.apptype[_ngcontent-%COMP%]{font-size:18px;margin:20px 0}']}),n})(),l=(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-patient-appointments"]],decls:98,vars:0,consts:[[1,"resid-info-spacer"],[1,"row"],[1,"col-12","col-sm-12","col-md-12","col-lg-12"],[1,"page-header"],[1,"appointment_wrap"],[1,"item-list"],["src","./assets/images/booking/2.png","alt","",1,"main-image"],["src","./assets/images/icons/calendar-2.png","alt",""],["src","./assets/images/icons/clock.png","alt",""],["src","./assets/images/icons/person.png","alt",""],["src","./assets/images/booking/1.png","alt","",1,"main-image"],["href","#",1,"btn"]],template:function(e,i){1&e&&(t.TgZ(0,"div",0),t.TgZ(1,"div",1),t.TgZ(2,"div",2),t.TgZ(3,"div",3),t.TgZ(4,"h1"),t._uU(5,"Appointments"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(6,"div",2),t.TgZ(7,"div",4),t.TgZ(8,"h3"),t._uU(9,"This Week"),t.qZA(),t.TgZ(10,"div",5),t.TgZ(11,"ul"),t.TgZ(12,"li"),t._UZ(13,"img",6),t.qZA(),t.TgZ(14,"li"),t.TgZ(15,"h4"),t._uU(16,"Vital Check"),t.qZA(),t.qZA(),t.TgZ(17,"li"),t.TgZ(18,"p"),t._UZ(19,"img",7),t._uU(20," Tuesday, Aug 3rd"),t.qZA(),t.qZA(),t.TgZ(21,"li"),t.TgZ(22,"p"),t._UZ(23,"img",8),t._uU(24," 9:00am"),t.qZA(),t.qZA(),t.TgZ(25,"li"),t.TgZ(26,"p"),t._UZ(27,"img",9),t._uU(28," Tristan Hunt"),t.qZA(),t.qZA(),t.TgZ(29,"li"),t._uU(30," \xa0 "),t.qZA(),t.qZA(),t.qZA(),t.TgZ(31,"div",5),t.TgZ(32,"ul"),t.TgZ(33,"li"),t._UZ(34,"img",10),t.qZA(),t.TgZ(35,"li"),t.TgZ(36,"h4"),t._uU(37,"Telehealth"),t.qZA(),t.qZA(),t.TgZ(38,"li"),t.TgZ(39,"p"),t._UZ(40,"img",7),t._uU(41," Tuesday, Aug 3rd"),t.qZA(),t.qZA(),t.TgZ(42,"li"),t.TgZ(43,"p"),t._UZ(44,"img",8),t._uU(45," 11:30am"),t.qZA(),t.qZA(),t.TgZ(46,"li"),t.TgZ(47,"p"),t._UZ(48,"img",9),t._uU(49," Dr Mckenzie"),t.qZA(),t.qZA(),t.TgZ(50,"li"),t.TgZ(51,"a",11),t._uU(52,"Join Telehealth"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(53,"div",4),t.TgZ(54,"h3"),t._uU(55,"Next Week"),t.qZA(),t.TgZ(56,"div",5),t.TgZ(57,"ul"),t.TgZ(58,"li"),t._UZ(59,"img",6),t.qZA(),t.TgZ(60,"li"),t.TgZ(61,"h4"),t._uU(62,"Vital Check"),t.qZA(),t.qZA(),t.TgZ(63,"li"),t.TgZ(64,"p"),t._UZ(65,"img",7),t._uU(66," Tuesday, Aug 3rd"),t.qZA(),t.qZA(),t.TgZ(67,"li"),t.TgZ(68,"p"),t._UZ(69,"img",8),t._uU(70," 9:00am"),t.qZA(),t.qZA(),t.TgZ(71,"li"),t.TgZ(72,"p"),t._UZ(73,"img",9),t._uU(74," Tristan Hunt"),t.qZA(),t.qZA(),t.TgZ(75,"li"),t._uU(76," \xa0 "),t.qZA(),t.qZA(),t.qZA(),t.TgZ(77,"div",5),t.TgZ(78,"ul"),t.TgZ(79,"li"),t._UZ(80,"img",6),t.qZA(),t.TgZ(81,"li"),t.TgZ(82,"h4"),t._uU(83,"Vital Check"),t.qZA(),t.qZA(),t.TgZ(84,"li"),t.TgZ(85,"p"),t._UZ(86,"img",7),t._uU(87," Tuesday, Aug 3rd"),t.qZA(),t.qZA(),t.TgZ(88,"li"),t.TgZ(89,"p"),t._UZ(90,"img",8),t._uU(91," 9:00am"),t.qZA(),t.qZA(),t.TgZ(92,"li"),t.TgZ(93,"p"),t._UZ(94,"img",9),t._uU(95," Tristan Hunt"),t.qZA(),t.qZA(),t.TgZ(96,"li"),t._uU(97," \xa0 "),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA())},styles:[".resid-info-spacer[_ngcontent-%COMP%]{padding:30px;width:100%}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{margin-top:0}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .secheading[_ngcontent-%COMP%]{margin-bottom:15px}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#a2a2a2}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#2fc4ca;font-weight:600}h3[_ngcontent-%COMP%]{margin-bottom:22px;font-size:14px}.appointment_wrap[_ngcontent-%COMP%]{padding-top:30px}.appointment_wrap[_ngcontent-%COMP%]   .item-list[_ngcontent-%COMP%]{background:#ffffff;padding:4px 10px;border-radius:16px;position:relative;margin-bottom:30px}.appointment_wrap[_ngcontent-%COMP%]   .item-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{padding:0;margin:0}.appointment_wrap[_ngcontent-%COMP%]   .item-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{list-style-type:none;display:inline-block;width:16%}.appointment_wrap[_ngcontent-%COMP%]   .item-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]{width:120px;height:120px;vertical-align:middle;position:relative;left:-15%}.appointment_wrap[_ngcontent-%COMP%]   .item-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#2fc4ca}.appointment_wrap[_ngcontent-%COMP%]   .item-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:14px;vertical-align:middle;margin-right:5px}.appointment_wrap[_ngcontent-%COMP%]   .item-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{background:#2FC4CA}@media (max-width: 767px){.appointment_wrap[_ngcontent-%COMP%]   .item-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{width:100%}.appointment_wrap[_ngcontent-%COMP%]   .item-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]{width:70px;height:70px}}@media (min-width: 768px) and (max-width: 991px){.appointment_wrap[_ngcontent-%COMP%]   .item-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{width:49%;margin-bottom:8px}.appointment_wrap[_ngcontent-%COMP%]   .item-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]{width:70px;height:70px}}"]}),n})(),u=(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-patient-monitoring"]],decls:202,vars:0,consts:[[1,"container-fluid","pl-0"],[1,"page-spacer"],[1,"row"],[1,"col-12"],[1,"dflexrow","justifybetween","aligncenter","flexwrap"],[1,"page-header"],[1,"subheading"],[1,"brand-text"],[1,"col-12","col-sm-12","col-md-12","col-lg-12"],[1,"data-box"],[1,"item_data_list"],["src","./assets/images/icons/bp.png","alt",""],["href","#",1,"btn-custom",2,"background","#27d108"],["src","./assets/images/icons/spo2.png","alt",""],["href","#",1,"btn-custom",2,"background","#ffa800"],["src","./assets/images/icons/temprature.png","alt",""],["src","./assets/images/icons/gluecose.png","alt",""],["href","#",1,"btn-custom",2,"background","#ff0000"],["src","./assets/images/icons/weight.png","alt",""],["src","./assets/images/icons/uti.png","alt",""],[1,"heading-space","dflexrow","justifybetween","aligncenter","flexwrap"],[1,"section-header"],[1,"secheading"],[1,"prev-data"],[1,"list_previous"],[2,"background","#27d108"],[2,"background","#ffa800"]],template:function(e,i){1&e&&(t.TgZ(0,"section"),t.TgZ(1,"div",0),t.TgZ(2,"div",1),t.TgZ(3,"div",2),t.TgZ(4,"div",3),t.TgZ(5,"div",4),t.TgZ(6,"div",5),t.TgZ(7,"h1"),t._uU(8,"Capture"),t.qZA(),t.TgZ(9,"div",6),t._uU(10,"Captured "),t.TgZ(11,"span",7),t.TgZ(12,"strong"),t._uU(13,"03/08/2021"),t.qZA(),t.qZA(),t._uU(14," at "),t.TgZ(15,"span",7),t.TgZ(16,"strong"),t._uU(17,"11:00am"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(18,"div",8),t.TgZ(19,"div",9),t.TgZ(20,"div",10),t.TgZ(21,"h3"),t._UZ(22,"img",11),t._uU(23," BP Systolic"),t.qZA(),t.TgZ(24,"a",12),t._uU(25,"110"),t.qZA(),t.TgZ(26,"p"),t._uU(27,"Upper Urgent: 180"),t.qZA(),t.TgZ(28,"p"),t._uU(29,"Lower Urgent: 60"),t.qZA(),t.qZA(),t.TgZ(30,"div",10),t.TgZ(31,"h3"),t._UZ(32,"img",11),t._uU(33," BP Diastolic"),t.qZA(),t.TgZ(34,"a",12),t._uU(35,"71"),t.qZA(),t.TgZ(36,"p"),t._uU(37,"Upper Urgent: 180"),t.qZA(),t.TgZ(38,"p"),t._uU(39,"Lower Urgent: 60"),t.qZA(),t.qZA(),t.TgZ(40,"div",10),t.TgZ(41,"h3"),t._UZ(42,"img",13),t._uU(43," SpO2"),t.qZA(),t.TgZ(44,"a",14),t._uU(45,"90"),t.qZA(),t.TgZ(46,"p"),t._uU(47,"Upper Urgent: 180"),t.qZA(),t.TgZ(48,"p"),t._uU(49,"Lower Urgent: 60"),t.qZA(),t.qZA(),t.TgZ(50,"div",10),t.TgZ(51,"h3"),t._UZ(52,"img",15),t._uU(53," Temperature"),t.qZA(),t.TgZ(54,"a",14),t._uU(55,"39"),t.qZA(),t.TgZ(56,"p"),t._uU(57,"Upper Urgent: 40"),t.qZA(),t.TgZ(58,"p"),t._uU(59,"Lower Urgent: 35"),t.qZA(),t.qZA(),t.TgZ(60,"div",10),t.TgZ(61,"h3"),t._UZ(62,"img",16),t._uU(63," Glucose level"),t.qZA(),t.TgZ(64,"a",17),t._uU(65,"200"),t.qZA(),t.TgZ(66,"p"),t._uU(67,"Upper Urgent: 180"),t.qZA(),t.TgZ(68,"p"),t._uU(69,"Lower Urgent: 60"),t.qZA(),t.qZA(),t.TgZ(70,"div",10),t.TgZ(71,"h3"),t._UZ(72,"img",18),t._uU(73," weight"),t.qZA(),t.TgZ(74,"a",12),t._uU(75,"61"),t.qZA(),t.TgZ(76,"p"),t._uU(77,"Upper Urgent: 80"),t.qZA(),t.TgZ(78,"p"),t._uU(79,"Lower Urgent: 55"),t.qZA(),t.qZA(),t.TgZ(80,"div",10),t.TgZ(81,"h3"),t._UZ(82,"img",13),t._uU(83," Heart Rate"),t.qZA(),t.TgZ(84,"a",12),t._uU(85,"80"),t.qZA(),t.TgZ(86,"p"),t._uU(87,"Upper Urgent: 120"),t.qZA(),t.TgZ(88,"p"),t._uU(89,"Lower Urgent: 50"),t.qZA(),t.qZA(),t.TgZ(90,"div",10),t.TgZ(91,"h3"),t._UZ(92,"img",19),t._uU(93," UTI"),t.qZA(),t.TgZ(94,"a",12),t._uU(95,"No"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(96,"div",3),t.TgZ(97,"div",20),t.TgZ(98,"div",21),t.TgZ(99,"h2",22),t._uU(100,"Previous Monitoring Data"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(101,"div",8),t.TgZ(102,"div",23),t.TgZ(103,"div",24),t.TgZ(104,"h3"),t._uU(105,"Latest data "),t.TgZ(106,"span"),t._uU(107,"captured 02/06/2021"),t.qZA(),t._uU(108," at "),t.TgZ(109,"span"),t._uU(110,"9:00am"),t.qZA(),t.qZA(),t.TgZ(111,"ul"),t.TgZ(112,"li"),t.TgZ(113,"h4"),t._uU(114,"BP Systolic"),t.qZA(),t.TgZ(115,"span",25),t._uU(116,"110"),t.qZA(),t.qZA(),t.TgZ(117,"li"),t.TgZ(118,"h4"),t._uU(119,"BP Diastolic"),t.qZA(),t.TgZ(120,"span",25),t._uU(121,"71"),t.qZA(),t.qZA(),t.TgZ(122,"li"),t.TgZ(123,"h4"),t._uU(124,"SpO2"),t.qZA(),t.TgZ(125,"span",26),t._uU(126,"100"),t.qZA(),t.qZA(),t.TgZ(127,"li"),t.TgZ(128,"h4"),t._uU(129,"Temp"),t.qZA(),t.TgZ(130,"span",25),t._uU(131,"37"),t.qZA(),t.qZA(),t.TgZ(132,"li"),t.TgZ(133,"h4"),t._uU(134,"Glucose Level"),t.qZA(),t.TgZ(135,"span",26),t._uU(136,"170"),t.qZA(),t.qZA(),t.TgZ(137,"li"),t.TgZ(138,"h4"),t._uU(139,"Weight"),t.qZA(),t.TgZ(140,"span",25),t._uU(141,"61"),t.qZA(),t.qZA(),t.TgZ(142,"li"),t.TgZ(143,"h4"),t._uU(144,"Heart Rate"),t.qZA(),t.TgZ(145,"span",25),t._uU(146,"82"),t.qZA(),t.qZA(),t.TgZ(147,"li"),t.TgZ(148,"h4"),t._uU(149,"UTI"),t.qZA(),t.TgZ(150,"span",25),t._uU(151,"No"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(152,"div",23),t.TgZ(153,"div",24),t.TgZ(154,"h3"),t._uU(155,"Latest data "),t.TgZ(156,"span"),t._uU(157,"captured 01/06/2021"),t.qZA(),t._uU(158," at "),t.TgZ(159,"span"),t._uU(160,"7:08am"),t.qZA(),t.qZA(),t.TgZ(161,"ul"),t.TgZ(162,"li"),t.TgZ(163,"h4"),t._uU(164,"BP Systolic"),t.qZA(),t.TgZ(165,"span",25),t._uU(166,"120"),t.qZA(),t.qZA(),t.TgZ(167,"li"),t.TgZ(168,"h4"),t._uU(169,"BP Diastolic"),t.qZA(),t.TgZ(170,"span",25),t._uU(171,"71"),t.qZA(),t.qZA(),t.TgZ(172,"li"),t.TgZ(173,"h4"),t._uU(174,"SpO2"),t.qZA(),t.TgZ(175,"span",26),t._uU(176,"80"),t.qZA(),t.qZA(),t.TgZ(177,"li"),t.TgZ(178,"h4"),t._uU(179,"Temp"),t.qZA(),t.TgZ(180,"span",25),t._uU(181,"37"),t.qZA(),t.qZA(),t.TgZ(182,"li"),t.TgZ(183,"h4"),t._uU(184,"Glucose Level"),t.qZA(),t.TgZ(185,"span",26),t._uU(186,"160"),t.qZA(),t.qZA(),t.TgZ(187,"li"),t.TgZ(188,"h4"),t._uU(189,"Weight"),t.qZA(),t.TgZ(190,"span",25),t._uU(191,"62"),t.qZA(),t.qZA(),t.TgZ(192,"li"),t.TgZ(193,"h4"),t._uU(194,"Heart Rate"),t.qZA(),t.TgZ(195,"span",25),t._uU(196,"82"),t.qZA(),t.qZA(),t.TgZ(197,"li"),t.TgZ(198,"h4"),t._uU(199,"UTI"),t.qZA(),t.TgZ(200,"span",25),t._uU(201,"No"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA())},styles:[".page-spacer[_ngcontent-%COMP%]{padding:30px 45px}.data-box[_ngcontent-%COMP%]{position:relative;width:calc(100% + 16px);left:-8px;float:left;padding:40px 0 0}.data-box[_ngcontent-%COMP%]   .item_data_list[_ngcontent-%COMP%]{padding:22px;border-radius:20px;background:#ffffff;width:calc(20% - 16px);float:left;position:relative;display:flex;flex-direction:column;transition:all .3s ease 0s;margin:8px;height:225px}.data-box[_ngcontent-%COMP%]   .item_data_list[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:14px;font-weight:800;height:42px;overflow:hidden;text-transform:capitalize}.data-box[_ngcontent-%COMP%]   .item_data_list[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{vertical-align:middle;margin-right:8px}.data-box[_ngcontent-%COMP%]   .item_data_list[_ngcontent-%COMP%]   .btn-custom[_ngcontent-%COMP%]{display:inline-block;margin:14px 0 20px;text-align:center;font-size:17px;font-weight:800;padding:14px 30px;color:#fff;cursor:pointer;border-radius:15px;border:none}.data-box[_ngcontent-%COMP%]   .item_data_list[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:4px;font-size:13px;line-height:21px}.data-box[_ngcontent-%COMP%]   .item_data_list[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child{margin-bottom:0}.prev-data[_ngcontent-%COMP%]{position:relative;padding:20px 0 10px}.prev-data[_ngcontent-%COMP%]   .list_previous[_ngcontent-%COMP%]{padding:25px;border-radius:20px;background:#ffffff;margin-bottom:20px}.prev-data[_ngcontent-%COMP%]   .list_previous[_ngcontent-%COMP%]:last-child{margin-bottom:0}.prev-data[_ngcontent-%COMP%]   .list_previous[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#a2a2a2;font-size:14px;font-weight:100;margin:0 0 25px}.prev-data[_ngcontent-%COMP%]   .list_previous[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#2fc4ca;font-weight:600}.prev-data[_ngcontent-%COMP%]   .list_previous[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{padding-left:0}.prev-data[_ngcontent-%COMP%]   .list_previous[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{list-style-type:none;display:inline-block;text-align:center;width:12.5%}.prev-data[_ngcontent-%COMP%]   .list_previous[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:14px;font-weight:800;text-transform:capitalize}.prev-data[_ngcontent-%COMP%]   .list_previous[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:inline-block;padding:10px;border-radius:15px;color:#fff;font-weight:600;margin-top:12px}.section-header[_ngcontent-%COMP%]{margin-top:40px;margin-bottom:12px}@media (max-width: 767px){.data-box[_ngcontent-%COMP%]   .item_data_list[_ngcontent-%COMP%]{width:calc(100% - 16px);padding:28px}.prev-data[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{width:49%}}@media (min-width: 768px) and (max-width: 991px){.data-box[_ngcontent-%COMP%]   .item_data_list[_ngcontent-%COMP%]{width:calc(50% - 16px);padding:28px}.prev-data[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{width:32%}}"]}),n})(),A=(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-patient-scripts"]],decls:105,vars:0,consts:[[1,"row","resid-info-spacer"],[1,"col-12","col-sm-12","col-md-12","col-lg-12"],[1,"page-header"],[1,"script-grid"],[1,"item_scpt"],["routerLink","../script-details"],[1,"section-header"],[1,"secheading"],[1,"item_scpt","disableScpt"]],template:function(e,i){1&e&&(t.TgZ(0,"div",0),t.TgZ(1,"div",1),t.TgZ(2,"div",2),t.TgZ(3,"h1"),t._uU(4,"Scripts"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(5,"div",1),t.TgZ(6,"div",3),t.TgZ(7,"div",4),t.TgZ(8,"h3"),t._uU(9,"Prescription 3947"),t.qZA(),t.TgZ(10,"h5"),t._uU(11,"Unfilled"),t.qZA(),t.TgZ(12,"h6"),t._uU(13,"Expiry 10/2021"),t.qZA(),t.TgZ(14,"p"),t._uU(15,"Roximosfan"),t.qZA(),t.TgZ(16,"p"),t._uU(17,"Dexoradisan"),t.qZA(),t.TgZ(18,"a",5),t._uU(19,"View Script"),t.qZA(),t.qZA(),t.TgZ(20,"div",4),t.TgZ(21,"h3"),t._uU(22,"Prescription 2267"),t.qZA(),t.TgZ(23,"h5"),t._uU(24,"Marked filled "),t.TgZ(25,"span"),t._uU(26,"01/08/2021"),t.qZA(),t.qZA(),t.TgZ(27,"p"),t._uU(28,"Zenofex"),t.qZA(),t.TgZ(29,"a",5),t._uU(30,"View Script"),t.qZA(),t.qZA(),t.TgZ(31,"div",4),t.TgZ(32,"h3"),t._uU(33,"Prescription 2002"),t.qZA(),t.TgZ(34,"h5"),t._uU(35,"Sent to pharmacy "),t.TgZ(36,"span"),t._uU(37,"28/07/2021"),t.qZA(),t.qZA(),t.TgZ(38,"p"),t._uU(39,"Arvadan"),t.qZA(),t.TgZ(40,"a",5),t._uU(41,"View Script"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(42,"div",1),t.TgZ(43,"div",6),t.TgZ(44,"h2",7),t._uU(45,"Past Scripts"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(46,"div",1),t.TgZ(47,"div",3),t.TgZ(48,"div",8),t.TgZ(49,"h3"),t._uU(50,"Prescription 1293"),t.qZA(),t.TgZ(51,"h5"),t._uU(52,"Filled"),t.qZA(),t.TgZ(53,"p"),t._uU(54,"Roximosfan"),t.qZA(),t.TgZ(55,"p"),t._uU(56,"Dexoradisan"),t.qZA(),t.TgZ(57,"a",5),t._uU(58,"View Script"),t.qZA(),t.qZA(),t.TgZ(59,"div",8),t.TgZ(60,"h3"),t._uU(61,"Prescription 1293"),t.qZA(),t.TgZ(62,"h5"),t._uU(63,"Expired "),t.TgZ(64,"span"),t._uU(65,"07/08/2020"),t.qZA(),t.qZA(),t.TgZ(66,"p"),t._uU(67,"Roximosfan"),t.qZA(),t.TgZ(68,"a",5),t._uU(69,"View Script"),t.qZA(),t.qZA(),t.TgZ(70,"div",8),t.TgZ(71,"h3"),t._uU(72,"Prescription 1293"),t.qZA(),t.TgZ(73,"h5"),t._uU(74,"Expired "),t.TgZ(75,"span"),t._uU(76,"07/08/2020"),t.qZA(),t.qZA(),t.TgZ(77,"p"),t._uU(78,"Roximosfan"),t.qZA(),t.TgZ(79,"p"),t._uU(80,"Dexoradisan"),t.qZA(),t.TgZ(81,"a",5),t._uU(82,"View Script"),t.qZA(),t.qZA(),t.TgZ(83,"div",8),t.TgZ(84,"h3"),t._uU(85,"Prescription 1293"),t.qZA(),t.TgZ(86,"h5"),t._uU(87,"Filled"),t.qZA(),t.TgZ(88,"p"),t._uU(89,"Roximosfan"),t.qZA(),t.TgZ(90,"p"),t._uU(91,"Dexoradisan"),t.qZA(),t.TgZ(92,"a",5),t._uU(93,"View Script"),t.qZA(),t.qZA(),t.TgZ(94,"div",8),t.TgZ(95,"h3"),t._uU(96,"Prescription 1293"),t.qZA(),t.TgZ(97,"h5"),t._uU(98,"Expired "),t.TgZ(99,"span"),t._uU(100,"07/08/2020"),t.qZA(),t.qZA(),t.TgZ(101,"p"),t._uU(102,"Roximosfan"),t.qZA(),t.TgZ(103,"a",5),t._uU(104,"View Script"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA())},directives:[g.yS],styles:[".resid-info-spacer[_ngcontent-%COMP%]{padding:30px 0 30px 30px;width:100%;min-height:100vh}.resid-info-spacer[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]{margin-bottom:20px}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{margin-top:0}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .secheading[_ngcontent-%COMP%]{margin-bottom:15px}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#a2a2a2}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#2fc4ca;font-weight:600}.script-grid[_ngcontent-%COMP%]{position:relative;padding:0 0 30px;width:calc(100% + 16px);left:-16px;float:left}.script-grid[_ngcontent-%COMP%]   .item_scpt[_ngcontent-%COMP%]{background:#ffffff;padding:30px;border-radius:20px;width:calc(31% - 16px);float:left;position:relative;display:flex;flex-direction:column;transition:all .3s ease 0s;margin:16px}.script-grid[_ngcontent-%COMP%]   .item_scpt[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:16px;font-weight:800;text-transform:capitalize}.script-grid[_ngcontent-%COMP%]   .item_scpt[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-size:14px;font-weight:700;margin:12px 0 18px;text-transform:capitalize}.script-grid[_ngcontent-%COMP%]   .item_scpt[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#2fc4ca}.script-grid[_ngcontent-%COMP%]   .item_scpt[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font-size:14px;font-weight:500;margin:0 0 20px;text-transform:capitalize}.script-grid[_ngcontent-%COMP%]   .item_scpt[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:6px;text-transform:capitalize}.script-grid[_ngcontent-%COMP%]   .item_scpt[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{background:#2FC4CA;display:inline-block;margin:14px 0 0;text-align:center;font-size:14px;padding:10px 26px;color:#fff;cursor:pointer;border-radius:15px;border:none;width:-moz-fit-content;width:fit-content}.script-grid[_ngcontent-%COMP%]   .disableScpt[_ngcontent-%COMP%]{opacity:.6}.script-grid[_ngcontent-%COMP%]   .disableScpt[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-weight:500}.script-grid[_ngcontent-%COMP%]   .disableScpt[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#000}@media (max-width: 767px){.script-grid[_ngcontent-%COMP%]   .item_scpt[_ngcontent-%COMP%]{width:calc(100% - 16px)}}@media (min-width: 768px) and (max-width: 991px){.script-grid[_ngcontent-%COMP%]   .item_scpt[_ngcontent-%COMP%]{width:calc(48% - 16px)}}"]}),n})(),T=(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-script-details"]],decls:100,vars:0,consts:[[1,"resid-info-spacer"],[1,"light-grid"],[1,"row"],[1,"col-12","col-sm-12","col-md-12","col-lg-12"],[1,"section-header"],["src","./assets/images/HT-Logo.png","alt",""],[1,"secheading"],[1,"script-individual"],[1,"col-12","col-md-6"],[1,"col-12","col-md-3"],[1,"table-responsive"],[1,"table"],[1,"sign_area"],[1,"col-md-2"],["src","./assets/images/signature.png","alt",""],[1,"col-md-10"],[1,"foot-button"],["href","javascript:;",1,"btn"]],template:function(e,i){1&e&&(t.TgZ(0,"div",0),t.TgZ(1,"div",1),t.TgZ(2,"div",2),t.TgZ(3,"div",3),t.TgZ(4,"div",4),t._UZ(5,"img",5),t.TgZ(6,"h2",6),t._uU(7,"Prescription 3947"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(8,"div",3),t.TgZ(9,"div",7),t.TgZ(10,"div",2),t.TgZ(11,"div",8),t.TgZ(12,"h4"),t._uU(13,"Name"),t.qZA(),t.TgZ(14,"p"),t._uU(15,"Janice Antoinette Bauer"),t.qZA(),t.qZA(),t.TgZ(16,"div",9),t.TgZ(17,"h4"),t._uU(18,"Date"),t.qZA(),t.TgZ(19,"p"),t._uU(20,"03/02/2021"),t.qZA(),t.qZA(),t.TgZ(21,"div",9),t.TgZ(22,"h4"),t._uU(23,"Expiry"),t.qZA(),t.TgZ(24,"p"),t._uU(25,"03/08/2021"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(26,"div",3),t.TgZ(27,"div",10),t.TgZ(28,"table",11),t.TgZ(29,"thead"),t.TgZ(30,"tr"),t.TgZ(31,"th"),t._uU(32,"Items"),t.qZA(),t.TgZ(33,"th"),t._uU(34,"Strength"),t.qZA(),t.TgZ(35,"th"),t._uU(36,"Frequency"),t.qZA(),t.TgZ(37,"th"),t._uU(38,"Duration"),t.qZA(),t.TgZ(39,"th"),t._uU(40,"Repeats"),t.qZA(),t.TgZ(41,"th"),t._uU(42,"Instruction"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(43,"tbody"),t.TgZ(44,"tr"),t.TgZ(45,"td"),t._uU(46,"Roximosfan"),t.qZA(),t.TgZ(47,"td"),t._uU(48,"50ug"),t.qZA(),t.TgZ(49,"td"),t._uU(50,"Daily"),t.qZA(),t.TgZ(51,"td"),t._uU(52,"1 Week"),t.qZA(),t.TgZ(53,"td"),t._uU(54,"1"),t.qZA(),t.TgZ(55,"td"),t._uU(56,"With Meals"),t.qZA(),t.qZA(),t.TgZ(57,"tr"),t.TgZ(58,"td"),t._uU(59,"Dexoradisan"),t.qZA(),t.TgZ(60,"td"),t._uU(61,"100ug"),t.qZA(),t.TgZ(62,"td"),t._uU(63,"Twice Daily"),t.qZA(),t.TgZ(64,"td"),t._uU(65,"1 Week"),t.qZA(),t.TgZ(66,"td"),t._uU(67,"0"),t.qZA(),t._UZ(68,"td"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(69,"div",3),t.TgZ(70,"div",12),t.TgZ(71,"div",2),t.TgZ(72,"div",13),t._UZ(73,"img",14),t.qZA(),t.TgZ(74,"div",15),t.TgZ(75,"h4"),t._uU(76,"Dr Andrea Mckenzie"),t.qZA(),t.TgZ(77,"p"),t._uU(78,"1 Address Rd,"),t.qZA(),t.TgZ(79,"p"),t._uU(80,"Sydney NSW 2000"),t.qZA(),t.TgZ(81,"p"),t._uU(82,"0411 222 333"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(83,"div",3),t.TgZ(84,"p"),t._uU(85,"Privacy notice goes here Privacy notice goes here Privacy notice goes here Privacy notice goes here Privacy notice goes here Privacy notice goes here Privacy notice goes here Privacy notice goes here Privacy notice goes here Privacy notice goes here Privacy notice goes here Privacy notice goes here Privacy notice goes here Privacy notice goes here Privacy notice goes here Privacy notice goes here "),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(86,"div",16),t.TgZ(87,"ul"),t.TgZ(88,"li"),t.TgZ(89,"a",17),t._uU(90,"Send to Pharmacy"),t.qZA(),t.qZA(),t.TgZ(91,"li"),t.TgZ(92,"a",17),t._uU(93,"Order Refill"),t.qZA(),t.qZA(),t.TgZ(94,"li"),t.TgZ(95,"a",17),t._uU(96,"Mark Script Filled"),t.qZA(),t.qZA(),t.TgZ(97,"li"),t.TgZ(98,"a",17),t._uU(99,"Mark Script Filled"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA())},styles:[".resid-info-spacer[_ngcontent-%COMP%]{padding:30px;width:100%}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{margin-top:0}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:120px;margin-bottom:10px}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .secheading[_ngcontent-%COMP%]{margin-bottom:15px;font-size:17px;font-weight:800}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#000}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#2fc4ca;font-weight:600}.light-grid[_ngcontent-%COMP%]{background:#ffffff;padding:30px;border-radius:10px;margin-bottom:30px}.script-individual[_ngcontent-%COMP%]{margin:20px 0 50px}.script-individual[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin-bottom:8px;font-weight:800}.table[_ngcontent-%COMP%]{width:100%;max-width:100%;background-color:transparent;margin:20px 0}table[_ngcontent-%COMP%]{border-collapse:collapse}table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{font-weight:800;text-align:left;padding:10px 0}table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{text-align:left;padding:7px 0}.sign_area[_ngcontent-%COMP%]{position:relative;max-width:900px;width:100%;margin:60px 0}.sign_area[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;width:150px}.sign_area[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin-bottom:10px}.foot-button[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{padding-left:0;width:100%}.foot-button[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{list-style-type:none;display:inline-block}.foot-button[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{background:#2FC4CA}.foot-button[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:nth-child(2){margin-left:10px}.foot-button[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:nth-child(3){margin-left:10px}.foot-button[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:nth-child(4){float:right}"]}),n})(),m=(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-patient-referrals"]],decls:45,vars:0,consts:[[1,"resid-info-spacer"],[1,"row"],[1,"col-12","col-sm-12","col-md-12","col-lg-12"],[1,"page-header"],[1,"table-responsive"],[1,"table"],["routerLink","../referral-details",1,"btn"]],template:function(e,i){1&e&&(t.TgZ(0,"div",0),t.TgZ(1,"div",1),t.TgZ(2,"div",2),t.TgZ(3,"div",3),t.TgZ(4,"h1"),t._uU(5,"Referrals"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(6,"div",2),t.TgZ(7,"div",4),t.TgZ(8,"table",5),t.TgZ(9,"thead"),t.TgZ(10,"tr"),t.TgZ(11,"th"),t._uU(12,"Type"),t.qZA(),t.TgZ(13,"th"),t._uU(14,"Date"),t.qZA(),t.TgZ(15,"th"),t._uU(16,"Referred To"),t.qZA(),t.TgZ(17,"th"),t._uU(18,"Status"),t.qZA(),t._UZ(19,"th"),t.qZA(),t.qZA(),t.TgZ(20,"tbody"),t.TgZ(21,"tr"),t.TgZ(22,"td"),t._uU(23,"Geriatrician Referral"),t.qZA(),t.TgZ(24,"td"),t._uU(25,"06/08/2021"),t.qZA(),t.TgZ(26,"td"),t._uU(27,"Dr Joe Grange"),t.qZA(),t.TgZ(28,"td"),t._uU(29,"Sent"),t.qZA(),t.TgZ(30,"td"),t.TgZ(31,"a",6),t._uU(32,"View Referral"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(33,"tr"),t.TgZ(34,"td"),t._uU(35,"Radiology Referral"),t.qZA(),t.TgZ(36,"td"),t._uU(37,"06/08/2021"),t.qZA(),t.TgZ(38,"td"),t._uU(39,"Marview Hospital"),t.qZA(),t.TgZ(40,"td"),t._uU(41,"Marview Hospital"),t.qZA(),t.TgZ(42,"td"),t.TgZ(43,"a",6),t._uU(44,"View Referral"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA())},directives:[g.yS],styles:[".resid-info-spacer[_ngcontent-%COMP%]{padding:30px;width:100%;min-height:100vh}.resid-info-spacer[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]{margin-bottom:20px}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{margin-top:0}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .secheading[_ngcontent-%COMP%]{margin-bottom:15px}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#a2a2a2}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#2fc4ca;font-weight:600}table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{padding:15px 10px;color:#2fc4ca;font-weight:600;text-align:left}table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:last-child{width:20%}table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{background:#ffffff;border-bottom:6px solid #F7F7F7;padding:10px}table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child{font-weight:700;border-radius:16px 0 0 16px;padding-left:12px}table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:last-child{border-radius:0 16px 16px 0;padding-right:12px}table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{background:#2FC4CA}"]}),n})(),P=(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-patient-survey"]],decls:67,vars:0,consts:[[1,"resid-info-spacer"],[1,"row"],[1,"col-12","col-sm-12","col-md-12","col-lg-12"],[1,"page-header"],[1,"survery_grid"],[1,"item-surveys"],[1,"faces"],["href","#"],["src","./assets/images/survey/1.png","alt",""],["src","./assets/images/survey/2.png","alt",""],["src","./assets/images/survey/3.png","alt",""],["src","./assets/images/survey/4.png","alt",""],["src","./assets/images/survey/5.png","alt",""],[1,"select-btn"],["href","#",1,"btn","btn-green"],["href","#",1,"btn","btn-red"],[1,"col-12","btn-spacer-app"],["routerLink","../thank-you",1,"btn","btn-brand"]],template:function(e,i){1&e&&(t.TgZ(0,"div",0),t.TgZ(1,"div",1),t.TgZ(2,"div",2),t.TgZ(3,"div",3),t.TgZ(4,"h1"),t._uU(5,"Qualitative Survey"),t.qZA(),t.TgZ(6,"p"),t._uU(7,"Answering on "),t.TgZ(8,"span"),t._uU(9,"03/08/2021"),t.qZA(),t._uU(10," at "),t.TgZ(11,"span"),t._uU(12,"11:00am"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(13,"div",2),t.TgZ(14,"div",4),t.TgZ(15,"div",5),t.TgZ(16,"h4"),t._uU(17,"How are you feeling today?"),t.qZA(),t.TgZ(18,"ul",6),t.TgZ(19,"li"),t.TgZ(20,"a",7),t._UZ(21,"img",8),t.qZA(),t.qZA(),t.TgZ(22,"li"),t.TgZ(23,"a",7),t._UZ(24,"img",9),t.qZA(),t.qZA(),t.TgZ(25,"li"),t.TgZ(26,"a",7),t._UZ(27,"img",10),t.qZA(),t.qZA(),t.TgZ(28,"li"),t.TgZ(29,"a",7),t._UZ(30,"img",11),t.qZA(),t.qZA(),t.TgZ(31,"li"),t.TgZ(32,"a",7),t._UZ(33,"img",12),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(34,"div",5),t.TgZ(35,"h4"),t._uU(36,"How are you feeling today?"),t.qZA(),t.TgZ(37,"ul",13),t.TgZ(38,"li"),t.TgZ(39,"a",14),t._uU(40,"Yes"),t.qZA(),t.qZA(),t.TgZ(41,"li"),t.TgZ(42,"a",15),t._uU(43,"No"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(44,"div",5),t.TgZ(45,"h4"),t._uU(46,"Are you able to eat today?"),t.qZA(),t.TgZ(47,"ul",13),t.TgZ(48,"li"),t.TgZ(49,"a",14),t._uU(50,"Yes"),t.qZA(),t.qZA(),t.TgZ(51,"li"),t.TgZ(52,"a",15),t._uU(53,"No"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(54,"div",5),t.TgZ(55,"h4"),t._uU(56,"Have you taken your medications today?"),t.qZA(),t.TgZ(57,"ul",13),t.TgZ(58,"li"),t.TgZ(59,"a",14),t._uU(60,"Yes"),t.qZA(),t.qZA(),t.TgZ(61,"li"),t.TgZ(62,"a",15),t._uU(63,"No"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(64,"div",16),t.TgZ(65,"a",17),t._uU(66,"Submit Survey"),t.qZA(),t.qZA(),t.qZA(),t.qZA())},directives:[g.yS],styles:[".resid-info-spacer[_ngcontent-%COMP%]{padding:30px;width:100%;min-height:100vh}.resid-info-spacer[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]{margin-bottom:20px}.resid-info-spacer[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:20px;color:#000}.resid-info-spacer[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#2fc4ca;font-weight:600}.survery_grid[_ngcontent-%COMP%]{max-width:360px}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]{background:#ffffff;padding:30px;border-radius:18px;margin-bottom:15px;margin-top:10px}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#2fc4ca;margin-bottom:20px}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   .faces[_ngcontent-%COMP%]{padding-left:0;margin-bottom:0}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   .faces[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{list-style-type:none;display:inline-block;margin-right:10px}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   .faces[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:38px}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   .select-btn[_ngcontent-%COMP%]{padding-left:0;margin-bottom:0}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   .select-btn[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{list-style-type:none;display:inline-block;margin-right:10px}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   .select-btn[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{height:38px;border-radius:8px}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   .select-btn[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .btn-green[_ngcontent-%COMP%]{background:#91D700}.survery_grid[_ngcontent-%COMP%]   .item-surveys[_ngcontent-%COMP%]   .select-btn[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .btn-red[_ngcontent-%COMP%]{background:#FF0000}.btn-spacer-app[_ngcontent-%COMP%]{margin-top:30px}.btn-spacer-app[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{display:inline-flex}"]}),n})();const q=function(){return["./account"]},r=function(){return["activeTab"]},a=function(){return{exact:!0}},C=function(){return["./monitoring-setup"]},M=function(){return["./consent"]},h=function(){return["./password"]},O=[{path:"",redirectTo:"dashboard",pathMatch:"full"},{path:"dashboard",component:d},{path:"book-appointment",component:_},{path:"appointments",component:l},{path:"monitoring",component:u},{path:"scripts",component:A},{path:"script-details",component:T},{path:"referrals",component:m},{path:"referral-details",component:(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-patient-referral-details"]],decls:78,vars:0,consts:[[1,"resid-info-spacer"],[1,"light-grid"],[1,"row"],[1,"col-12","col-sm-12","col-md-12","col-lg-12"],[1,"section-header"],["src","./assets/images/HT-Logo.png","alt",""],[1,"secheading"],[1,"script-individual"],[1,"col-12","col-md-4"],[1,"sign_area"],[1,"col-md-2"],["src","./assets/images/signature.png","alt",""],[1,"col-md-10"],[1,"foot-button"],["href","#",1,"btn"]],template:function(e,i){1&e&&(t.TgZ(0,"div",0),t.TgZ(1,"div",1),t.TgZ(2,"div",2),t.TgZ(3,"div",3),t.TgZ(4,"div",4),t._UZ(5,"img",5),t.TgZ(6,"h2",6),t._uU(7,"Geriatrician Referral"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(8,"div",3),t.TgZ(9,"div",7),t.TgZ(10,"div",2),t.TgZ(11,"div",8),t.TgZ(12,"h4"),t._uU(13,"To"),t.qZA(),t.TgZ(14,"p"),t._uU(15,"Dr Joe Grange"),t.qZA(),t.TgZ(16,"p"),t._uU(17,"Geriatric Australia 1 This Street, Sydney, NSW 2000"),t.qZA(),t.TgZ(18,"p"),t.TgZ(19,"strong"),t._uU(20,"Provider #"),t.qZA(),t._uU(21," **********"),t.qZA(),t.TgZ(22,"p"),t.TgZ(23,"strong"),t._uU(24,"HealthLink EDI"),t.qZA(),t._uU(25," 88920033"),t.qZA(),t.qZA(),t.TgZ(26,"div",8),t.TgZ(27,"h4"),t._uU(28,"For"),t.qZA(),t.TgZ(29,"p"),t._uU(30,"Janice Antoinette Bauer"),t.qZA(),t._UZ(31,"p"),t.TgZ(32,"p"),t._uU(33," Riverwood Care Unit 06, 123 Oswold Avenue Sydney, NSW 2000"),t.qZA(),t.qZA(),t.TgZ(34,"div",8),t.TgZ(35,"p"),t.TgZ(36,"strong"),t._uU(37,"Date"),t.qZA(),t._uU(38," 06/08/2021"),t.qZA(),t.TgZ(39,"p"),t.TgZ(40,"strong"),t._uU(41,"Medicare #"),t.qZA(),t._uU(42," 2255 6666 9999"),t.qZA(),t.TgZ(43,"p"),t.TgZ(44,"strong"),t._uU(45,"Position"),t.qZA(),t._uU(46," 1"),t.qZA(),t.TgZ(47,"p"),t.TgZ(48,"strong"),t._uU(49,"Expiry"),t.qZA(),t._uU(50," 03/2022"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(51,"div",3),t.TgZ(52,"p"),t._uU(53,"Some referral information here Some referral information here Some referral information here Some referral inform Some referral information here Some referral information here Some referral information here"),t.qZA(),t.TgZ(54,"p"),t._uU(55,"Some referral information here Some referral information here Some referral information here Some referral"),t.qZA(),t.qZA(),t.TgZ(56,"div",3),t.TgZ(57,"div",9),t.TgZ(58,"div",2),t.TgZ(59,"div",10),t._UZ(60,"img",11),t.qZA(),t.TgZ(61,"div",12),t.TgZ(62,"h4"),t._uU(63,"Dr Andrea Mckenzie"),t.qZA(),t.TgZ(64,"p"),t._uU(65,"1 Address Rd,"),t.qZA(),t.TgZ(66,"p"),t._uU(67,"Sydney NSW 2000"),t.qZA(),t.TgZ(68,"p"),t._uU(69,"0411 222 333"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(70,"div",13),t.TgZ(71,"ul"),t.TgZ(72,"li"),t.TgZ(73,"a",14),t._uU(74,"Send to Specialist"),t.qZA(),t.qZA(),t.TgZ(75,"li"),t.TgZ(76,"a",14),t._uU(77,"Print"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA())},styles:[".resid-info-spacer[_ngcontent-%COMP%]{padding:30px;width:100%}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{margin-top:0}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:120px;margin-bottom:10px}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .secheading[_ngcontent-%COMP%]{margin-bottom:15px;font-size:24px;font-weight:800}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#000}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#2fc4ca;font-weight:600}.light-grid[_ngcontent-%COMP%]{background:#ffffff;padding:30px;border-radius:10px;margin-bottom:30px}.script-individual[_ngcontent-%COMP%]{margin:20px 0 60px}.script-individual[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin-bottom:8px;font-weight:800}.sign_area[_ngcontent-%COMP%]{position:relative;max-width:900px;width:100%;margin:60px 0}.sign_area[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;width:150px}.sign_area[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin-bottom:10px}.foot-button[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{padding-left:0;width:100%}.foot-button[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{list-style-type:none;display:inline-block}.foot-button[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{background:#2FC4CA}.foot-button[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:nth-child(2){margin-left:10px}.foot-button[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:nth-child(3){float:right}p[_ngcontent-%COMP%]{margin-bottom:8px}"]}),n})()},{path:"survey",component:P},{path:"thank-you",component:(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-patient-survey-thank-you"]],decls:9,vars:0,consts:[[1,"survey-container"],[1,"row","rowpd0","srh100"],[1,"col-12","col-sm-6","srh100"],[1,"step-4-sec"],[1,"step4-head"],["src","./assets/images/thankyou.png","alt",""]],template:function(e,i){1&e&&(t.TgZ(0,"section",0),t.TgZ(1,"div",1),t.TgZ(2,"div",2),t.TgZ(3,"div",3),t.TgZ(4,"h2",4),t._uU(5,"Thank you, Janice "),t._UZ(6,"br"),t._uU(7,"Your responses have been recorded"),t.qZA(),t._UZ(8,"img",5),t.qZA(),t.qZA(),t.qZA(),t.qZA())},styles:['.auth-section[_ngcontent-%COMP%]{background:#ffffff;position:relative;width:100%}.auth-icon-wrap[_ngcontent-%COMP%]{position:relative;padding:30px;display:flex;justify-content:center;align-items:center}.auth-steper-heading[_ngcontent-%COMP%]{margin-top:15px;font-size:20px;color:#2fc4ca;font-weight:900}.auth-form-setion[_ngcontent-%COMP%]{background:#F7F7F7;padding:30px;min-height:100vh;border-radius:40px 0 0 40px}.pr0[_ngcontent-%COMP%]{padding-right:0}.formsubhead[_ngcontent-%COMP%]{font-size:16px;margin-top:20px;margin-bottom:30px;font-weight:700}.child-form-spacer[_ngcontent-%COMP%]{margin-top:10px}.form-spacer-fm[_ngcontent-%COMP%]{margin-top:20px}.questxt[_ngcontent-%COMP%]{font-size:14px}.checkbox[_ngcontent-%COMP%]{padding-left:25px}.checboxcheckmark[_ngcontent-%COMP%]{width:14px;height:14px}.checkbox[_ngcontent-%COMP%]   .checboxcheckmark[_ngcontent-%COMP%]:after{left:4px;top:2px;width:5px;height:6px}.checkspacer[_ngcontent-%COMP%]{margin:20px 0}.checkspacer[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{margin-right:30px}.parentfacility-check[_ngcontent-%COMP%]{padding:10px;background-color:#fff;font-size:14px!important}.add-doctor-txt[_ngcontent-%COMP%]{position:relative}.form-spacer-fms2[_ngcontent-%COMP%]{margin-top:40px}.adddr[_ngcontent-%COMP%]{margin-top:30px}.skiptxt[_ngcontent-%COMP%]{color:#c4c4c4;text-decoration:underline;margin-left:25px;cursor:pointer}.btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:22px}.add-dr-list-container[_ngcontent-%COMP%]{position:relative}.add-dr-list[_ngcontent-%COMP%]{width:100%;display:flex;margin-bottom:15px}.add-dr-listwrap[_ngcontent-%COMP%]{width:100%;display:flex;padding:15px}.add-dr-bg[_ngcontent-%COMP%]{background:#ffffff;border-radius:10px}.add-dr-listwrap[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:90%}.w30[_ngcontent-%COMP%]{width:30%}.w20[_ngcontent-%COMP%]{width:20%}.w15[_ngcontent-%COMP%]{width:15%}.w10[_ngcontent-%COMP%]{width:10%}.item-txt[_ngcontent-%COMP%]{font-size:12px;color:#219399;font-weight:600;margin-left:15px}.adddrspacer[_ngcontent-%COMP%]{margin-top:30px}.trashdr[_ngcontent-%COMP%]{height:100%;display:flex;align-items:center}.trashdr[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:22px;color:red;cursor:pointer}.step-3-wrapper[_ngcontent-%COMP%]{position:relative}.step-3-form[_ngcontent-%COMP%]{position:relative;display:flex;flex-direction:column;justify-content:flex-start;align-items:center}.step3-head[_ngcontent-%COMP%]{font-size:24px;font-weight:900}.selectPic[_ngcontent-%COMP%]{margin-top:30px;position:relative;background:#ffffff;height:116px;width:116px;border-radius:50%;cursor:pointer;display:flex;flex-direction:column;justify-content:center;align-items:center;border:2px solid #E2E2E2}.selectPic-input[type=file][_ngcontent-%COMP%]{display:none}.selectPic[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px}.upld[_ngcontent-%COMP%]{font-size:12px;text-align:center;margin-top:10px}.step-3-inputs[_ngcontent-%COMP%]{margin-top:30px;width:100%}.step3-spacer-fms2[_ngcontent-%COMP%]{margin-top:100px}.step-4-sec[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center;padding-top:35%}.s4imgspcr[_ngcontent-%COMP%]{margin:40px 0}.step4-head[_ngcontent-%COMP%]{font-size:24px;font-weight:900;color:#2fc4ca;text-align:center}.finishimg[_ngcontent-%COMP%]{position:absolute;bottom:0;max-width:600px;left:30%}.steper-container[_ngcontent-%COMP%]{position:relative;margin-top:30px;display:flex;flex-direction:column;align-items:center}.step[_ngcontent-%COMP%]{position:relative;min-height:1em;color:gray}.title[_ngcontent-%COMP%]{line-height:1.5em;font-weight:bold}.caption[_ngcontent-%COMP%]{font-size:.8em}.step[_ngcontent-%COMP%] + .step[_ngcontent-%COMP%]{margin-top:1.5em}.step[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:first-child{position:static;height:80px}.step[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:not(:first-child){margin-left:1.5em;padding-left:1em}.circle[_ngcontent-%COMP%]{position:relative;width:1.5em;height:1.5em;line-height:1.5em;border-radius:100%;color:#fff;text-align:center;box-shadow:0 0 0 3px #fff}.circle[_ngcontent-%COMP%]:after{content:" ";position:absolute;display:block;top:-12px;right:50%;bottom:1px;left:50%;height:180%;width:1px;transform:scaleY(2);transform-origin:50% -100%;background-color:#00000040}.step[_ngcontent-%COMP%]:last-child   .circle[_ngcontent-%COMP%]:after{display:none}.step.step-active[_ngcontent-%COMP%]{color:#2fc4ca}.step.step-active[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{background-color:#2fc4ca}.srh100[_ngcontent-%COMP%]{height:100vh}.survey-container[_ngcontent-%COMP%]{min-height:100vh}.rowpd0[_ngcontent-%COMP%]{margin:0;flex-direction:column;justify-content:center;align-items:center}.step-4-sec[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;margin-top:50px}']}),n})()},{path:"setting",component:(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-patient-account"]],decls:21,vars:24,consts:[[1,"resd-bg"],[1,"row","w100"],[1,"resd-width","w100"],[1,"row","ml-0","mr-0","w100"],[1,"col-12"],[1,"heading-space","page-spacer","dflexrow","justifybetween","aligncenter","flexwrap"],[1,"page-header","trim-resd-space"],[1,"col-12","pl-0","pr-0"],[1,"tab-container","tab-wrapper","dflexrow","aligncenter"],[1,"tab-item",3,"routerLink","routerLinkActive","routerLinkActiveOptions"],[1,"row","resid-info-spacer"]],template:function(e,i){1&e&&(t.TgZ(0,"section",0),t.TgZ(1,"div",1),t.TgZ(2,"section",2),t.TgZ(3,"div",3),t.TgZ(4,"div",4),t.TgZ(5,"div",5),t.TgZ(6,"div",6),t.TgZ(7,"h1"),t._uU(8,"Account"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(9,"div",7),t.TgZ(10,"div",8),t.TgZ(11,"a",9),t._uU(12,"Account"),t.qZA(),t.TgZ(13,"a",9),t._uU(14,"Monitoring Setup"),t.qZA(),t.TgZ(15,"a",9),t._uU(16,"Consent"),t.qZA(),t.TgZ(17,"a",9),t._uU(18,"Password"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(19,"div",10),t._UZ(20,"router-outlet"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA()),2&e&&(t.xp6(11),t.Q6J("routerLink",t.DdM(12,q))("routerLinkActive",t.DdM(13,r))("routerLinkActiveOptions",t.DdM(14,a)),t.xp6(2),t.Q6J("routerLink",t.DdM(15,C))("routerLinkActive",t.DdM(16,r))("routerLinkActiveOptions",t.DdM(17,a)),t.xp6(2),t.Q6J("routerLink",t.DdM(18,M))("routerLinkActive",t.DdM(19,r))("routerLinkActiveOptions",t.DdM(20,a)),t.xp6(2),t.Q6J("routerLink",t.DdM(21,h))("routerLinkActive",t.DdM(22,r))("routerLinkActiveOptions",t.DdM(23,a)))},directives:[g.yS,g.Od,g.lC],styles:[".page-spacer[_ngcontent-%COMP%]{padding:30px 15px 30px 30px}.resd-bg[_ngcontent-%COMP%]{background:#ffffff}.residence-sidenav-container[_ngcontent-%COMP%]{position:fixed;border-radius:40px 0 0 40px;background:#2FC4CA;width:240px;height:100vh}.resident-side-heading[_ngcontent-%COMP%]{padding:38px 0 25px}.page-header[_ngcontent-%COMP%]{padding:0 30px}.page-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:30px;color:#fff;cursor:pointer;margin-top:3px}.residence-sidenav-search[_ngcontent-%COMP%]{position:relative;margin-top:27px;padding:0 15px}.search-mod[_ngcontent-%COMP%]{height:37px;border-radius:15px;background:#219399;color:#fff}.search-mod[_ngcontent-%COMP%]::placeholder{color:#fff;font-size:12px}.residents-list-container[_ngcontent-%COMP%]{margin-top:20px;padding:25px 5px}.residents-list-wrap[_ngcontent-%COMP%]{position:relative;margin-bottom:15px}.residets-alphabet[_ngcontent-%COMP%]{color:#219399;font-weight:900;margin-left:30px}.residents-items[_ngcontent-%COMP%]{margin-top:10px;padding:0 10px;position:relative}.residents-items[_ngcontent-%COMP%]   .userlist-name-container[_ngcontent-%COMP%]{padding:0 15px;border-radius:25px}.residents-items[_ngcontent-%COMP%]   .userlist-name-container[_ngcontent-%COMP%]:hover{cursor:pointer;background:#ffffff}.activeResident[_ngcontent-%COMP%]{background:#ffffff}.trim-resd-space[_ngcontent-%COMP%]{padding-left:10px}.heading-space[_ngcontent-%COMP%]{border-radius:40px 0 0 30px;padding-left:10px!important}.resd-width[_ngcontent-%COMP%]{background:#F7F7F7;border-radius:40px 0 0 40px}.resid-info-spacer[_ngcontent-%COMP%]{padding:30px 0 30px 30px;width:100%}.resident-image[_ngcontent-%COMP%]{position:relative}.resident-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:200px;height:200px;border-radius:50%}.select-profile-pic[_ngcontent-%COMP%]{position:absolute;right:23%;background:#219399;height:31px;width:31px;color:#fff;border-radius:50%;cursor:pointer;display:inline-flex;justify-content:center;align-items:center}.select-profile-pic-input[type=file][_ngcontent-%COMP%]{display:none}.section-header[_ngcontent-%COMP%]{margin:0 0 20px;padding-left:30px}.inforspacer[_ngcontent-%COMP%]{padding:30px 0 30px 10px}.resident-info-space[_ngcontent-%COMP%]{margin-bottom:60px}.pdtop-30[_ngcontent-%COMP%]{padding-top:30px}.radio-container[_ngcontent-%COMP%]{display:flex;align-items:center}.checboxcheckmark[_ngcontent-%COMP%]{width:18px;height:18px}.checkbox[_ngcontent-%COMP%]{padding-left:15px}.checkbox[_ngcontent-%COMP%]   .checboxcheckmark[_ngcontent-%COMP%]:after{left:6px;top:2px}.overview-card[_ngcontent-%COMP%]   .col-6[_ngcontent-%COMP%]:nth-last-child(1)   .resident-info[_ngcontent-%COMP%]{margin-bottom:0}.overview-card[_ngcontent-%COMP%]   .col-6[_ngcontent-%COMP%]:nth-last-child(2)   .resident-info[_ngcontent-%COMP%]{margin-bottom:0}.margin-reference[_ngcontent-%COMP%]   .col-6[_ngcontent-%COMP%]:nth-last-child(1)   .resident-info[_ngcontent-%COMP%]{margin-bottom:0}.margin-reference[_ngcontent-%COMP%]   .col-6[_ngcontent-%COMP%]:nth-last-child(2)   .resident-info[_ngcontent-%COMP%]{margin-bottom:0}.appointment-reminder-container[_ngcontent-%COMP%]   .reminder-header[_ngcontent-%COMP%]:nth-last-child(1){margin-bottom:0}.radiobutton[_ngcontent-%COMP%]{padding-left:25px}.radiocheckmark[_ngcontent-%COMP%]{height:14px;width:14px}.radiobutton[_ngcontent-%COMP%]   .radiocheckmark[_ngcontent-%COMP%]:after{top:4px;left:4px;width:4px;height:4px}@media (min-width: 300px) and (max-width: 1050px){.residence-sidenav-container[_ngcontent-%COMP%]{border-radius:40px 40px 0 0}.residence-sidenav-container[_ngcontent-%COMP%]{position:relative;width:100%;height:auto}.resident-side-heading[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding:20px 0 10px}.residents-list-container[_ngcontent-%COMP%]{padding:0 5px;max-height:300px;overflow-y:scroll}.resd-width[_ngcontent-%COMP%]{margin-left:0}.page-spacer[_ngcontent-%COMP%]{padding:15px 0 15px 15px;padding-left:0!important}.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:20px}.tab-wrapper[_ngcontent-%COMP%]{padding:0}.tab-container[_ngcontent-%COMP%]{width:350px;overflow-x:scroll;white-space:nowrap}.resid-info-spacer[_ngcontent-%COMP%]{padding:30px 0 30px 20px}.resident-image[_ngcontent-%COMP%]{display:flex;justify-content:center}.page-header[_ngcontent-%COMP%]{padding-right:15px}.select-profile-pic[_ngcontent-%COMP%]{right:29%}.pl-0[_ngcontent-%COMP%]{padding-left:15px!important}.pr-0[_ngcontent-%COMP%]{padding-right:auto}.overview-spacer-sm[_ngcontent-%COMP%]{margin-top:20px}.pref-spacer-sm[_ngcontent-%COMP%]{margin-top:30px}.overviewpr0[_ngcontent-%COMP%]{padding-right:0}.inforspacer[_ngcontent-%COMP%]{padding:30px 0}.pr-0-sm[_ngcontent-%COMP%]{padding-right:0}}@media (min-width: 700px) and (max-width: 800px){.tab-container[_ngcontent-%COMP%]{width:700px}}@media (min-width: 801px) and (max-width: 1050px){.tab-container[_ngcontent-%COMP%]{width:auto}}.w100[_ngcontent-%COMP%]{width:100%}"]}),n})(),children:[{path:"",redirectTo:"account",pathMatch:"full"},{path:"account",component:(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-patient-account-form"]],decls:92,vars:0,consts:[[1,"row","resid-info-spacer"],[1,"col-12","col-sm-12","col-md-12","col-lg-12"],[1,"col-12","col-sm-12","col-md-6","col-lg-6"],[1,"form-input-container"],[1,"section-header"],[1,"secheading"],[1,"row"],[1,"col-sm-12","col-md-12","col-lg-6"],[1,"input-group"],[1,"label"],["type","text","value","Naomi",1,"form-control"],["type","text","value","Leister",1,"form-control"],[1,"col-sm-12","col-md-12","col-lg-12"],["type","email","value","<EMAIL>",1,"form-control"],[1,"col-sm-12","col-md-12","col-lg-4"],[1,"form-control"],["value",""],["src","./assets/images/icons/australia.png","alt",""],[1,"col-sm-12","col-md-12","col-lg-8"],["type","text","value","0400 888 999",1,"form-control"],["type","text","value","18/02/1942",1,"form-control"],["type","email","value","2255 6666 9999",1,"form-control"],["type","text","value","1",1,"form-control"],["type","text","value","03/2022",1,"form-control"],[1,"col-12"],[1,"reg-btn-wrap"],[1,"btn"],[1,"resident-image"],[1,"img_position"],["src","./assets/images/users/user-lg-01.png","alt",""],[1,"select-profile-pic"],[1,"ri-pencil-line"],["type","file","size","60",1,"select-profile-pic-input"],[1,"text-center","btn-image-update"]],template:function(e,i){1&e&&(t.TgZ(0,"div",0),t._UZ(1,"div",1),t.TgZ(2,"div",2),t.TgZ(3,"div",3),t.TgZ(4,"form"),t.TgZ(5,"div",4),t.TgZ(6,"h2",5),t._uU(7,"General"),t.qZA(),t.qZA(),t.TgZ(8,"div",6),t.TgZ(9,"div",7),t.TgZ(10,"div",8),t.TgZ(11,"label",9),t._uU(12,"First Name"),t.qZA(),t._UZ(13,"input",10),t.qZA(),t.qZA(),t.TgZ(14,"div",7),t.TgZ(15,"div",8),t.TgZ(16,"label",9),t._uU(17,"Last Name"),t.qZA(),t._UZ(18,"input",11),t.qZA(),t.qZA(),t.TgZ(19,"div",12),t.TgZ(20,"div",8),t.TgZ(21,"label",9),t._uU(22,"Email Address"),t.qZA(),t._UZ(23,"input",13),t.qZA(),t.qZA(),t.TgZ(24,"div",14),t.TgZ(25,"div",8),t.TgZ(26,"label",9),t._uU(27,"Country"),t.qZA(),t.TgZ(28,"select",15),t.TgZ(29,"option",16),t._UZ(30,"img",17),t.TgZ(31,"span"),t._uU(32,"Australia"),t.qZA(),t.qZA(),t.TgZ(33,"option",16),t._uU(34,"India"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(35,"div",18),t.TgZ(36,"div",8),t.TgZ(37,"label",9),t._uU(38,"Mobile Number"),t.qZA(),t._UZ(39,"input",19),t.qZA(),t.qZA(),t.TgZ(40,"div",12),t.TgZ(41,"div",8),t.TgZ(42,"label",9),t._uU(43,"Date of Birth"),t.qZA(),t._UZ(44,"input",20),t.qZA(),t.qZA(),t.TgZ(45,"div",12),t.TgZ(46,"div",8),t.TgZ(47,"label",9),t._uU(48,"Preferred Contact Method"),t.qZA(),t.TgZ(49,"select",15),t.TgZ(50,"option"),t._uU(51,"A"),t.qZA(),t.TgZ(52,"option"),t._uU(53,"B"),t.qZA(),t.TgZ(54,"option"),t._uU(55,"C"),t.qZA(),t.TgZ(56,"option"),t._uU(57,"D"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t._UZ(58,"br"),t.TgZ(59,"div",4),t.TgZ(60,"h2",5),t._uU(61,"Medicare"),t.qZA(),t.qZA(),t.TgZ(62,"div",6),t.TgZ(63,"div",12),t.TgZ(64,"div",8),t.TgZ(65,"label",9),t._uU(66,"Medicare Number"),t.qZA(),t._UZ(67,"input",21),t.qZA(),t.qZA(),t.TgZ(68,"div",7),t.TgZ(69,"div",8),t.TgZ(70,"label",9),t._uU(71,"Medicare Position"),t.qZA(),t._UZ(72,"input",22),t.qZA(),t.qZA(),t.TgZ(73,"div",7),t.TgZ(74,"div",8),t.TgZ(75,"label",9),t._uU(76,"Medicare Expiry"),t.qZA(),t._UZ(77,"input",23),t.qZA(),t.qZA(),t.TgZ(78,"div",24),t.TgZ(79,"div",25),t.TgZ(80,"button",26),t._uU(81,"Update Account"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(82,"div",2),t.TgZ(83,"div",27),t.TgZ(84,"div",28),t._UZ(85,"img",29),t.TgZ(86,"label",30),t._UZ(87,"i",31),t._UZ(88,"input",32),t.qZA(),t.TgZ(89,"div",33),t.TgZ(90,"button",26),t._uU(91,"Change Photo"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA())},styles:[".resid-info-spacer[_ngcontent-%COMP%]{padding:0 0 30px 24px}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{margin-top:0;margin-bottom:30px}.resident-image[_ngcontent-%COMP%]{position:relative;text-align:center}.resident-image[_ngcontent-%COMP%]   .img_position[_ngcontent-%COMP%]{position:relative;width:50%;margin:0 auto}.resident-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:200px;height:200px;border-radius:50%}.select-profile-pic[_ngcontent-%COMP%]{position:absolute;right:20%;top:4%;background:#219399;height:31px;width:31px;color:#fff;border-radius:50%;cursor:pointer;display:inline-flex;justify-content:center;align-items:center}.select-profile-pic-input[type=file][_ngcontent-%COMP%]{display:none}.form-input-container[_ngcontent-%COMP%]{position:relative}.form-control[_ngcontent-%COMP%]{height:45px}.reg-btn-wrap[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{background:#2FC4CA}.btn-image-update[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{background:#2FC4CA;margin:10px auto 0}"]}),n})()},{path:"monitoring-setup",component:(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-patient-monitoring-setup"]],decls:450,vars:24,consts:[[1,"row","resid-info-spacer"],[1,"col-12","col-sm-12","col-md-12","col-lg-12"],[1,"table-responsive"],[1,"table"],[1,"top-head"],["colspan","2"],[1,"bottom-head"],[2,"border-bottom","2px solid #ffa800"],[2,"border-bottom","2px solid #FF0000"],[2,"border-bottom","2px solid #22DC04"],["placeholder","XX",1,"form-control"],["placeholder","XX",1,"form-control","control-color",2,"background","#FFA800","border","1px solid  #FFA800"],["placeholder","XX",1,"form-control","control-color",2,"background","#FF0000","border","1px solid  #FFA800"],["placeholder","XX",1,"form-control","control-color",2,"background","#22DC04","border","1px solid  #22DC04"],[1,"col-12"],[1,"row","inforspacer"],[1,"col-12","col-md-6","col-lg-6","pr-0-sm"],[1,"section-header","pdtop-30"],[1,"secheading"],[1,"card","margin-reference"],[1,"row"],[1,"appointment-reminder-container"],[1,"reminder-header","dflexrow","justifybetween"],[1,"reminder-header-txt","reminder-color-dark"],[1,"reminder-header-txt","reminder-color-brand","center-checkbox"],[1,"reminder-info-txt"],[1,"reminder-info-txt","center-checkbox"],[1,"checkbox"],["type","checkbox","name","Type"],[1,"checboxcheckmark"],["type","checkbox","name","Type",3,"checked"],[1,"col-12","col-md-6","col-lg-6","pref-spacer-sm","pr-0-sm"],[1,"margin-reference"],[1,"col-12","col-md-6","col-lg-6"],[1,"form-control"],["type","checkbox","id","self","name","self","value","self"],["for","self"],["type","checkbox","id","nurse","name","nurse","value","nurse","checked",""],["for","nurse"]],template:function(e,i){1&e&&(t.TgZ(0,"div",0),t.TgZ(1,"div",1),t.TgZ(2,"div",2),t.TgZ(3,"table",3),t.TgZ(4,"thead",4),t.TgZ(5,"tr"),t._UZ(6,"th"),t._UZ(7,"th"),t.TgZ(8,"th",5),t.TgZ(9,"span"),t._uU(10,"Lower Limit"),t.qZA(),t.qZA(),t.TgZ(11,"th",5),t.TgZ(12,"span"),t._uU(13,"Upper Limit"),t.qZA(),t.qZA(),t._UZ(14,"th"),t._UZ(15,"th"),t._UZ(16,"th"),t._UZ(17,"th"),t.qZA(),t.qZA(),t.TgZ(18,"thead",6),t.TgZ(19,"tr"),t._UZ(20,"th"),t.TgZ(21,"th"),t.TgZ(22,"span"),t._uU(23,"Unit/M"),t.qZA(),t.qZA(),t.TgZ(24,"th"),t.TgZ(25,"span",7),t._uU(26,"Warning"),t.qZA(),t.qZA(),t.TgZ(27,"th"),t.TgZ(28,"span",8),t._uU(29,"Urgent"),t.qZA(),t.qZA(),t.TgZ(30,"th"),t.TgZ(31,"span",7),t._uU(32,"Warning"),t.qZA(),t.qZA(),t.TgZ(33,"th"),t.TgZ(34,"span",8),t._uU(35,"Urgent"),t.qZA(),t.qZA(),t.TgZ(36,"th"),t.TgZ(37,"span",9),t._uU(38,"Expected"),t.qZA(),t.qZA(),t.TgZ(39,"th"),t.TgZ(40,"span"),t._uU(41,"Last Reading"),t.qZA(),t.qZA(),t.TgZ(42,"th"),t.TgZ(43,"span"),t._uU(44,"Date & Time"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(45,"tbody"),t.TgZ(46,"tr"),t.TgZ(47,"td"),t._uU(48,"BP Systolic"),t.qZA(),t.TgZ(49,"td"),t._UZ(50,"input",10),t.qZA(),t.TgZ(51,"td"),t._UZ(52,"input",10),t.qZA(),t.TgZ(53,"td"),t._UZ(54,"input",10),t.qZA(),t.TgZ(55,"td"),t._UZ(56,"input",10),t.qZA(),t.TgZ(57,"td"),t._UZ(58,"input",10),t.qZA(),t.TgZ(59,"td"),t._UZ(60,"input",10),t.qZA(),t.TgZ(61,"td"),t._UZ(62,"input",10),t.qZA(),t.TgZ(63,"td"),t._UZ(64,"input",10),t.qZA(),t.qZA(),t.TgZ(65,"tr"),t.TgZ(66,"td"),t._uU(67,"BP Diastolic"),t.qZA(),t.TgZ(68,"td"),t._UZ(69,"input",10),t.qZA(),t.TgZ(70,"td"),t._UZ(71,"input",11),t.qZA(),t.TgZ(72,"td"),t._UZ(73,"input",12),t.qZA(),t.TgZ(74,"td"),t._UZ(75,"input",11),t.qZA(),t.TgZ(76,"td"),t._UZ(77,"input",12),t.qZA(),t.TgZ(78,"td"),t._UZ(79,"input",13),t.qZA(),t.TgZ(80,"td"),t._UZ(81,"input",10),t.qZA(),t.TgZ(82,"td"),t._UZ(83,"input",10),t.qZA(),t.qZA(),t.TgZ(84,"tr"),t.TgZ(85,"td"),t._uU(86,"SpO2"),t.qZA(),t.TgZ(87,"td"),t._UZ(88,"input",10),t.qZA(),t.TgZ(89,"td"),t._UZ(90,"input",10),t.qZA(),t.TgZ(91,"td"),t._UZ(92,"input",10),t.qZA(),t.TgZ(93,"td"),t._UZ(94,"input",10),t.qZA(),t.TgZ(95,"td"),t._UZ(96,"input",10),t.qZA(),t.TgZ(97,"td"),t._UZ(98,"input",10),t.qZA(),t.TgZ(99,"td"),t._UZ(100,"input",10),t.qZA(),t.TgZ(101,"td"),t._UZ(102,"input",10),t.qZA(),t.qZA(),t.TgZ(103,"tr"),t.TgZ(104,"td"),t._uU(105,"Temperature"),t.qZA(),t.TgZ(106,"td"),t._UZ(107,"input",10),t.qZA(),t.TgZ(108,"td"),t._UZ(109,"input",10),t.qZA(),t.TgZ(110,"td"),t._UZ(111,"input",10),t.qZA(),t.TgZ(112,"td"),t._UZ(113,"input",10),t.qZA(),t.TgZ(114,"td"),t._UZ(115,"input",10),t.qZA(),t.TgZ(116,"td"),t._UZ(117,"input",10),t.qZA(),t.TgZ(118,"td"),t._UZ(119,"input",10),t.qZA(),t.TgZ(120,"td"),t._UZ(121,"input",10),t.qZA(),t.qZA(),t.TgZ(122,"tr"),t.TgZ(123,"td"),t._uU(124,"Glucose"),t.qZA(),t.TgZ(125,"td"),t._UZ(126,"input",10),t.qZA(),t.TgZ(127,"td"),t._UZ(128,"input",10),t.qZA(),t.TgZ(129,"td"),t._UZ(130,"input",10),t.qZA(),t.TgZ(131,"td"),t._UZ(132,"input",10),t.qZA(),t.TgZ(133,"td"),t._UZ(134,"input",10),t.qZA(),t.TgZ(135,"td"),t._UZ(136,"input",10),t.qZA(),t.TgZ(137,"td"),t._UZ(138,"input",10),t.qZA(),t.TgZ(139,"td"),t._UZ(140,"input",10),t.qZA(),t.qZA(),t.TgZ(141,"tr"),t.TgZ(142,"td"),t._uU(143,"Weight"),t.qZA(),t.TgZ(144,"td"),t._UZ(145,"input",10),t.qZA(),t.TgZ(146,"td"),t._UZ(147,"input",10),t.qZA(),t.TgZ(148,"td"),t._UZ(149,"input",10),t.qZA(),t.TgZ(150,"td"),t._UZ(151,"input",10),t.qZA(),t.TgZ(152,"td"),t._UZ(153,"input",10),t.qZA(),t.TgZ(154,"td"),t._UZ(155,"input",10),t.qZA(),t.TgZ(156,"td"),t._UZ(157,"input",10),t.qZA(),t.TgZ(158,"td"),t._UZ(159,"input",10),t.qZA(),t.qZA(),t.TgZ(160,"tr"),t.TgZ(161,"td"),t._uU(162,"Heart Rate"),t.qZA(),t.TgZ(163,"td"),t._UZ(164,"input",10),t.qZA(),t.TgZ(165,"td"),t._UZ(166,"input",10),t.qZA(),t.TgZ(167,"td"),t._UZ(168,"input",10),t.qZA(),t.TgZ(169,"td"),t._UZ(170,"input",10),t.qZA(),t.TgZ(171,"td"),t._UZ(172,"input",10),t.qZA(),t.TgZ(173,"td"),t._UZ(174,"input",10),t.qZA(),t.TgZ(175,"td"),t._UZ(176,"input",10),t.qZA(),t.TgZ(177,"td"),t._UZ(178,"input",10),t.qZA(),t.qZA(),t.TgZ(179,"tr"),t.TgZ(180,"td"),t._uU(181,"UTI"),t.qZA(),t.TgZ(182,"td"),t._UZ(183,"input",10),t.qZA(),t.TgZ(184,"td"),t._UZ(185,"input",10),t.qZA(),t.TgZ(186,"td"),t._UZ(187,"input",10),t.qZA(),t.TgZ(188,"td"),t._UZ(189,"input",10),t.qZA(),t.TgZ(190,"td"),t._UZ(191,"input",10),t.qZA(),t.TgZ(192,"td"),t._UZ(193,"input",10),t.qZA(),t.TgZ(194,"td"),t._UZ(195,"input",10),t.qZA(),t.TgZ(196,"td"),t._UZ(197,"input",10),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(198,"div",14),t.TgZ(199,"div",15),t.TgZ(200,"div",16),t.TgZ(201,"div",17),t.TgZ(202,"h2",18),t._uU(203,"Monitoring Times"),t.qZA(),t.qZA(),t.TgZ(204,"div",19),t.TgZ(205,"div",20),t.TgZ(206,"div",14),t.TgZ(207,"div",21),t.TgZ(208,"div",22),t.TgZ(209,"div",23),t._uU(210,"#"),t.qZA(),t.TgZ(211,"div",24),t._uU(212,"Mon"),t.qZA(),t.TgZ(213,"div",24),t._uU(214,"Tue"),t.qZA(),t.TgZ(215,"div",24),t._uU(216,"Wed"),t.qZA(),t.TgZ(217,"div",24),t._uU(218,"Thu"),t.qZA(),t.TgZ(219,"div",24),t._uU(220,"Fri"),t.qZA(),t.TgZ(221,"div",24),t._uU(222,"Sat"),t.qZA(),t.TgZ(223,"div",24),t._uU(224,"Sun"),t.qZA(),t.qZA(),t.TgZ(225,"div",22),t.TgZ(226,"div",25),t._uU(227,"1"),t.qZA(),t.TgZ(228,"div",26),t.TgZ(229,"label",27),t._UZ(230,"input",28),t._UZ(231,"span",29),t.qZA(),t.qZA(),t.TgZ(232,"div",26),t.TgZ(233,"label",27),t._UZ(234,"input",30),t._UZ(235,"span",29),t.qZA(),t.qZA(),t.TgZ(236,"div",26),t.TgZ(237,"label",27),t._UZ(238,"input",30),t._UZ(239,"span",29),t.qZA(),t.qZA(),t.TgZ(240,"div",26),t.TgZ(241,"label",27),t._UZ(242,"input",30),t._UZ(243,"span",29),t.qZA(),t.qZA(),t.TgZ(244,"div",26),t.TgZ(245,"label",27),t._UZ(246,"input",30),t._UZ(247,"span",29),t.qZA(),t.qZA(),t.TgZ(248,"div",26),t.TgZ(249,"label",27),t._UZ(250,"input",30),t._UZ(251,"span",29),t.qZA(),t.qZA(),t.TgZ(252,"div",26),t.TgZ(253,"label",27),t._UZ(254,"input",28),t._UZ(255,"span",29),t.qZA(),t.qZA(),t.qZA(),t.TgZ(256,"div",22),t.TgZ(257,"div",25),t._uU(258,"2"),t.qZA(),t.TgZ(259,"div",26),t.TgZ(260,"label",27),t._UZ(261,"input",30),t._UZ(262,"span",29),t.qZA(),t.qZA(),t.TgZ(263,"div",26),t.TgZ(264,"label",27),t._UZ(265,"input",28),t._UZ(266,"span",29),t.qZA(),t.qZA(),t.TgZ(267,"div",26),t.TgZ(268,"label",27),t._UZ(269,"input",30),t._UZ(270,"span",29),t.qZA(),t.qZA(),t.TgZ(271,"div",26),t.TgZ(272,"label",27),t._UZ(273,"input",28),t._UZ(274,"span",29),t.qZA(),t.qZA(),t.TgZ(275,"div",26),t.TgZ(276,"label",27),t._UZ(277,"input",28),t._UZ(278,"span",29),t.qZA(),t.qZA(),t.TgZ(279,"div",26),t.TgZ(280,"label",27),t._UZ(281,"input",30),t._UZ(282,"span",29),t.qZA(),t.qZA(),t.TgZ(283,"div",26),t.TgZ(284,"label",27),t._UZ(285,"input",28),t._UZ(286,"span",29),t.qZA(),t.qZA(),t.qZA(),t.TgZ(287,"div",22),t.TgZ(288,"div",25),t._uU(289,"3"),t.qZA(),t.TgZ(290,"div",26),t.TgZ(291,"label",27),t._UZ(292,"input",28),t._UZ(293,"span",29),t.qZA(),t.qZA(),t.TgZ(294,"div",26),t.TgZ(295,"label",27),t._UZ(296,"input",30),t._UZ(297,"span",29),t.qZA(),t.qZA(),t.TgZ(298,"div",26),t.TgZ(299,"label",27),t._UZ(300,"input",30),t._UZ(301,"span",29),t.qZA(),t.qZA(),t.TgZ(302,"div",26),t.TgZ(303,"label",27),t._UZ(304,"input",30),t._UZ(305,"span",29),t.qZA(),t.qZA(),t.TgZ(306,"div",26),t.TgZ(307,"label",27),t._UZ(308,"input",30),t._UZ(309,"span",29),t.qZA(),t.qZA(),t.TgZ(310,"div",26),t.TgZ(311,"label",27),t._UZ(312,"input",28),t._UZ(313,"span",29),t.qZA(),t.qZA(),t.TgZ(314,"div",26),t.TgZ(315,"label",27),t._UZ(316,"input",30),t._UZ(317,"span",29),t.qZA(),t.qZA(),t.qZA(),t.TgZ(318,"div",22),t.TgZ(319,"div",25),t._uU(320,"4"),t.qZA(),t.TgZ(321,"div",26),t.TgZ(322,"label",27),t._UZ(323,"input",30),t._UZ(324,"span",29),t.qZA(),t.qZA(),t.TgZ(325,"div",26),t.TgZ(326,"label",27),t._UZ(327,"input",28),t._UZ(328,"span",29),t.qZA(),t.qZA(),t.TgZ(329,"div",26),t.TgZ(330,"label",27),t._UZ(331,"input",30),t._UZ(332,"span",29),t.qZA(),t.qZA(),t.TgZ(333,"div",26),t.TgZ(334,"label",27),t._UZ(335,"input",28),t._UZ(336,"span",29),t.qZA(),t.qZA(),t.TgZ(337,"div",26),t.TgZ(338,"label",27),t._UZ(339,"input",28),t._UZ(340,"span",29),t.qZA(),t.qZA(),t.TgZ(341,"div",26),t.TgZ(342,"label",27),t._UZ(343,"input",28),t._UZ(344,"span",29),t.qZA(),t.qZA(),t.TgZ(345,"div",26),t.TgZ(346,"label",27),t._UZ(347,"input",30),t._UZ(348,"span",29),t.qZA(),t.qZA(),t.qZA(),t.TgZ(349,"div",22),t.TgZ(350,"div",25),t._uU(351,"5"),t.qZA(),t.TgZ(352,"div",26),t.TgZ(353,"label",27),t._UZ(354,"input",28),t._UZ(355,"span",29),t.qZA(),t.qZA(),t.TgZ(356,"div",26),t.TgZ(357,"label",27),t._UZ(358,"input",30),t._UZ(359,"span",29),t.qZA(),t.qZA(),t.TgZ(360,"div",26),t.TgZ(361,"label",27),t._UZ(362,"input",30),t._UZ(363,"span",29),t.qZA(),t.qZA(),t.TgZ(364,"div",26),t.TgZ(365,"label",27),t._UZ(366,"input",30),t._UZ(367,"span",29),t.qZA(),t.qZA(),t.TgZ(368,"div",26),t.TgZ(369,"label",27),t._UZ(370,"input",30),t._UZ(371,"span",29),t.qZA(),t.qZA(),t.TgZ(372,"div",26),t.TgZ(373,"label",27),t._UZ(374,"input",30),t._UZ(375,"span",29),t.qZA(),t.qZA(),t.TgZ(376,"div",26),t.TgZ(377,"label",27),t._UZ(378,"input",28),t._UZ(379,"span",29),t.qZA(),t.qZA(),t.qZA(),t.TgZ(380,"div",22),t.TgZ(381,"div",25),t._uU(382,"6"),t.qZA(),t.TgZ(383,"div",26),t.TgZ(384,"label",27),t._UZ(385,"input",30),t._UZ(386,"span",29),t.qZA(),t.qZA(),t.TgZ(387,"div",26),t.TgZ(388,"label",27),t._UZ(389,"input",28),t._UZ(390,"span",29),t.qZA(),t.qZA(),t.TgZ(391,"div",26),t.TgZ(392,"label",27),t._UZ(393,"input",30),t._UZ(394,"span",29),t.qZA(),t.qZA(),t.TgZ(395,"div",26),t.TgZ(396,"label",27),t._UZ(397,"input",28),t._UZ(398,"span",29),t.qZA(),t.qZA(),t.TgZ(399,"div",26),t.TgZ(400,"label",27),t._UZ(401,"input",28),t._UZ(402,"span",29),t.qZA(),t.qZA(),t.TgZ(403,"div",26),t.TgZ(404,"label",27),t._UZ(405,"input",28),t._UZ(406,"span",29),t.qZA(),t.qZA(),t.TgZ(407,"div",26),t.TgZ(408,"label",27),t._UZ(409,"input",30),t._UZ(410,"span",29),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(411,"div",31),t.TgZ(412,"div",17),t.TgZ(413,"h2",18),t._uU(414,"Alerts"),t.qZA(),t.qZA(),t.TgZ(415,"div",32),t.TgZ(416,"div",20),t.TgZ(417,"div",33),t.TgZ(418,"label"),t._uU(419,"Warning"),t.qZA(),t.TgZ(420,"select",34),t.TgZ(421,"option"),t._uU(422,"Select"),t.qZA(),t.TgZ(423,"option"),t._uU(424,"Nurse"),t.qZA(),t.TgZ(425,"option"),t._uU(426,"Doctor"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(427,"div",33),t.TgZ(428,"label"),t._uU(429,"Urgent"),t.qZA(),t.TgZ(430,"select",34),t.TgZ(431,"option"),t._uU(432,"Select"),t.qZA(),t.TgZ(433,"option"),t._uU(434,"Nurse"),t.qZA(),t.TgZ(435,"option"),t._uU(436,"Doctor"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(437,"div",17),t.TgZ(438,"h2",18),t._uU(439,"Monitoring Type"),t.qZA(),t.qZA(),t.TgZ(440,"div",32),t.TgZ(441,"div",20),t.TgZ(442,"div",33),t._UZ(443,"input",35),t.TgZ(444,"label",36),t._uU(445," Self Monitoring"),t.qZA(),t.qZA(),t.TgZ(446,"div",33),t._UZ(447,"input",37),t.TgZ(448,"label",38),t._uU(449," Nurse Monitoring"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA()),2&e&&(t.xp6(234),t.Q6J("checked",!0),t.xp6(4),t.Q6J("checked",!0),t.xp6(4),t.Q6J("checked",!0),t.xp6(4),t.Q6J("checked",!0),t.xp6(4),t.Q6J("checked",!0),t.xp6(11),t.Q6J("checked",!0),t.xp6(8),t.Q6J("checked",!0),t.xp6(12),t.Q6J("checked",!0),t.xp6(15),t.Q6J("checked",!0),t.xp6(4),t.Q6J("checked",!0),t.xp6(4),t.Q6J("checked",!0),t.xp6(4),t.Q6J("checked",!0),t.xp6(8),t.Q6J("checked",!0),t.xp6(7),t.Q6J("checked",!0),t.xp6(8),t.Q6J("checked",!0),t.xp6(16),t.Q6J("checked",!0),t.xp6(11),t.Q6J("checked",!0),t.xp6(4),t.Q6J("checked",!0),t.xp6(4),t.Q6J("checked",!0),t.xp6(4),t.Q6J("checked",!0),t.xp6(4),t.Q6J("checked",!0),t.xp6(11),t.Q6J("checked",!0),t.xp6(8),t.Q6J("checked",!0),t.xp6(16),t.Q6J("checked",!0))},styles:[".resid-info-spacer[_ngcontent-%COMP%]{padding:0 0 30px 30px;width:100%}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{margin-top:0}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .secheading[_ngcontent-%COMP%]{margin-bottom:15px}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#a2a2a2}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#2fc4ca;font-weight:600}.form-control[_ngcontent-%COMP%]{border:1px solid #ddd;margin-top:12px}.pdtop-30[_ngcontent-%COMP%]{padding-top:30px}table[_ngcontent-%COMP%]{border-collapse:collapse}table[_ngcontent-%COMP%]   .top-head[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{color:#000;font-weight:700;position:relative}table[_ngcontent-%COMP%]   .top-head[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{position:absolute;left:28%;right:0;top:68%;background:#F7F7F7;width:-moz-fit-content;width:fit-content;padding:2px 6px}table[_ngcontent-%COMP%]   .bottom-head[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{padding:16px 0}table[_ngcontent-%COMP%]   .bottom-head[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(3)   span[_ngcontent-%COMP%]{border-left:1px solid #E2E2E2;border-top:1px solid #E2E2E2;padding:16px 14px}table[_ngcontent-%COMP%]   .bottom-head[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(4)   span[_ngcontent-%COMP%]{border-right:1px solid #E2E2E2;border-top:1px solid #E2E2E2;padding:16px 14px}table[_ngcontent-%COMP%]   .bottom-head[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(5)   span[_ngcontent-%COMP%]{border-left:1px solid #E2E2E2;border-top:1px solid #E2E2E2;padding:16px 14px}table[_ngcontent-%COMP%]   .bottom-head[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(6)   span[_ngcontent-%COMP%]{border-right:1px solid #E2E2E2;border-top:1px solid #E2E2E2;padding:16px 14px}table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{color:#219399;font-size:13px;padding:18px 5px;text-align:center;font-weight:400}table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:10px 5px;text-align:center;background:#ffffff;border-bottom:4px solid #F7F7F7}table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child{text-align:left;font-weight:700;border-radius:12px 0 0 12px;padding-left:12px;font-size:13px}table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:last-child{border-radius:0 12px 12px 0;padding-right:12px}table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{text-align:center;height:36px;font-size:12px;border-radius:8px;margin-top:0}.table[_ngcontent-%COMP%]{width:100%;max-width:100%;background-color:transparent}@media (max-width: 991px){table[_ngcontent-%COMP%]   .top-head[_ngcontent-%COMP%]{display:none}table[_ngcontent-%COMP%]   .bottom-head[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(3)   span[_ngcontent-%COMP%]{border-left:none;border-top:none;padding:0}table[_ngcontent-%COMP%]   .bottom-head[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(4)   span[_ngcontent-%COMP%]{border-right:none;border-top:none;padding:0}table[_ngcontent-%COMP%]   .bottom-head[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(5)   span[_ngcontent-%COMP%]{border-left:none;border-top:none;padding:0}table[_ngcontent-%COMP%]   .bottom-head[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(6)   span[_ngcontent-%COMP%]{border-right:none;border-top:none;padding:0}}.reminder-info-txt[_ngcontent-%COMP%]{margin-right:-18px}.control-color[_ngcontent-%COMP%]{color:#fff}.control-color[_ngcontent-%COMP%]::placeholder{color:#fff}"]}),n})()},{path:"consent",component:(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-patient-consent"]],decls:2,vars:0,template:function(e,i){1&e&&(t.TgZ(0,"p"),t._uU(1,"patient-consent works!"),t.qZA())},styles:[""]}),n})()},{path:"password",component:(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-patient-password"]],decls:37,vars:0,consts:[[1,"resid-info-spacer"],[1,"password_block"],[1,"row"],[1,"col-12","col-sm-12","col-md-6","col-lg-12"],[1,"section-header"],[1,"secheading"],[1,"col-12","col-sm-12","col-md-12","col-lg-12"],[1,"form-input-container",2,"margin-bottom","20px"],[1,"col-sm-12","col-md-12","col-lg-12"],[1,"input-group"],[1,"label"],["type","text","value","Enter your current password",1,"form-control"],[1,"form-input-container"],["type","text","value","New Password",1,"form-control"],["type","text","value","Confirm Password",1,"form-control"],[1,"col-12"],[1,"reg-btn-wrap"],[1,"btn"]],template:function(e,i){1&e&&(t.TgZ(0,"div",0),t.TgZ(1,"form"),t.TgZ(2,"div",1),t.TgZ(3,"div",2),t.TgZ(4,"div",3),t.TgZ(5,"div",4),t.TgZ(6,"h2",5),t._uU(7,"Current Password"),t.qZA(),t.qZA(),t.TgZ(8,"div",6),t.TgZ(9,"div",7),t.TgZ(10,"div",2),t.TgZ(11,"div",8),t.TgZ(12,"div",9),t.TgZ(13,"label",10),t._uU(14,"Current Password"),t.qZA(),t._UZ(15,"input",11),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(16,"div",3),t.TgZ(17,"div",4),t.TgZ(18,"h2",5),t._uU(19,"New Password"),t.qZA(),t.qZA(),t.TgZ(20,"div",6),t.TgZ(21,"div",12),t.TgZ(22,"div",2),t.TgZ(23,"div",8),t.TgZ(24,"div",9),t.TgZ(25,"label",10),t._uU(26,"New Password"),t.qZA(),t._UZ(27,"input",13),t.qZA(),t.qZA(),t.TgZ(28,"div",8),t.TgZ(29,"div",9),t.TgZ(30,"label",10),t._uU(31,"Confirm Password"),t.qZA(),t._UZ(32,"input",14),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(33,"div",15),t.TgZ(34,"div",16),t.TgZ(35,"button",17),t._uU(36,"Update Password"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA())},styles:[".resid-info-spacer[_ngcontent-%COMP%]{padding:0 0 30px 10px}.resid-info-spacer[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{margin-top:0;margin-bottom:30px;padding-left:16px}.form-input-container[_ngcontent-%COMP%]{position:relative}.form-control[_ngcontent-%COMP%]{height:45px}.reg-btn-wrap[_ngcontent-%COMP%]{padding:0 15px}.reg-btn-wrap[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{background:#219399}.password_block[_ngcontent-%COMP%]{max-width:500px}"]}),n})()}]}];let f=(()=>{class n{}return n.\u0275fac=function(e){return new(e||n)},n.\u0275mod=t.oAB({type:n}),n.\u0275inj=t.cJS({imports:[[g.Bz.forChild(O)],g.Bz]}),n})();var x=c(3165);let U=(()=>{class n{}return n.\u0275fac=function(e){return new(e||n)},n.\u0275mod=t.oAB({type:n}),n.\u0275inj=t.cJS({imports:[[p.ez,f,x.N]]}),n})()}}]);