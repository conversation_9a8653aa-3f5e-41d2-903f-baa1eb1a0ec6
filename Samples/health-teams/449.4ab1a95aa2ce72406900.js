"use strict";(self.webpackChunkhealth_teams=self.webpackChunkhealth_teams||[]).push([[449],{2449:(A,Z,o)=>{o.r(Z),o.d(Z,{PatientAuthModule:()=>m});var c=o(8583),a=o(4494),t=o(639);function s(e,i){if(1&e){const n=t.EpF();t.TgZ(0,"section",1),t.TgZ(1,"div",2),t.TgZ(2,"div",3),t.TgZ(3,"div",4),t.TgZ(4,"div",5),t.TgZ(5,"div",6),t._<PERSON><PERSON>(6,"img",7),t.qZ<PERSON>(),t.TgZ(7,"div",8),t.TgZ(8,"h1",9),t._u<PERSON>(9,"Hello <PERSON>,"),t._<PERSON><PERSON>(10,"br"),t._u<PERSON>(11," Welcome to Health Teams"),t.q<PERSON><PERSON>(),t.q<PERSON><PERSON>(),t.Tg<PERSON>(12,"div",10),t._<PERSON><PERSON>(13,"img",11),t.qZA(),t.qZA(),t.qZA(),t.TgZ(14,"div",12),t.TgZ(15,"div",13),t.TgZ(16,"div",14),t.TgZ(17,"div",15),t.TgZ(18,"h1",9),t._uU(19,"Let\u2019s get your account setup. "),t._UZ(20,"br"),t._uU(21,"Are the below details correct?"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(22,"div",16),t.TgZ(23,"form"),t.TgZ(24,"div",3),t.TgZ(25,"div",17),t.TgZ(26,"div",18),t.TgZ(27,"label",19),t._uU(28,"First Name"),t.qZA(),t._UZ(29,"input",20),t.qZA(),t.qZA(),t.TgZ(30,"div",17),t.TgZ(31,"div",18),t.TgZ(32,"label",19),t._uU(33,"Last Name"),t.qZA(),t._UZ(34,"input",20),t.qZA(),t.qZA(),t.TgZ(35,"div",21),t.TgZ(36,"div",18),t.TgZ(37,"label",19),t._uU(38,"Country"),t.qZA(),t.TgZ(39,"select",22),t.TgZ(40,"option",23),t._uU(41,"+61"),t.qZA(),t.TgZ(42,"option",23),t._uU(43,"+91"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(44,"div",24),t.TgZ(45,"div",18),t.TgZ(46,"label",19),t._uU(47,"Mobile Number"),t.qZA(),t._UZ(48,"input",20),t.qZA(),t.qZA(),t.TgZ(49,"div",17),t.TgZ(50,"div",18),t.TgZ(51,"label",19),t._uU(52,"Facility"),t.qZA(),t._UZ(53,"input",20),t.qZA(),t.qZA(),t.TgZ(54,"div",17),t.TgZ(55,"div",25),t.TgZ(56,"a",26),t.NdJ("click",function(){t.CHM(n);const r=t.oxw();return r.step1=!1,r.step2=!0}),t._uU(57,"Correct "),t._UZ(58,"i",27),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA()}}function p(e,i){if(1&e){const n=t.EpF();t.TgZ(0,"section",1),t.TgZ(1,"div",2),t.TgZ(2,"div",3),t.TgZ(3,"div",4),t.TgZ(4,"div",5),t.TgZ(5,"div",6),t._UZ(6,"img",7),t.qZA(),t.TgZ(7,"div",8),t.TgZ(8,"h1",9),t._uU(9,"Hello Janice,"),t._UZ(10,"br"),t._uU(11," Welcome to Health Teams"),t.qZA(),t.qZA(),t.TgZ(12,"div",10),t._UZ(13,"img",11),t.qZA(),t.qZA(),t.qZA(),t.TgZ(14,"div",12),t.TgZ(15,"div",13),t.TgZ(16,"div",14),t.TgZ(17,"div",15),t.TgZ(18,"h1",9),t._uU(19,"Now let\u2019s set "),t._UZ(20,"br"),t._uU(21," your login details"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(22,"div",16),t.TgZ(23,"form"),t.TgZ(24,"div",3),t.TgZ(25,"div",17),t.TgZ(26,"div",18),t.TgZ(27,"label",19),t._uU(28,"Email Address"),t.qZA(),t._UZ(29,"input",28),t.qZA(),t.qZA(),t.TgZ(30,"div",17),t.TgZ(31,"div",18),t.TgZ(32,"label",19),t._uU(33,"Password"),t.qZA(),t._UZ(34,"input",29),t.qZA(),t.qZA(),t.TgZ(35,"div",17),t.TgZ(36,"div",18),t.TgZ(37,"label",19),t._uU(38,"Confirm Password"),t.qZA(),t._UZ(39,"input",29),t.qZA(),t.qZA(),t.TgZ(40,"div",17),t.TgZ(41,"div",25),t.TgZ(42,"a",26),t.NdJ("click",function(){t.CHM(n);const r=t.oxw();return r.step2=!1,r.step3=!0}),t._uU(43,"Finish Setup "),t._UZ(44,"i",27),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA()}}function d(e,i){1&e&&(t.TgZ(0,"section",1),t.TgZ(1,"div",2),t.TgZ(2,"div",30),t.TgZ(3,"div",31),t.TgZ(4,"div",5),t.TgZ(5,"div",6),t._UZ(6,"img",7),t.qZA(),t.TgZ(7,"div",8),t.TgZ(8,"h1",9),t._uU(9,"Thank you Janice, "),t._UZ(10,"br"),t._uU(11," Your account is now ready to use"),t.qZA(),t.qZA(),t.TgZ(12,"div",17),t.TgZ(13,"div",25),t.TgZ(14,"a",32),t._uU(15,"Go To Dashboard"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(16,"div",10),t._UZ(17,"img",11),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA())}const l=[{path:"",redirectTo:"register",pathMatch:"full"},{path:"register",component:(()=>{class e{constructor(){this.step1=!0,this.step2=!1,this.step3=!1}ngOnInit(){}}return e.\u0275fac=function(n){return new(n||e)},e.\u0275cmp=t.Xpm({type:e,selectors:[["app-register-patient"]],decls:3,vars:3,consts:[["class","bg-light reg-section",4,"ngIf"],[1,"bg-light","reg-section"],[1,"container-fluid"],[1,"row"],[1,"col-sm-12","col-md-12","col-lg-6"],[1,"register-bannerwrapper","dflexcol","justifycenter","aligncenter"],[1,"brand-logo-wrapper"],["src","./assets/images/logo/logo.png","alt",""],[1,"reg-header"],[1,"text-center"],[1,"reg-imgwrap","dflexrow","justifycenter"],["src","./assets/images/group-2.png","alt","",1,"frame-wrap"],[1,"col-sm-12","col-md-12","col-lg-6","padd0"],[1,"form-main-container"],[1,"reg-form-section","dflexcol","justifycenter","align-center"],[1,"form-heading-wrap"],[1,"form-input-container"],[1,"col-12"],[1,"input-group"],[1,"label"],["type","text",1,"form-control"],[1,"col-sm-12","col-md-12","col-lg-4"],[1,"form-control"],["value",""],[1,"col-sm-12","col-md-12","col-lg-8"],[1,"reg-btn-wrap","dflexrow","justifycenter"],["routerLink","/dashboard",1,"btn","btn-orange",3,"click"],[1,"ri-check-fill"],["type","email",1,"form-control"],["type","password",1,"form-control"],[1,"row","justify-content-center"],[1,"col-6"],["routerLink","/patient",1,"btn","btn-orange"]],template:function(n,g){1&n&&(t.YNc(0,s,59,0,"section",0),t.YNc(1,p,45,0,"section",0),t.YNc(2,d,18,0,"section",0)),2&n&&(t.Q6J("ngIf",g.step1),t.xp6(1),t.Q6J("ngIf",g.step2),t.xp6(1),t.Q6J("ngIf",g.step3))},directives:[c.O5,a.yS],styles:[".reg-section[_ngcontent-%COMP%]{position:relative}.register-bannerwrapper[_ngcontent-%COMP%]{position:relative;padding:40px}.brand-logo-wrapper[_ngcontent-%COMP%]{position:relative;margin-top:20px}.brand-logo-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:220px}.reg-header[_ngcontent-%COMP%]{position:relative;margin-top:30px}.reg-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;font-weight:900;color:#2fc4ca}.reg-imgwrap[_ngcontent-%COMP%]{position:relative;margin-top:53px;width:100%}.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:85%}.form-main-container[_ngcontent-%COMP%]{padding:40px;background:#F7F7F7;height:100%;border-radius:30px 0 0 30px}.reg-form-section[_ngcontent-%COMP%]{position:relative}.form-heading-wrap[_ngcontent-%COMP%]{margin-top:100px;position:relative}.form-heading-wrap[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;font-weight:900;color:#2fc4ca}.padd0[_ngcontent-%COMP%]{padding:0}.form-input-container[_ngcontent-%COMP%]{position:relative;padding:41px 14.5%}.register-text[_ngcontent-%COMP%]{position:relative;margin-top:8px}.register-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:14px;font-weight:700;line-height:20px}.reg-btn-wrap[_ngcontent-%COMP%]{position:relative;margin-top:28px}@media (min-width: 300px) and (max-width: 768px){.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%}.form-heading-wrap[_ngcontent-%COMP%]{margin-top:0}.form-main-container[_ngcontent-%COMP%]{padding:40px 20px}.form-input-container[_ngcontent-%COMP%]{padding:41px 10px}}@media (min-width: 768px) and (max-width: 1050px){.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%}.form-main-container[_ngcontent-%COMP%]{padding:40px 10px}}"]}),e})()}];let u=(()=>{class e{}return e.\u0275fac=function(n){return new(n||e)},e.\u0275mod=t.oAB({type:e}),e.\u0275inj=t.cJS({imports:[[a.Bz.forChild(l)],a.Bz]}),e})(),m=(()=>{class e{}return e.\u0275fac=function(n){return new(n||e)},e.\u0275mod=t.oAB({type:e}),e.\u0275inj=t.cJS({imports:[[c.ez,u]]}),e})()}}]);