"use strict";(self.webpackChunkhealth_teams=self.webpackChunkhealth_teams||[]).push([[805],{6805:(A,c,i)=>{i.r(c),i.d(c,{DoctorAuthModule:()=>m});var a=i(8583),Z=i(4494),t=i(639);function d(e,o){if(1&e){const n=t.EpF();t.TgZ(0,"section",1),t.TgZ(1,"div",2),t.TgZ(2,"div",3),t.TgZ(3,"div",4),t.TgZ(4,"div",5),t.TgZ(5,"div",6),t._<PERSON><PERSON>(6,"img",7),t.qZ<PERSON>(),t.TgZ(7,"div",8),t.TgZ(8,"h1",9),t._uU(9,"Welcome to Health Teams"),t.q<PERSON><PERSON>(),t.q<PERSON><PERSON>(),t.Tg<PERSON>(10,"div",10),t._<PERSON><PERSON>(11,"img",11),t.q<PERSON><PERSON>(),t.q<PERSON><PERSON>(),t.q<PERSON><PERSON>(),t.TgZ(12,"div",12),t.TgZ(13,"div",13),t.TgZ(14,"div",14),t.TgZ(15,"div",15),t.TgZ(16,"h1",9),t._uU(17,"Let\u2019s get your account setup."),t.qZA(),t.qZA(),t.qZA(),t.TgZ(18,"div",16),t.TgZ(19,"form"),t.TgZ(20,"div",3),t.TgZ(21,"div",17),t.TgZ(22,"div",18),t.TgZ(23,"label",19),t._uU(24,"First Name"),t.qZA(),t._UZ(25,"input",20),t.qZA(),t.qZA(),t.TgZ(26,"div",17),t.TgZ(27,"div",18),t.TgZ(28,"label",19),t._uU(29,"Last Name"),t.qZA(),t._UZ(30,"input",20),t.qZA(),t.qZA(),t.TgZ(31,"div",21),t.TgZ(32,"div",18),t.TgZ(33,"label",19),t._uU(34,"Email Address"),t.qZA(),t._UZ(35,"input",22),t.qZA(),t.qZA(),t.TgZ(36,"div",23),t.TgZ(37,"div",18),t.TgZ(38,"label",19),t._uU(39,"Country"),t.qZA(),t.TgZ(40,"select",24),t.TgZ(41,"option",25),t._uU(42,"+61"),t.qZA(),t.TgZ(43,"option",25),t._uU(44,"+91"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(45,"div",26),t.TgZ(46,"div",18),t.TgZ(47,"label",19),t._uU(48,"Mobile Number"),t.qZA(),t._UZ(49,"input",20),t.qZA(),t.qZA(),t.TgZ(50,"div",21),t.TgZ(51,"div",18),t.TgZ(52,"label",19),t._uU(53,"Medipass"),t.qZA(),t._UZ(54,"input",20),t.qZA(),t.qZA(),t.TgZ(55,"div",21),t.TgZ(56,"div",18),t.TgZ(57,"label",19),t._uU(58,"HealthLink EDI"),t.qZA(),t._UZ(59,"input",20),t.qZA(),t.qZA(),t.TgZ(60,"div",21),t.TgZ(61,"div",18),t.TgZ(62,"p"),t._uU(63,"If you have a connection code, enter it here, if not, you can connect to Facilities later."),t.qZA(),t.qZA(),t.qZA(),t.TgZ(64,"div",27),t.TgZ(65,"div",18),t.TgZ(66,"label",19),t._uU(67,"6 Digit Connection Code"),t.qZA(),t._UZ(68,"input",20),t.qZA(),t.qZA(),t.TgZ(69,"div",27),t.TgZ(70,"div",18),t.TgZ(71,"label",19),t._uU(72,"Connected Facility"),t.qZA(),t._UZ(73,"input",20),t.qZA(),t.qZA(),t.TgZ(74,"div",21),t.TgZ(75,"div",28),t.TgZ(76,"a",29),t.NdJ("click",function(){t.CHM(n);const g=t.oxw();return g.step1=!1,g.step2=!0}),t._uU(77,"Next "),t._UZ(78,"i",30),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA()}}function l(e,o){if(1&e){const n=t.EpF();t.TgZ(0,"section",1),t.TgZ(1,"div",2),t.TgZ(2,"div",3),t.TgZ(3,"div",4),t.TgZ(4,"div",5),t.TgZ(5,"div",6),t._UZ(6,"img",7),t.qZA(),t.TgZ(7,"div",8),t.TgZ(8,"h1",9),t._uU(9,"Welcome to Health Teams"),t.qZA(),t.qZA(),t.TgZ(10,"div",10),t._UZ(11,"img",11),t.qZA(),t.qZA(),t.qZA(),t.TgZ(12,"div",12),t.TgZ(13,"div",13),t.TgZ(14,"div",14),t.TgZ(15,"div",15),t.TgZ(16,"h1",9),t._uU(17,"Now let\u2019s set "),t._UZ(18,"br"),t._uU(19," your login details"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(20,"div",16),t.TgZ(21,"form"),t.TgZ(22,"div",3),t.TgZ(23,"div",31),t.TgZ(24,"label",32),t._UZ(25,"i",33),t._UZ(26,"input",34),t.TgZ(27,"span",35),t._uU(28,"Upload Profile "),t._UZ(29,"br"),t._uU(30," Photo"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(31,"div",21),t.TgZ(32,"div",18),t.TgZ(33,"label",19),t._uU(34,"Email Address"),t.qZA(),t._UZ(35,"input",22),t.qZA(),t.qZA(),t.TgZ(36,"div",21),t.TgZ(37,"div",18),t.TgZ(38,"label",19),t._uU(39,"Password"),t.qZA(),t._UZ(40,"input",36),t.qZA(),t.qZA(),t.TgZ(41,"div",21),t.TgZ(42,"div",18),t.TgZ(43,"label",19),t._uU(44,"Confirm Password"),t.qZA(),t._UZ(45,"input",36),t.qZA(),t.qZA(),t.TgZ(46,"div",21),t.TgZ(47,"div",28),t.TgZ(48,"a",29),t.NdJ("click",function(){t.CHM(n);const g=t.oxw();return g.step2=!1,g.step3=!0}),t._uU(49,"Finish Setup "),t._UZ(50,"i",37),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA()}}function p(e,o){1&e&&(t.TgZ(0,"section",1),t.TgZ(1,"div",2),t.TgZ(2,"div",38),t.TgZ(3,"div",27),t.TgZ(4,"div",5),t.TgZ(5,"div",6),t._UZ(6,"img",7),t.qZA(),t.TgZ(7,"div",8),t.TgZ(8,"h1",9),t._uU(9,"You\u2019re account is setup "),t._UZ(10,"br"),t._uU(11," Welcome aboard, Dr Mckenzie"),t.qZA(),t.qZA(),t.TgZ(12,"div",21),t.TgZ(13,"div",28),t.TgZ(14,"a",39),t._uU(15,"Go To Dashboard"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(16,"div",10),t._UZ(17,"img",11),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA())}const s=[{path:"",redirectTo:"register",pathMatch:"full"},{path:"register",component:(()=>{class e{constructor(){this.step1=!0,this.step2=!1,this.step3=!1}ngOnInit(){}}return e.\u0275fac=function(n){return new(n||e)},e.\u0275cmp=t.Xpm({type:e,selectors:[["app-doctor-register"]],decls:3,vars:3,consts:[["class","bg-light reg-section",4,"ngIf"],[1,"bg-light","reg-section"],[1,"container-fluid"],[1,"row"],[1,"col-sm-12","col-md-12","col-lg-6"],[1,"register-bannerwrapper","dflexcol","justifycenter","aligncenter"],[1,"brand-logo-wrapper"],["src","./assets/images/logo/logo.png","alt",""],[1,"reg-header"],[1,"text-center"],[1,"reg-imgwrap","dflexrow","justifycenter"],["src","./assets/images/doctor-register.jpg","alt","",1,"frame-wrap"],[1,"col-sm-12","col-md-12","col-lg-6","padd0"],[1,"form-main-container"],[1,"reg-form-section","dflexcol","justifycenter","align-center"],[1,"form-heading-wrap"],[1,"form-input-container"],[1,"col-12","col-sm-6"],[1,"input-group"],[1,"label"],["type","text",1,"form-control"],[1,"col-12"],["type","email",1,"form-control"],[1,"col-sm-12","col-md-12","col-lg-4"],[1,"form-control"],["value",""],[1,"col-sm-12","col-md-12","col-lg-8"],[1,"col-6"],[1,"reg-btn-wrap","dflexrow","justifycenter"],["routerLink","/dashboard",1,"btn","btn-orange",3,"click"],[1,"ri-arrow-right-line"],[1,"col-12","dflexrow","justifycenter"],[1,"selectPic"],[1,"ri-upload-2-line"],["type","file","size","60",1,"selectPic-input"],[1,"upld"],["type","password",1,"form-control"],[1,"ri-check-fill"],[1,"row","justify-content-center"],["routerLink","/doctor",1,"btn","btn-orange"]],template:function(n,r){1&n&&(t.YNc(0,d,79,0,"section",0),t.YNc(1,l,51,0,"section",0),t.YNc(2,p,18,0,"section",0)),2&n&&(t.Q6J("ngIf",r.step1),t.xp6(1),t.Q6J("ngIf",r.step2),t.xp6(1),t.Q6J("ngIf",r.step3))},directives:[a.O5,Z.yS],styles:[".reg-section[_ngcontent-%COMP%]{position:relative}.register-bannerwrapper[_ngcontent-%COMP%]{position:relative;padding:40px}.brand-logo-wrapper[_ngcontent-%COMP%]{position:relative;margin-top:20px}.brand-logo-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:220px}.reg-header[_ngcontent-%COMP%]{position:relative;margin-top:30px}.reg-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;font-weight:900;color:#2fc4ca}.reg-imgwrap[_ngcontent-%COMP%]{position:relative;margin-top:53px;width:100%}.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:85%;max-height:450px;object-fit:revert}.form-main-container[_ngcontent-%COMP%]{padding:40px;background:#F7F7F7;height:100%;border-radius:30px 0 0 30px}.reg-form-section[_ngcontent-%COMP%]{position:relative}.form-heading-wrap[_ngcontent-%COMP%]{margin-top:100px;position:relative}.form-heading-wrap[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;font-weight:900;color:#2fc4ca}.padd0[_ngcontent-%COMP%]{padding:0}.form-input-container[_ngcontent-%COMP%]{position:relative;padding:41px 14.5%}.register-text[_ngcontent-%COMP%]{position:relative;margin-top:8px}.register-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:14px;font-weight:700;line-height:20px}.reg-btn-wrap[_ngcontent-%COMP%]{position:relative;margin-top:28px}@media (min-width: 300px) and (max-width: 768px){.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%}.form-heading-wrap[_ngcontent-%COMP%]{margin-top:0}.form-main-container[_ngcontent-%COMP%]{padding:40px 20px}.form-input-container[_ngcontent-%COMP%]{padding:41px 10px}}@media (min-width: 768px) and (max-width: 1050px){.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%}.form-main-container[_ngcontent-%COMP%]{padding:40px 10px}}.selectPic[_ngcontent-%COMP%]{margin-bottom:30px;position:relative;background:#ffffff;height:116px;width:116px;border-radius:50%;cursor:pointer;display:flex;flex-direction:column;justify-content:center;align-items:center;border:2px solid #E2E2E2}.selectPic-input[type=file][_ngcontent-%COMP%]{display:none}.selectPic[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px}.upld[_ngcontent-%COMP%]{font-size:12px;text-align:center;margin-top:10px}.form-input-container[_ngcontent-%COMP%]{padding:30px 14.5%}"]}),e})()}];let u=(()=>{class e{}return e.\u0275fac=function(n){return new(n||e)},e.\u0275mod=t.oAB({type:e}),e.\u0275inj=t.cJS({imports:[[Z.Bz.forChild(s)],Z.Bz]}),e})(),m=(()=>{class e{}return e.\u0275fac=function(n){return new(n||e)},e.\u0275mod=t.oAB({type:e}),e.\u0275inj=t.cJS({imports:[[a.ez,u]]}),e})()}}]);