"use strict";(self.webpackChunkhealth_teams=self.webpackChunkhealth_teams||[]).push([[467],{9467:(Z,g,o)=>{o.r(g),o.d(g,{RegisterModule:()=>l});var a=o(8583),r=o(4494),t=o(639);const c=[{path:"",component:(()=>{class e{constructor(){this.cities=[],this.selectedCity=[]}ngOnInit(){this.cities=[{name:"New York",code:"NY"},{name:"Rome",code:"RM"},{name:"London",code:"LDN"},{name:"Istanbul",code:"IST"},{name:"Paris",code:"PRS"}]}}return e.\u0275fac=function(n){return new(n||e)},e.\u0275cmp=t.Xpm({type:e,selectors:[["app-register"]],decls:76,vars:0,consts:[[1,"bg-light","reg-section"],[1,"container-fluid"],[1,"row"],[1,"col-sm-12","col-md-12","col-lg-6"],[1,"register-bannerwrapper","dflexcol","justifycenter","aligncenter"],[1,"brand-logo-wrapper"],["src","./assets/images/logo/logo.png","alt",""],[1,"reg-header"],[1,"text-center"],[1,"reg-imgwrap","dflexrow","justifycenter"],["src","./assets/images/frame.jpg","alt","",1,"frame-wrap"],[1,"col-sm-12","col-md-12","col-lg-6","padd0"],[1,"form-main-container"],[1,"reg-form-section","dflexcol","justifycenter","align-center"],[1,"form-heading-wrap"],[1,"form-input-container"],[1,"input-group"],[1,"label"],["type","text",1,"form-control"],[1,"col-sm-12","col-md-12","col-lg-12"],["type","email",1,"form-control"],[1,"col-sm-12","col-md-12","col-lg-4"],[1,"form-control"],["value",""],["src","./assets/images/icons/australia.png","alt",""],[1,"col-sm-12","col-md-12","col-lg-8"],[1,"col-sm-12","col-md-12"],[1,"col-12"],[1,"register-text"],[1,"reg-btn-wrap","dflexrow","justifycenter"],["routerLink","/dashboard",1,"btn","btn-orange"],[1,"ri-arrow-right-line"]],template:function(n,s){1&n&&(t.TgZ(0,"section",0),t.TgZ(1,"div",1),t.TgZ(2,"div",2),t.TgZ(3,"div",3),t.TgZ(4,"div",4),t.TgZ(5,"div",5),t._UZ(6,"img",6),t.qZA(),t.TgZ(7,"div",7),t.TgZ(8,"h1",8),t._uU(9,"Welcome to Health Team"),t.qZA(),t.qZA(),t.TgZ(10,"div",9),t._UZ(11,"img",10),t.qZA(),t.qZA(),t.qZA(),t.TgZ(12,"div",11),t.TgZ(13,"div",12),t.TgZ(14,"div",13),t.TgZ(15,"div",14),t.TgZ(16,"h1",8),t._uU(17,"Register your account"),t.qZA(),t.qZA(),t.qZA(),t.TgZ(18,"div",15),t.TgZ(19,"form"),t.TgZ(20,"div",2),t.TgZ(21,"div",3),t.TgZ(22,"div",16),t.TgZ(23,"label",17),t._uU(24,"First Name"),t.qZA(),t._UZ(25,"input",18),t.qZA(),t.qZA(),t.TgZ(26,"div",3),t.TgZ(27,"div",16),t.TgZ(28,"label",17),t._uU(29,"Last Name"),t.qZA(),t._UZ(30,"input",18),t.qZA(),t.qZA(),t.TgZ(31,"div",19),t.TgZ(32,"div",16),t.TgZ(33,"label",17),t._uU(34,"Email Address"),t.qZA(),t._UZ(35,"input",20),t.qZA(),t.qZA(),t.TgZ(36,"div",21),t.TgZ(37,"div",16),t.TgZ(38,"label",17),t._uU(39,"Country"),t.qZA(),t.TgZ(40,"select",22),t.TgZ(41,"option",23),t._UZ(42,"img",24),t.TgZ(43,"span"),t._uU(44,"Australia"),t.qZA(),t.qZA(),t.TgZ(45,"option",23),t._uU(46,"India"),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.TgZ(47,"div",25),t.TgZ(48,"div",16),t.TgZ(49,"label",17),t._uU(50,"Mobile Number"),t.qZA(),t._UZ(51,"input",18),t.qZA(),t.qZA(),t.TgZ(52,"div",3),t.TgZ(53,"div",16),t.TgZ(54,"label",17),t._uU(55,"6 DIgit Connection Code"),t.qZA(),t._UZ(56,"input",18),t.qZA(),t.qZA(),t.TgZ(57,"div",3),t.TgZ(58,"div",16),t.TgZ(59,"label",17),t._uU(60,"Connected Facility"),t.qZA(),t._UZ(61,"input",18),t.qZA(),t.qZA(),t.TgZ(62,"div",26),t.TgZ(63,"div",16),t.TgZ(64,"label",17),t._uU(65,"HPI Number"),t.qZA(),t._UZ(66,"input",18),t.qZA(),t.qZA(),t.TgZ(67,"div",27),t.TgZ(68,"div",28),t.TgZ(69,"h2"),t._uU(70,"This will be your primary account. You can connect to additional Facilities within this account later."),t.qZA(),t.qZA(),t.qZA(),t.TgZ(71,"div",27),t.TgZ(72,"div",29),t.TgZ(73,"a",30),t._uU(74,"Next "),t._UZ(75,"i",31),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA(),t.qZA())},directives:[r.yS],styles:[".reg-section[_ngcontent-%COMP%]{position:relative}.register-bannerwrapper[_ngcontent-%COMP%]{position:relative;padding:40px}.brand-logo-wrapper[_ngcontent-%COMP%]{position:relative;margin-top:20px}.brand-logo-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:220px}.reg-header[_ngcontent-%COMP%]{position:relative;margin-top:30px}.reg-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;font-weight:900;color:#2fc4ca}.reg-imgwrap[_ngcontent-%COMP%]{position:relative;margin-top:53px;width:100%}.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:85%}.form-main-container[_ngcontent-%COMP%]{padding:40px;background:#F7F7F7;height:100%;border-radius:30px 0 0 30px}.reg-form-section[_ngcontent-%COMP%]{position:relative}.form-heading-wrap[_ngcontent-%COMP%]{margin-top:100px;position:relative}.form-heading-wrap[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;font-weight:900;color:#2fc4ca}.padd0[_ngcontent-%COMP%]{padding:0}.form-input-container[_ngcontent-%COMP%]{position:relative;padding:41px 14.5%}.register-text[_ngcontent-%COMP%]{position:relative;margin-top:8px}.register-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:14px;font-weight:700;line-height:20px}.reg-btn-wrap[_ngcontent-%COMP%]{position:relative;margin-top:28px}@media (min-width: 300px) and (max-width: 768px){.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%}.form-heading-wrap[_ngcontent-%COMP%]{margin-top:0}.form-main-container[_ngcontent-%COMP%]{padding:40px 20px}.form-input-container[_ngcontent-%COMP%]{padding:41px 10px}}@media (min-width: 768px) and (max-width: 1050px){.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%}.form-main-container[_ngcontent-%COMP%]{padding:40px 10px}}"]}),e})()}];let d=(()=>{class e{}return e.\u0275fac=function(n){return new(n||e)},e.\u0275mod=t.oAB({type:e}),e.\u0275inj=t.cJS({imports:[[r.Bz.forChild(c)],r.Bz]}),e})(),l=(()=>{class e{}return e.\u0275fac=function(n){return new(n||e)},e.\u0275mod=t.oAB({type:e}),e.\u0275inj=t.cJS({imports:[[a.ez,d]]}),e})()}}]);