"use strict";(self.webpackChunkhealth_teams=self.webpackChunkhealth_teams||[]).push([[165],{2165:(b,r,g)=>{g.r(r),g.d(r,{LoginModule:()=>h});var c=g(8583),a=g(4494),n=g(639);function l(t,e){1&t&&(n.TgZ(0,"button",37),n._uU(1,"Login "),n._UZ(2,"i",38),n.qZA())}function p(t,e){1&t&&(n.TgZ(0,"a",39),n._uU(1,"Login "),n._UZ(2,"i",38),n.qZA())}function d(t,e){1&t&&(n.TgZ(0,"a",40),n._uU(1,"Login "),n._UZ(2,"i",38),n.qZA())}function s(t,e){1&t&&(n.TgZ(0,"a",41),n._u<PERSON>(1,"Login "),n._UZ(2,"i",38),n.qZA())}function m(t,e){1&t&&(n.TgZ(0,"a",42),n._uU(1,"Login "),n._UZ(2,"i",38),n.qZA())}function u(t,e){1&t&&(n.TgZ(0,"a",43),n._uU(1,"Login "),n._UZ(2,"i",38),n.qZA())}const f=[{path:"",redirectTo:"login",pathMatch:"full"},{path:"login",component:(()=>{class t{constructor(o){this.router=o,this.navigate=i=>{this.selectedModule=i.target.value}}ngOnInit(){}}return t.\u0275fac=function(o){return new(o||t)(n.Y36(a.F0))},t.\u0275cmp=n.Xpm({type:t,selectors:[["app-login"]],decls:57,vars:8,consts:[[1,"bg-gray","reg-section"],[1,"container-fluid"],[1,"row","justify-content-center"],[1,"col-sm-12","col-md-12","col-lg-6","padd0"],[1,"form-main-container"],[1,"brand-logo-wrapper"],["src","./assets/images/logo/logo.png","alt",""],[1,"reg-form-section","dflexcol","justifycenter","align-center"],[1,"form-heading-wrap"],[1,"text-center"],[1,"form-input-container"],[1,"row"],[1,"col-sm-12","col-md-12","col-lg-12"],[1,"input-group"],[1,"label"],["type","email",1,"form-control"],["type","password",1,"form-control"],[1,"col-12"],[1,"form-control",3,"change"],["value","",3,"selected","disabled"],["value","doctor"],["value","nurse"],["value","facility"],["value","resident"],["value","family"],[1,"rememberme"],[1,"checkbox"],["type","checkbox","name","Type"],[1,"checboxcheckmark"],[1,"reg-btn-wrap","dflexrow","justifycenter"],["class","btn btn-orange",4,"ngIf"],["class","btn btn-orange","href","doctor-auth",4,"ngIf"],["class","btn btn-orange","href","facility-auth",4,"ngIf"],["class","btn btn-orange","href","patient-auth",4,"ngIf"],["class","btn btn-orange","href","family-auth",4,"ngIf"],["class","btn btn-orange","href","nurse-auth",4,"ngIf"],["src","./assets/images/login-mobile.png","alt","",1,"login-bottom-image"],[1,"btn","btn-orange"],[1,"ri-arrow-right-line"],["href","doctor-auth",1,"btn","btn-orange"],["href","facility-auth",1,"btn","btn-orange"],["href","patient-auth",1,"btn","btn-orange"],["href","family-auth",1,"btn","btn-orange"],["href","nurse-auth",1,"btn","btn-orange"]],template:function(o,i){1&o&&(n.TgZ(0,"section",0),n.TgZ(1,"div",1),n.TgZ(2,"div",2),n.TgZ(3,"div",3),n.TgZ(4,"div",4),n.TgZ(5,"div",5),n._UZ(6,"img",6),n.qZA(),n.TgZ(7,"div",7),n.TgZ(8,"div",8),n.TgZ(9,"h1",9),n._uU(10,"Welcome to Health Teams"),n.qZA(),n.qZA(),n.qZA(),n.TgZ(11,"div",10),n.TgZ(12,"form"),n.TgZ(13,"div",11),n.TgZ(14,"div",12),n.TgZ(15,"div",13),n.TgZ(16,"label",14),n._uU(17,"Email Address"),n.qZA(),n._UZ(18,"input",15),n.qZA(),n.qZA(),n.TgZ(19,"div",12),n.TgZ(20,"div",13),n.TgZ(21,"label",14),n._uU(22,"Password"),n.qZA(),n._UZ(23,"input",16),n.qZA(),n.qZA(),n.TgZ(24,"div",17),n.TgZ(25,"div",13),n.TgZ(26,"label",14),n._uU(27,"Role"),n.qZA(),n.TgZ(28,"select",18),n.NdJ("change",function(_){return i.navigate(_)}),n.TgZ(29,"option",19),n._uU(30,"Select Role"),n.qZA(),n.TgZ(31,"option",20),n._uU(32,"Doctor"),n.qZA(),n.TgZ(33,"option",21),n._uU(34,"Nurse"),n.qZA(),n.TgZ(35,"option",22),n._uU(36,"Facility"),n.qZA(),n.TgZ(37,"option",23),n._uU(38,"Resident"),n.qZA(),n.TgZ(39,"option",24),n._uU(40,"Family"),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.TgZ(41,"div",17),n.TgZ(42,"div",25),n.TgZ(43,"label",26),n._uU(44,"Remember me on this device "),n._UZ(45,"input",27),n._UZ(46,"span",28),n.qZA(),n.qZA(),n.qZA(),n.TgZ(47,"div",17),n.TgZ(48,"div",29),n.YNc(49,l,3,0,"button",30),n.YNc(50,p,3,0,"a",31),n.YNc(51,d,3,0,"a",32),n.YNc(52,s,3,0,"a",33),n.YNc(53,m,3,0,"a",34),n.YNc(54,u,3,0,"a",35),n.qZA(),n.qZA(),n.TgZ(55,"div",17),n._UZ(56,"img",36),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA()),2&o&&(n.xp6(29),n.Q6J("selected",!0)("disabled",!0),n.xp6(20),n.Q6J("ngIf",!i.selectedModule),n.xp6(1),n.Q6J("ngIf","doctor"==i.selectedModule),n.xp6(1),n.Q6J("ngIf","facility"==i.selectedModule),n.xp6(1),n.Q6J("ngIf","resident"==i.selectedModule),n.xp6(1),n.Q6J("ngIf","family"==i.selectedModule),n.xp6(1),n.Q6J("ngIf","nurse"==i.selectedModule))},directives:[c.O5],styles:[".reg-section[_ngcontent-%COMP%]{position:relative}.register-bannerwrapper[_ngcontent-%COMP%]{position:relative;padding:40px 40px 0}.brand-logo-wrapper[_ngcontent-%COMP%]{position:relative;margin-top:20px;display:flex;justify-content:center}.brand-logo-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:220px}.reg-header[_ngcontent-%COMP%]{position:relative;margin-top:30px}.reg-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;font-weight:900;color:#2fc4ca}.reg-imgwrap[_ngcontent-%COMP%]{position:relative;margin-top:53px;width:100%}.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:85%}.form-main-container[_ngcontent-%COMP%]{padding:40px 40px 0;background:#F7F7F7;height:100%;border-radius:30px 0 0 30px}.reg-form-section[_ngcontent-%COMP%]{position:relative}.form-heading-wrap[_ngcontent-%COMP%]{margin-top:30px;position:relative}.form-heading-wrap[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;font-weight:900;color:#2fc4ca}.padd0[_ngcontent-%COMP%]{padding:0}.form-input-container[_ngcontent-%COMP%]{position:relative;padding:41px 14.5% 0;min-height:100vh}.register-text[_ngcontent-%COMP%]{position:relative;margin-top:8px}.register-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:14px;font-weight:700;line-height:20px}.reg-btn-wrap[_ngcontent-%COMP%]{position:relative;margin-top:28px}@media (min-width: 300px) and (max-width: 768px){.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%}.form-heading-wrap[_ngcontent-%COMP%]{margin-top:0}.form-main-container[_ngcontent-%COMP%]{padding:40px 20px}.form-input-container[_ngcontent-%COMP%]{padding:41px 10px}}@media (min-width: 768px) and (max-width: 1050px){.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%}.form-main-container[_ngcontent-%COMP%]{padding:40px 10px}}.rememberme[_ngcontent-%COMP%]{display:flex;justify-content:center}.checkbox[_ngcontent-%COMP%]{padding-left:30px}.login-bottom-image[_ngcontent-%COMP%]{position:absolute;background:#f7f7f7;left:0;right:0}"]}),t})()}];let Z=(()=>{class t{}return t.\u0275fac=function(o){return new(o||t)},t.\u0275mod=n.oAB({type:t}),t.\u0275inj=n.cJS({imports:[[a.Bz.forChild(f)],a.Bz]}),t})(),h=(()=>{class t{}return t.\u0275fac=function(o){return new(o||t)},t.\u0275mod=n.oAB({type:t}),t.\u0275inj=n.cJS({imports:[[c.ez,Z]]}),t})()}}]);