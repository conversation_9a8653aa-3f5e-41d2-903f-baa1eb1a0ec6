"use strict";(self.webpackChunkhealth_teams=self.webpackChunkhealth_teams||[]).push([[304],{1304:(T,c,o)=>{o.r(c),o.d(c,{NurseAuthModule:()=>m});var a=o(8583),Z=o(4494),e=o(639);function s(t,i){if(1&t){const n=e.EpF();e.TgZ(0,"section",1),e.TgZ(1,"div",2),e.TgZ(2,"div",3),e.TgZ(3,"div",4),e.TgZ(4,"div",5),e.TgZ(5,"div",6),e._<PERSON><PERSON>(6,"img",7),e.qZ<PERSON>(),e.Tg<PERSON>(7,"div",8),e.TgZ(8,"h1",9),e._u<PERSON>(9,"Welcome to Health Teams"),e.q<PERSON><PERSON>(),e.q<PERSON><PERSON>(),e.Tg<PERSON>(10,"div",10),e._<PERSON><PERSON>(11,"img",11),e.q<PERSON><PERSON>(),e.q<PERSON><PERSON>(),e.q<PERSON><PERSON>(),e.Tg<PERSON>(12,"div",12),e.TgZ(13,"div",13),e.TgZ(14,"div",14),e.TgZ(15,"div",15),e.TgZ(16,"h1",9),e._uU(17,"Register your account "),e.qZA(),e.qZA(),e.qZA(),e.TgZ(18,"div",16),e.TgZ(19,"form"),e.TgZ(20,"div",3),e.TgZ(21,"div",17),e.TgZ(22,"div",18),e.TgZ(23,"label",19),e._uU(24,"First Name"),e.qZA(),e._UZ(25,"input",20),e.qZA(),e.qZA(),e.TgZ(26,"div",17),e.TgZ(27,"div",18),e.TgZ(28,"label",19),e._uU(29,"Last Name"),e.qZA(),e._UZ(30,"input",20),e.qZA(),e.qZA(),e.TgZ(31,"div",21),e.TgZ(32,"div",18),e.TgZ(33,"label",19),e._uU(34,"Email Address"),e.qZA(),e._UZ(35,"input",22),e.qZA(),e.qZA(),e.TgZ(36,"div",23),e.TgZ(37,"div",18),e.TgZ(38,"label",19),e._uU(39,"Country"),e.qZA(),e.TgZ(40,"select",24),e.TgZ(41,"option",25),e._uU(42,"+61"),e.qZA(),e.TgZ(43,"option",25),e._uU(44,"+91"),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.TgZ(45,"div",26),e.TgZ(46,"div",18),e.TgZ(47,"label",19),e._uU(48,"Mobile Number"),e.qZA(),e._UZ(49,"input",20),e.qZA(),e.qZA(),e.TgZ(50,"div",21),e.TgZ(51,"div",18),e.TgZ(52,"label",19),e._uU(53,"HPI Number"),e.qZA(),e._UZ(54,"input",20),e.qZA(),e.qZA(),e.TgZ(55,"div",21),e.TgZ(56,"div",18),e.TgZ(57,"p"),e._uU(58,"If you have a connection code, enter it here, if not, you can connect to Facilities later."),e.qZA(),e.qZA(),e.qZA(),e.TgZ(59,"div",27),e.TgZ(60,"div",18),e.TgZ(61,"label",19),e._uU(62,"6 Digit Connection Code"),e.qZA(),e._UZ(63,"input",20),e.qZA(),e.qZA(),e.TgZ(64,"div",27),e.TgZ(65,"div",18),e.TgZ(66,"label",19),e._uU(67,"Connected Facility"),e.qZA(),e._UZ(68,"input",20),e.qZA(),e.qZA(),e.TgZ(69,"div",21),e.TgZ(70,"div",28),e.TgZ(71,"a",29),e.NdJ("click",function(){e.CHM(n);const g=e.oxw();return g.step1=!1,g.step2=!0}),e._uU(72,"Next "),e._UZ(73,"i",30),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.qZA()}}function l(t,i){if(1&t){const n=e.EpF();e.TgZ(0,"section",1),e.TgZ(1,"div",2),e.TgZ(2,"div",3),e.TgZ(3,"div",4),e.TgZ(4,"div",5),e.TgZ(5,"div",6),e._UZ(6,"img",7),e.qZA(),e.TgZ(7,"div",8),e.TgZ(8,"h1",9),e._uU(9,"Hello Tristan, "),e._UZ(10,"br"),e._uU(11," Welcome to Health Teams"),e.qZA(),e.qZA(),e.TgZ(12,"div",10),e._UZ(13,"img",11),e.qZA(),e.qZA(),e.qZA(),e.TgZ(14,"div",12),e.TgZ(15,"div",13),e.TgZ(16,"div",14),e.TgZ(17,"div",15),e.TgZ(18,"h1",9),e._uU(19,"Now let\u2019s set "),e._UZ(20,"br"),e._uU(21," your login details"),e.qZA(),e.qZA(),e.qZA(),e.TgZ(22,"div",16),e.TgZ(23,"form"),e.TgZ(24,"div",3),e.TgZ(25,"div",31),e.TgZ(26,"label",32),e._UZ(27,"i",33),e._UZ(28,"input",34),e.TgZ(29,"span",35),e._uU(30,"Upload "),e._UZ(31,"br"),e._uU(32," Profile Photo"),e.qZA(),e.qZA(),e.qZA(),e.TgZ(33,"div",21),e.TgZ(34,"div",18),e.TgZ(35,"label",19),e._uU(36,"Email Address"),e.qZA(),e._UZ(37,"input",22),e.qZA(),e.qZA(),e.TgZ(38,"div",21),e.TgZ(39,"div",18),e.TgZ(40,"label",19),e._uU(41,"Password"),e.qZA(),e._UZ(42,"input",36),e.qZA(),e.qZA(),e.TgZ(43,"div",21),e.TgZ(44,"div",18),e.TgZ(45,"label",19),e._uU(46,"Confirm Password"),e.qZA(),e._UZ(47,"input",36),e.qZA(),e.qZA(),e.TgZ(48,"div",21),e.TgZ(49,"div",28),e.TgZ(50,"a",29),e.NdJ("click",function(){e.CHM(n);const g=e.oxw();return g.step2=!1,g.step3=!0}),e._uU(51,"Finish Setup "),e._UZ(52,"i",37),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.qZA()}}function p(t,i){1&t&&(e.TgZ(0,"section",1),e.TgZ(1,"div",2),e.TgZ(2,"div",38),e.TgZ(3,"div",27),e.TgZ(4,"div",5),e.TgZ(5,"div",6),e._UZ(6,"img",7),e.qZA(),e.TgZ(7,"div",8),e.TgZ(8,"h1",9),e._uU(9,"You\u2019re account is setup "),e._UZ(10,"br"),e._uU(11," Welcome aboard, Tristan"),e.qZA(),e.qZA(),e.TgZ(12,"div",21),e.TgZ(13,"div",28),e.TgZ(14,"a",39),e._uU(15,"Go To Dashboard"),e.qZA(),e.qZA(),e.qZA(),e.TgZ(16,"div",10),e._UZ(17,"img",40),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.qZA(),e.qZA())}const d=[{path:"",redirectTo:"register",pathMatch:"full"},{path:"register",component:(()=>{class t{constructor(){this.step1=!0,this.step2=!1,this.step3=!1}ngOnInit(){}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275cmp=e.Xpm({type:t,selectors:[["app-register-nurse"]],decls:3,vars:3,consts:[["class","bg-light reg-section",4,"ngIf"],[1,"bg-light","reg-section"],[1,"container-fluid"],[1,"row"],[1,"col-sm-12","col-md-12","col-lg-6"],[1,"register-bannerwrapper","dflexcol","justifycenter","aligncenter"],[1,"brand-logo-wrapper"],["src","./assets/images/logo/logo.png","alt",""],[1,"reg-header"],[1,"text-center"],[1,"reg-imgwrap","dflexrow","justifycenter"],["src","./assets/images/frame.jpg","alt","",1,"frame-wrap"],[1,"col-sm-12","col-md-12","col-lg-6","padd0"],[1,"form-main-container"],[1,"reg-form-section","dflexcol","justifycenter","align-center"],[1,"form-heading-wrap"],[1,"form-input-container"],[1,"col-12","col-sm-6"],[1,"input-group"],[1,"label"],["type","text",1,"form-control"],[1,"col-12"],["type","email",1,"form-control"],[1,"col-sm-12","col-md-12","col-lg-4"],[1,"form-control"],["value",""],[1,"col-sm-12","col-md-12","col-lg-8"],[1,"col-6"],[1,"reg-btn-wrap","dflexrow","justifycenter"],["routerLink","/dashboard",1,"btn","btn-orange",3,"click"],[1,"ri-arrow-right-line"],[1,"col-12","dflexrow","justifycenter"],[1,"selectPic"],[1,"ri-upload-2-line"],["type","file","size","60",1,"selectPic-input"],[1,"upld"],["type","password",1,"form-control"],[1,"ri-check-fill"],[1,"row","justify-content-center"],["routerLink","/nurse",1,"btn","btn-orange"],["src","./assets/images/group-2.png","alt","",1,"frame-wrap"]],template:function(n,r){1&n&&(e.YNc(0,s,74,0,"section",0),e.YNc(1,l,53,0,"section",0),e.YNc(2,p,18,0,"section",0)),2&n&&(e.Q6J("ngIf",r.step1),e.xp6(1),e.Q6J("ngIf",r.step2),e.xp6(1),e.Q6J("ngIf",r.step3))},directives:[a.O5,Z.yS],styles:[".reg-section[_ngcontent-%COMP%]{position:relative}.register-bannerwrapper[_ngcontent-%COMP%]{position:relative;padding:40px}.brand-logo-wrapper[_ngcontent-%COMP%]{position:relative;margin-top:20px}.brand-logo-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:220px}.reg-header[_ngcontent-%COMP%]{position:relative;margin-top:30px}.reg-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;font-weight:900;color:#2fc4ca}.reg-imgwrap[_ngcontent-%COMP%]{position:relative;margin-top:53px;width:100%}.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:85%;max-height:450px;object-fit:revert}.form-main-container[_ngcontent-%COMP%]{padding:40px;background:#F7F7F7;height:100%;border-radius:30px 0 0 30px}.reg-form-section[_ngcontent-%COMP%]{position:relative}.form-heading-wrap[_ngcontent-%COMP%]{margin-top:100px;position:relative}.form-heading-wrap[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;font-weight:900;color:#2fc4ca}.padd0[_ngcontent-%COMP%]{padding:0}.form-input-container[_ngcontent-%COMP%]{position:relative;padding:41px 14.5%;min-height:100vh}.register-text[_ngcontent-%COMP%]{position:relative;margin-top:8px}.register-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:14px;font-weight:700;line-height:20px}.reg-btn-wrap[_ngcontent-%COMP%]{position:relative;margin-top:28px}@media (min-width: 300px) and (max-width: 768px){.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%}.form-heading-wrap[_ngcontent-%COMP%]{margin-top:0}.form-main-container[_ngcontent-%COMP%]{padding:40px 20px}.form-input-container[_ngcontent-%COMP%]{padding:41px 10px}}@media (min-width: 768px) and (max-width: 1050px){.reg-imgwrap[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%}.form-main-container[_ngcontent-%COMP%]{padding:40px 10px}}.selectPic[_ngcontent-%COMP%]{margin-bottom:30px;position:relative;background:#ffffff;height:116px;width:116px;border-radius:50%;cursor:pointer;display:flex;flex-direction:column;justify-content:center;align-items:center;border:2px solid #E2E2E2}.selectPic-input[type=file][_ngcontent-%COMP%]{display:none}.selectPic[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px}.upld[_ngcontent-%COMP%]{font-size:12px;text-align:center;margin-top:10px}.form-input-container[_ngcontent-%COMP%]{padding:30px 14.5%}"]}),t})()}];let u=(()=>{class t{}return t.\u0275fac=function(n){return new(n||t)},t.\u0275mod=e.oAB({type:t}),t.\u0275inj=e.cJS({imports:[[Z.Bz.forChild(d)],Z.Bz]}),t})(),m=(()=>{class t{}return t.\u0275fac=function(n){return new(n||t)},t.\u0275mod=e.oAB({type:t}),t.\u0275inj=e.cJS({imports:[[a.ez,u]]}),t})()}}]);