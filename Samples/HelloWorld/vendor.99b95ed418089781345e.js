"use strict";(self.webpackChunkhelloworld=self.webpackChunkhelloworld||[]).push([[736],{583:(Ye,ie,F)=>{F.d(ie,{mr:()=>we,ez:()=>Po,K0:()=>oe,Do:()=>Gt,V_:()=>L,Ye:()=>gn,S$:()=>Te,RF:()=>Ot,n9:()=>ze,ED:()=>Fi,b0:()=>te,lw:()=>fe,EM:()=>Oo,JF:()=>ci,w_:()=>W,bD:()=>Ur,q:()=>ae,Mx:()=>B,HT:()=>De});var I=F(639);let b=null;function ae(){return b}function De(p){b||(b=p)}class W{}const oe=new I.OlP("DocumentToken");let fe=(()=>{class p{historyGo(g){throw new Error("Not implemented")}}return p.\u0275fac=function(g){return new(g||p)},p.\u0275prov=(0,I.Yz7)({factory:q,token:p,providedIn:"platform"}),p})();function q(){return(0,I.LFG)(V)}const L=new I.OlP("Location Initialized");let V=(()=>{class p extends fe{constructor(g){super(),this._doc=g,this._init()}_init(){this.location=window.location,this._history=window.history}getBaseHrefFromDOM(){return ae().getBaseHref(this._doc)}onPopState(g){const E=ae().getGlobalEventTarget(this._doc,"window");return E.addEventListener("popstate",g,!1),()=>E.removeEventListener("popstate",g)}onHashChange(g){const E=ae().getGlobalEventTarget(this._doc,"window");return E.addEventListener("hashchange",g,!1),()=>E.removeEventListener("hashchange",g)}get href(){return this.location.href}get protocol(){return this.location.protocol}get hostname(){return this.location.hostname}get port(){return this.location.port}get pathname(){return this.location.pathname}get search(){return this.location.search}get hash(){return this.location.hash}set pathname(g){this.location.pathname=g}pushState(g,E,M){H()?this._history.pushState(g,E,M):this.location.hash=M}replaceState(g,E,M){H()?this._history.replaceState(g,E,M):this.location.hash=M}forward(){this._history.forward()}back(){this._history.back()}historyGo(g=0){this._history.go(g)}getState(){return this._history.state}}return p.\u0275fac=function(g){return new(g||p)(I.LFG(oe))},p.\u0275prov=(0,I.Yz7)({factory:Q,token:p,providedIn:"platform"}),p})();function H(){return!!window.history.pushState}function Q(){return new V((0,I.LFG)(oe))}function Z(p,D){if(0==p.length)return D;if(0==D.length)return p;let g=0;return p.endsWith("/")&&g++,D.startsWith("/")&&g++,2==g?p+D.substring(1):1==g?p+D:p+"/"+D}function X(p){const D=p.match(/#|\?|$/),g=D&&D.index||p.length;return p.slice(0,g-("/"===p[g-1]?1:0))+p.slice(g)}function G(p){return p&&"?"!==p[0]?"?"+p:p}let Te=(()=>{class p{historyGo(g){throw new Error("Not implemented")}}return p.\u0275fac=function(g){return new(g||p)},p.\u0275prov=(0,I.Yz7)({factory:qe,token:p,providedIn:"root"}),p})();function qe(p){const D=(0,I.LFG)(oe).location;return new te((0,I.LFG)(fe),D&&D.origin||"")}const we=new I.OlP("appBaseHref");let te=(()=>{class p extends Te{constructor(g,E){if(super(),this._platformLocation=g,this._removeListenerFns=[],null==E&&(E=this._platformLocation.getBaseHrefFromDOM()),null==E)throw new Error("No base href set. Please provide a value for the APP_BASE_HREF token or add a base element to the document.");this._baseHref=E}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(g){this._removeListenerFns.push(this._platformLocation.onPopState(g),this._platformLocation.onHashChange(g))}getBaseHref(){return this._baseHref}prepareExternalUrl(g){return Z(this._baseHref,g)}path(g=!1){const E=this._platformLocation.pathname+G(this._platformLocation.search),M=this._platformLocation.hash;return M&&g?`${E}${M}`:E}pushState(g,E,M,j){const Y=this.prepareExternalUrl(M+G(j));this._platformLocation.pushState(g,E,Y)}replaceState(g,E,M,j){const Y=this.prepareExternalUrl(M+G(j));this._platformLocation.replaceState(g,E,Y)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}historyGo(g=0){var E,M;null===(M=(E=this._platformLocation).historyGo)||void 0===M||M.call(E,g)}}return p.\u0275fac=function(g){return new(g||p)(I.LFG(fe),I.LFG(we,8))},p.\u0275prov=I.Yz7({token:p,factory:p.\u0275fac}),p})(),Gt=(()=>{class p extends Te{constructor(g,E){super(),this._platformLocation=g,this._baseHref="",this._removeListenerFns=[],null!=E&&(this._baseHref=E)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(g){this._removeListenerFns.push(this._platformLocation.onPopState(g),this._platformLocation.onHashChange(g))}getBaseHref(){return this._baseHref}path(g=!1){let E=this._platformLocation.hash;return null==E&&(E="#"),E.length>0?E.substring(1):E}prepareExternalUrl(g){const E=Z(this._baseHref,g);return E.length>0?"#"+E:E}pushState(g,E,M,j){let Y=this.prepareExternalUrl(M+G(j));0==Y.length&&(Y=this._platformLocation.pathname),this._platformLocation.pushState(g,E,Y)}replaceState(g,E,M,j){let Y=this.prepareExternalUrl(M+G(j));0==Y.length&&(Y=this._platformLocation.pathname),this._platformLocation.replaceState(g,E,Y)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}historyGo(g=0){var E,M;null===(M=(E=this._platformLocation).historyGo)||void 0===M||M.call(E,g)}}return p.\u0275fac=function(g){return new(g||p)(I.LFG(fe),I.LFG(we,8))},p.\u0275prov=I.Yz7({token:p,factory:p.\u0275fac}),p})(),gn=(()=>{class p{constructor(g,E){this._subject=new I.vpe,this._urlChangeListeners=[],this._platformStrategy=g;const M=this._platformStrategy.getBaseHref();this._platformLocation=E,this._baseHref=X(wn(M)),this._platformStrategy.onPopState(j=>{this._subject.emit({url:this.path(!0),pop:!0,state:j.state,type:j.type})})}path(g=!1){return this.normalize(this._platformStrategy.path(g))}getState(){return this._platformLocation.getState()}isCurrentPathEqualTo(g,E=""){return this.path()==this.normalize(g+G(E))}normalize(g){return p.stripTrailingSlash(function(p,D){return p&&D.startsWith(p)?D.substring(p.length):D}(this._baseHref,wn(g)))}prepareExternalUrl(g){return g&&"/"!==g[0]&&(g="/"+g),this._platformStrategy.prepareExternalUrl(g)}go(g,E="",M=null){this._platformStrategy.pushState(M,"",g,E),this._notifyUrlChangeListeners(this.prepareExternalUrl(g+G(E)),M)}replaceState(g,E="",M=null){this._platformStrategy.replaceState(M,"",g,E),this._notifyUrlChangeListeners(this.prepareExternalUrl(g+G(E)),M)}forward(){this._platformStrategy.forward()}back(){this._platformStrategy.back()}historyGo(g=0){var E,M;null===(M=(E=this._platformStrategy).historyGo)||void 0===M||M.call(E,g)}onUrlChange(g){this._urlChangeListeners.push(g),this._urlChangeSubscription||(this._urlChangeSubscription=this.subscribe(E=>{this._notifyUrlChangeListeners(E.url,E.state)}))}_notifyUrlChangeListeners(g="",E){this._urlChangeListeners.forEach(M=>M(g,E))}subscribe(g,E,M){return this._subject.subscribe({next:g,error:E,complete:M})}}return p.\u0275fac=function(g){return new(g||p)(I.LFG(Te),I.LFG(fe))},p.normalizeQueryParams=G,p.joinWithSlash=Z,p.stripTrailingSlash=X,p.\u0275prov=(0,I.Yz7)({factory:ft,token:p,providedIn:"root"}),p})();function ft(){return new gn((0,I.LFG)(Te),(0,I.LFG)(fe))}function wn(p){return p.replace(/\/index.html$/,"")}var et=(()=>((et=et||{})[et.Zero=0]="Zero",et[et.One=1]="One",et[et.Two=2]="Two",et[et.Few=3]="Few",et[et.Many=4]="Many",et[et.Other=5]="Other",et))();const ut=I.kL8;class P{}let w=(()=>{class p extends P{constructor(g){super(),this.locale=g}getPluralCategory(g,E){switch(ut(E||this.locale)(g)){case et.Zero:return"zero";case et.One:return"one";case et.Two:return"two";case et.Few:return"few";case et.Many:return"many";default:return"other"}}}return p.\u0275fac=function(g){return new(g||p)(I.LFG(I.soG))},p.\u0275prov=I.Yz7({token:p,factory:p.\u0275fac}),p})();function B(p,D){D=encodeURIComponent(D);for(const g of p.split(";")){const E=g.indexOf("="),[M,j]=-1==E?[g,""]:[g.slice(0,E),g.slice(E+1)];if(M.trim()===D)return decodeURIComponent(j)}return null}class Nt{constructor(D,g){this._viewContainerRef=D,this._templateRef=g,this._created=!1}create(){this._created=!0,this._viewContainerRef.createEmbeddedView(this._templateRef)}destroy(){this._created=!1,this._viewContainerRef.clear()}enforceState(D){D&&!this._created?this.create():!D&&this._created&&this.destroy()}}let Ot=(()=>{class p{constructor(){this._defaultUsed=!1,this._caseCount=0,this._lastCaseCheckIndex=0,this._lastCasesMatched=!1}set ngSwitch(g){this._ngSwitch=g,0===this._caseCount&&this._updateDefaultCases(!0)}_addCase(){return this._caseCount++}_addDefault(g){this._defaultViews||(this._defaultViews=[]),this._defaultViews.push(g)}_matchCase(g){const E=g==this._ngSwitch;return this._lastCasesMatched=this._lastCasesMatched||E,this._lastCaseCheckIndex++,this._lastCaseCheckIndex===this._caseCount&&(this._updateDefaultCases(!this._lastCasesMatched),this._lastCaseCheckIndex=0,this._lastCasesMatched=!1),E}_updateDefaultCases(g){if(this._defaultViews&&g!==this._defaultUsed){this._defaultUsed=g;for(let E=0;E<this._defaultViews.length;E++)this._defaultViews[E].enforceState(g)}}}return p.\u0275fac=function(g){return new(g||p)},p.\u0275dir=I.lG2({type:p,selectors:[["","ngSwitch",""]],inputs:{ngSwitch:"ngSwitch"}}),p})(),ze=(()=>{class p{constructor(g,E,M){this.ngSwitch=M,M._addCase(),this._view=new Nt(g,E)}ngDoCheck(){this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase))}}return p.\u0275fac=function(g){return new(g||p)(I.Y36(I.s_b),I.Y36(I.Rgc),I.Y36(Ot,9))},p.\u0275dir=I.lG2({type:p,selectors:[["","ngSwitchCase",""]],inputs:{ngSwitchCase:"ngSwitchCase"}}),p})(),Fi=(()=>{class p{constructor(g,E,M){M._addDefault(new Nt(g,E))}}return p.\u0275fac=function(g){return new(g||p)(I.Y36(I.s_b),I.Y36(I.Rgc),I.Y36(Ot,9))},p.\u0275dir=I.lG2({type:p,selectors:[["","ngSwitchDefault",""]]}),p})(),Po=(()=>{class p{}return p.\u0275fac=function(g){return new(g||p)},p.\u0275mod=I.oAB({type:p}),p.\u0275inj=I.cJS({providers:[{provide:P,useClass:w}]}),p})();const Ur="browser";let Oo=(()=>{class p{}return p.\u0275prov=(0,I.Yz7)({token:p,providedIn:"root",factory:()=>new ui((0,I.LFG)(oe),window)}),p})();class ui{constructor(D,g){this.document=D,this.window=g,this.offset=()=>[0,0]}setOffset(D){this.offset=Array.isArray(D)?()=>D:D}getScrollPosition(){return this.supportsScrolling()?[this.window.pageXOffset,this.window.pageYOffset]:[0,0]}scrollToPosition(D){this.supportsScrolling()&&this.window.scrollTo(D[0],D[1])}scrollToAnchor(D){if(!this.supportsScrolling())return;const g=function(p,D){const g=p.getElementById(D)||p.getElementsByName(D)[0];if(g)return g;if("function"==typeof p.createTreeWalker&&p.body&&(p.body.createShadowRoot||p.body.attachShadow)){const E=p.createTreeWalker(p.body,NodeFilter.SHOW_ELEMENT);let M=E.currentNode;for(;M;){const j=M.shadowRoot;if(j){const Y=j.getElementById(D)||j.querySelector(`[name="${D}"]`);if(Y)return Y}M=E.nextNode()}}return null}(this.document,D);g&&(this.scrollToElement(g),this.attemptFocus(g))}setHistoryScrollRestoration(D){if(this.supportScrollRestoration()){const g=this.window.history;g&&g.scrollRestoration&&(g.scrollRestoration=D)}}scrollToElement(D){const g=D.getBoundingClientRect(),E=g.left+this.window.pageXOffset,M=g.top+this.window.pageYOffset,j=this.offset();this.window.scrollTo(E-j[0],M-j[1])}attemptFocus(D){return D.focus(),this.document.activeElement===D}supportScrollRestoration(){try{if(!this.supportsScrolling())return!1;const D=zn(this.window.history)||zn(Object.getPrototypeOf(this.window.history));return!(!D||!D.writable&&!D.set)}catch(D){return!1}}supportsScrolling(){try{return!!this.window&&!!this.window.scrollTo&&"pageXOffset"in this.window}catch(D){return!1}}}function zn(p){return Object.getOwnPropertyDescriptor(p,"scrollRestoration")}class ci{}},639:(Ye,ie,F)=>{F.d(ie,{deG:()=>_,tb:()=>Sy,AFp:()=>wy,ip1:()=>Od,CZH:()=>_o,hGG:()=>CT,z2F:()=>Oa,sBO:()=>iw,Sil:()=>Na,_Vd:()=>co,EJc:()=>Ay,SBq:()=>Mr,qLn:()=>Us,vpe:()=>Ar,gxx:()=>Ys,tBr:()=>ks,XFs:()=>he,OlP:()=>m,zs3:()=>ct,ZZ4:()=>$u,aQg:()=>Gu,soG:()=>tl,YKP:()=>Cm,v3s:()=>XI,h0i:()=>es,PXZ:()=>YI,R0b:()=>bn,FiY:()=>Qr,Lbi:()=>Ty,g9A:()=>Iy,Qsj:()=>$b,FYo:()=>Hu,JOm:()=>br,Tiy:()=>ud,q3G:()=>mt,tp0:()=>Ei,EAV:()=>rT,Rgc:()=>Ea,dDg:()=>Ny,DyG:()=>Cn,GfV:()=>fm,s_b:()=>Yu,ifc:()=>Pe,eFA:()=>jy,G48:()=>BI,_c5:()=>hT,VLi:()=>jI,c2e:()=>My,zSh:()=>la,wAp:()=>Ce,vHH:()=>ft,EiD:()=>Af,mCW:()=>ta,qzn:()=>Bs,JVY:()=>WD,pB0:()=>QD,eBb:()=>YD,L6k:()=>zD,LAX:()=>qD,cg1:()=>Xc,Tjo:()=>dT,kL8:()=>Fg,yhl:()=>Ef,dqk:()=>je,sIi:()=>fa,CqO:()=>Yc,QGY:()=>zc,F4k:()=>Up,RDi:()=>j,AaK:()=>G,z3N:()=>Kr,TTD:()=>gs,xp6:()=>hh,uIk:()=>kc,Suo:()=>ny,Xpm:()=>Lr,lG2:()=>lr,Yz7:()=>ut,cJS:()=>ot,oAB:()=>Vr,Yjl:()=>Oi,Y36:()=>pa,_UZ:()=>kp,qZA:()=>Wc,TgZ:()=>Gc,EpF:()=>Bp,Ikx:()=>Jc,LFG:()=>bt,$8M:()=>Qo,NdJ:()=>qc,CRH:()=>ry,kcU:()=>ru,O4$:()=>nu,Q6J:()=>Hc,iGM:()=>ty,MAs:()=>Ip,CHM:()=>Ya,LSH:()=>Yl,kYT:()=>tn,YNc:()=>wp,_uU:()=>gg,hij:()=>Fu});var I=F(574),b=F(869),ae=F(282),me=F(693),W=F(709),oe=F(319),fe=F(441);var V=F(307);function H(){return new W.xQ}function Z(e){for(let t in e)if(e[t]===Z)return t;throw Error("Could not find renamed property on target object.")}function G(e){if("string"==typeof e)return e;if(Array.isArray(e))return"["+e.map(G).join(", ")+"]";if(null==e)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;const t=e.toString();if(null==t)return""+t;const n=t.indexOf("\n");return-1===n?t:t.substring(0,n)}function Te(e,t){return null==e||""===e?null===t?"":t:null==t||""===t?e:e+" "+t}const qe=Z({__forward_ref__:Z});function we(e){return e.__forward_ref__=we,e.toString=function(){return G(this())},e}function te(e){return function(e){return"function"==typeof e&&e.hasOwnProperty(qe)&&e.__forward_ref__===we}(e)?e():e}class ft extends Error{constructor(t,n){super(function(e,t){return`${e?`NG0${e}: `:""}${t}`}(t,n)),this.code=t}}function se(e){return"string"==typeof e?e:null==e?"":String(e)}function Fe(e){return"function"==typeof e?e.name||e.toString():"object"==typeof e&&null!=e&&"function"==typeof e.type?e.type.name||e.type.toString():se(e)}function $e(e,t){const n=t?` in ${t}`:"";throw new ft("201",`No provider for ${Fe(e)} found${n}`)}function Et(e,t){null==e&&function(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(null==r?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}(t,e,null,"!=")}function ut(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function ot(e){return{providers:e.providers||[],imports:e.imports||[]}}function wt(e){return xr(e,or)||xr(e,Ai)}function xr(e,t){return e.hasOwnProperty(t)?e[t]:null}function Pr(e){return e&&(e.hasOwnProperty(Nr)||e.hasOwnProperty(Tn))?e[Nr]:null}const or=Z({\u0275prov:Z}),Nr=Z({\u0275inj:Z}),Ai=Z({ngInjectableDef:Z}),Tn=Z({ngInjectorDef:Z});var he=(()=>((he=he||{})[he.Default=0]="Default",he[he.Host=1]="Host",he[he.Self=2]="Self",he[he.SkipSelf=4]="SkipSelf",he[he.Optional=8]="Optional",he))();let tt;function ye(e){const t=tt;return tt=e,t}function cn(e,t,n){const r=wt(e);return r&&"root"==r.providedIn?void 0===r.value?r.value=r.factory():r.value:n&he.Optional?null:void 0!==t?t:void $e(G(e),"Injector")}function Dt(e){return{toString:e}.toString()}var Qe=(()=>((Qe=Qe||{})[Qe.OnPush=0]="OnPush",Qe[Qe.Default=1]="Default",Qe))(),Pe=(()=>((Pe=Pe||{})[Pe.Emulated=0]="Emulated",Pe[Pe.None=2]="None",Pe[Pe.ShadowDom=3]="ShadowDom",Pe))();const Ri="undefined"!=typeof globalThis&&globalThis,Ge="undefined"!=typeof window&&window,xi="undefined"!=typeof self&&"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&self,je=Ri||"undefined"!=typeof global&&global||Ge||xi,dn={},Be=[],Sn=Z({\u0275cmp:Z}),ur=Z({\u0275dir:Z}),Or=Z({\u0275pipe:Z}),Fr=Z({\u0275mod:Z}),Pi=Z({\u0275loc:Z}),Wt=Z({\u0275fac:Z}),Hn=Z({__NG_ELEMENT_ID__:Z});let Ni=0;function Lr(e){return Dt(()=>{const n={},r={type:e.type,providersResolver:null,decls:e.decls,vars:e.vars,factory:null,template:e.template||null,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:n,inputs:null,outputs:null,exportAs:e.exportAs||null,onPush:e.changeDetection===Qe.OnPush,directiveDefs:null,pipeDefs:null,selectors:e.selectors||Be,viewQuery:e.viewQuery||null,features:e.features||null,data:e.data||{},encapsulation:e.encapsulation||Pe.Emulated,id:"c",styles:e.styles||Be,_:null,setInput:null,schemas:e.schemas||null,tView:null},i=e.directives,s=e.features,o=e.pipes;return r.id+=Ni++,r.inputs=Mn(e.inputs,n),r.outputs=Mn(e.outputs),s&&s.forEach(a=>a(r)),r.directiveDefs=i?()=>("function"==typeof i?i():i).map(kr):null,r.pipeDefs=o?()=>("function"==typeof o?o():o).map(It):null,r})}function kr(e){return ht(e)||function(e){return e[ur]||null}(e)}function It(e){return function(e){return e[Or]||null}(e)}const Ee={};function Vr(e){return Dt(()=>{const t={type:e.type,bootstrap:e.bootstrap||Be,declarations:e.declarations||Be,imports:e.imports||Be,exports:e.exports||Be,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null};return null!=e.id&&(Ee[e.id]=e.type),t})}function tn(e,t){return Dt(()=>{const n=Tt(e,!0);n.declarations=t.declarations||Be,n.imports=t.imports||Be,n.exports=t.exports||Be})}function Mn(e,t){if(null==e)return dn;const n={};for(const r in e)if(e.hasOwnProperty(r)){let i=e[r],s=i;Array.isArray(i)&&(s=i[1],i=i[0]),n[i]=r,t&&(t[i]=s)}return n}const lr=Lr;function Oi(e){return{type:e.type,name:e.name,factory:null,pure:!1!==e.pure,onDestroy:e.type.prototype.ngOnDestroy||null}}function ht(e){return e[Sn]||null}function Tt(e,t){const n=e[Fr]||null;if(!n&&!0===t)throw new Error(`Type ${G(e)} does not have '\u0275mod' property.`);return n}function nn(e){return Array.isArray(e)&&"object"==typeof e[1]}function zt(e){return Array.isArray(e)&&!0===e[1]}function Li(e){return 0!=(8&e.flags)}function si(e){return 2==(2&e.flags)}function oi(e){return 1==(1&e.flags)}function Ft(e){return null!==e.template}function To(e){return 0!=(512&e[2])}function yn(e,t){return e.hasOwnProperty(Wt)?e[Wt]:null}class ps{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}}function gs(){return No}function No(e){return e.type.prototype.ngOnChanges&&(e.setInput=ui),Oo}function Oo(){const e=li(this),t=null==e?void 0:e.current;if(t){const n=e.previous;if(n===dn)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function ui(e,t,n,r){const i=li(e)||function(e,t){return e[zn]=t}(e,{previous:dn,current:null}),s=i.current||(i.current={}),o=i.previous,a=this.declaredInputs[n],c=o[a];s[a]=new ps(c&&c.currentValue,t,o===dn),e[r]=t}gs.ngInherit=!0;const zn="__ngSimpleChanges__";function li(e){return e[zn]||null}const g="http://www.w3.org/2000/svg";let M;function j(e){M=e}function ge(e){return!!e.listen}const Je={createRenderer:(e,t)=>void 0!==M?M:"undefined"!=typeof document?document:void 0};function Oe(e){for(;Array.isArray(e);)e=e[0];return e}function lt(e,t){return Oe(t[e.index])}function Pn(e,t){return e.data[t]}function Mt(e,t){const n=t[e];return nn(n)?n:n[0]}function Vi(e){return 4==(4&e[2])}function Lo(e){return 128==(128&e[2])}function hn(e,t){return null==t?null:e[t]}function di(e){e[18]=0}function ms(e,t){e[5]+=t;let n=e,r=e[3];for(;null!==r&&(1===t&&1===n[5]||-1===t&&0===n[5]);)r[5]+=t,n=r,r=r[3]}const ve={lFrame:Xa(null),bindingsEnabled:!0,isInCheckNoChangesMode:!1};function Vo(){return ve.bindingsEnabled}function z(){return ve.lFrame.lView}function Ue(){return ve.lFrame.tView}function Ya(e){return ve.lFrame.contextLView=e,e[8]}function Ct(){let e=qa();for(;null!==e&&64===e.type;)e=e.parent;return e}function qa(){return ve.lFrame.currentTNode}function Dn(e,t){const n=ve.lFrame;n.currentTNode=e,n.isParent=t}function Ds(){return ve.lFrame.isParent}function ji(){return ve.isInCheckNoChangesMode}function Cs(e){ve.isInCheckNoChangesMode=e}function fi(){return ve.lFrame.bindingIndex++}function Es(e,t){const n=ve.lFrame;n.bindingIndex=n.bindingRootIndex=e,Bo(t)}function Bo(e){ve.lFrame.currentDirectiveIndex=e}function Ka(){return ve.lFrame.currentQueryIndex}function $r(e){ve.lFrame.currentQueryIndex=e}function Dl(e){const t=e[1];return 2===t.type?t.declTNode:1===t.type?e[6]:null}function Za(e,t,n){if(n&he.SkipSelf){let i=t,s=e;for(;!(i=i.parent,null!==i||n&he.Host||(i=Dl(s),null===i||(s=s[15],10&i.type))););if(null===i)return!1;t=i,e=s}const r=ve.lFrame=Ja();return r.currentTNode=t,r.lView=e,!0}function pr(e){const t=Ja(),n=e[1];ve.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Ja(){const e=ve.lFrame,t=null===e?null:e.child;return null===t?Xa(e):t}function Xa(e){const t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return null!==e&&(e.child=t),t}function eu(){const e=ve.lFrame;return ve.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}const tu=eu;function vs(){const e=eu();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function $t(){return ve.lFrame.selectedIndex}function gr(e){ve.lFrame.selectedIndex=e}function at(){const e=ve.lFrame;return Pn(e.tView,e.selectedIndex)}function nu(){ve.lFrame.currentNamespace=g}function ru(){ve.lFrame.currentNamespace=null}function Uo(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){const s=e.data[n].type.prototype,{ngAfterContentInit:o,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:h,ngOnDestroy:y}=s;o&&(e.contentHooks||(e.contentHooks=[])).push(-n,o),a&&((e.contentHooks||(e.contentHooks=[])).push(n,a),(e.contentCheckHooks||(e.contentCheckHooks=[])).push(n,a)),c&&(e.viewHooks||(e.viewHooks=[])).push(-n,c),h&&((e.viewHooks||(e.viewHooks=[])).push(n,h),(e.viewCheckHooks||(e.viewCheckHooks=[])).push(n,h)),null!=y&&(e.destroyHooks||(e.destroyHooks=[])).push(n,y)}}function Bi(e,t,n){Ho(e,t,3,n)}function Ui(e,t,n,r){(3&e[2])===n&&Ho(e,t,n,r)}function bs(e,t){let n=e[2];(3&n)===t&&(n&=2047,n+=1,e[2]=n)}function Ho(e,t,n,r){const s=null!=r?r:-1,o=t.length-1;let a=0;for(let c=void 0!==r?65535&e[18]:0;c<o;c++)if("number"==typeof t[c+1]){if(a=t[c],null!=r&&a>=r)break}else t[c]<0&&(e[18]+=65536),(a<s||-1==s)&&(wl(e,n,t,c),e[18]=(**********&e[18])+c+2),c++}function wl(e,t,n,r){const i=n[r]<0,s=n[r+1],a=e[i?-n[r]:n[r]];if(i){if(e[2]>>11<e[18]>>16&&(3&e[2])===t){e[2]+=2048;try{s.call(a)}finally{}}}else try{s.call(a)}finally{}}class Hi{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}}function ws(e,t,n){const r=ge(e);let i=0;for(;i<n.length;){const s=n[i];if("number"==typeof s){if(0!==s)break;i++;const o=n[i++],a=n[i++],c=n[i++];r?e.setAttribute(t,a,c,o):t.setAttributeNS(o,a,c)}else{const o=s,a=n[++i];Go(o)?r&&e.setProperty(t,o,a):r?e.setAttribute(t,o,a):t.setAttribute(o,a),i++}}return i}function Wr(e){return 3===e||4===e||6===e}function Go(e){return 64===e.charCodeAt(0)}function Is(e,t){if(null!==t&&0!==t.length)if(null===e||0===e.length)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){const i=t[r];"number"==typeof i?n=i:0===n||ou(e,n,i,null,-1===n||2===n?t[++r]:null)}}return e}function ou(e,t,n,r,i){let s=0,o=e.length;if(-1===t)o=-1;else for(;s<e.length;){const a=e[s++];if("number"==typeof a){if(a===t){o=-1;break}if(a>t){o=s-1;break}}}for(;s<e.length;){const a=e[s];if("number"==typeof a)break;if(a===n){if(null===r)return void(null!==i&&(e[s+1]=i));if(r===e[s+1])return void(e[s+2]=i)}s++,null!==r&&s++,null!==i&&s++}-1!==o&&(e.splice(o,0,t),s=o+1),e.splice(s++,0,n),null!==r&&e.splice(s++,0,r),null!==i&&e.splice(s++,0,i)}function au(e){return-1!==e}function zr(e){return 32767&e}function pi(e,t){let n=function(e){return e>>16}(e),r=t;for(;n>0;)r=r[15],n--;return r}let Lt=!0;function Ts(e){const t=Lt;return Lt=e,t}let gi=0;function $i(e,t){const n=zo(e,t);if(-1!==n)return n;const r=t[1];r.firstCreatePass&&(e.injectorIndex=t.length,Gi(r.data,e),Gi(t,null),Gi(r.blueprint,null));const i=Wi(e,t),s=e.injectorIndex;if(au(i)){const o=zr(i),a=pi(i,t),c=a[1].data;for(let h=0;h<8;h++)t[s+h]=a[o+h]|c[o+h]}return t[s+8]=i,s}function Gi(e,t){e.push(0,0,0,0,0,0,0,0,t)}function zo(e,t){return-1===e.injectorIndex||e.parent&&e.parent.injectorIndex===e.injectorIndex||null===t[e.injectorIndex+8]?-1:e.injectorIndex}function Wi(e,t){if(e.parent&&-1!==e.parent.injectorIndex)return e.parent.injectorIndex;let n=0,r=null,i=t;for(;null!==i;){const s=i[1],o=s.type;if(r=2===o?s.declTNode:1===o?i[6]:null,null===r)return-1;if(n++,i=i[15],-1!==r.injectorIndex)return r.injectorIndex|n<<16}return-1}function As(e,t,n){!function(e,t,n){let r;"string"==typeof n?r=n.charCodeAt(0)||0:n.hasOwnProperty(Hn)&&(r=n[Hn]),null==r&&(r=n[Hn]=gi++);const i=255&r;t.data[e+(i>>5)]|=1<<i}(e,t,n)}function Yo(e,t,n){if(n&he.Optional)return e;$e(t,"NodeInjector")}function Rs(e,t,n,r){if(n&he.Optional&&void 0===r&&(r=null),0==(n&(he.Self|he.Host))){const i=e[9],s=ye(void 0);try{return i?i.get(t,r,n&he.Optional):cn(t,r,n&he.Optional)}finally{ye(s)}}return Yo(r,t,n)}function Pl(e,t,n,r=he.Default,i){if(null!==e){const s=function(e){if("string"==typeof e)return e.charCodeAt(0)||0;const t=e.hasOwnProperty(Hn)?e[Hn]:void 0;return"number"==typeof t?t>=0?255&t:lu:t}(n);if("function"==typeof s){if(!Za(t,e,r))return r&he.Host?Yo(i,n,r):Rs(t,n,r,i);try{const o=s(r);if(null!=o||r&he.Optional)return o;$e(n)}finally{tu()}}else if("number"==typeof s){let o=null,a=zo(e,t),c=-1,h=r&he.Host?t[16][6]:null;for((-1===a||r&he.SkipSelf)&&(c=-1===a?Wi(e,t):t[a+8],-1!==c&&du(r,!1)?(o=t[1],a=zr(c),t=pi(c,t)):a=-1);-1!==a;){const y=t[1];if(cu(s,a,y.data)){const C=Nl(a,t,n,o,r,h);if(C!==yr)return C}c=t[a+8],-1!==c&&du(r,t[1].data[a+8]===h)&&cu(s,a,t)?(o=y,a=zr(c),t=pi(c,t)):a=-1}}}return Rs(t,n,r,i)}const yr={};function lu(){return new mi(Ct(),z())}function Nl(e,t,n,r,i,s){const o=t[1],a=o.data[e+8],y=xs(a,o,n,null==r?si(a)&&Lt:r!=o&&0!=(3&a.type),i&he.Host&&s===a);return null!==y?zi(t,o,y,a):yr}function xs(e,t,n,r,i){const s=e.providerIndexes,o=t.data,a=1048575&s,c=e.directiveStart,y=s>>20,v=i?a+y:e.directiveEnd;for(let S=r?a:a+y;S<v;S++){const x=o[S];if(S<c&&n===x||S>=c&&x.type===n)return S}if(i){const S=o[c];if(S&&Ft(S)&&S.type===n)return c}return null}function zi(e,t,n,r){let i=e[n];const s=t.data;if(function(e){return e instanceof Hi}(i)){const o=i;o.resolving&&function(e,t){throw new ft("200",`Circular dependency in DI detected for ${e}`)}(Fe(s[n]));const a=Ts(o.canSeeViewProviders);o.resolving=!0;const c=o.injectImpl?ye(o.injectImpl):null;Za(e,r,he.Default);try{i=e[n]=o.factory(void 0,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&function(e,t,n){const{ngOnChanges:r,ngOnInit:i,ngDoCheck:s}=t.type.prototype;if(r){const o=No(t);(n.preOrderHooks||(n.preOrderHooks=[])).push(e,o),(n.preOrderCheckHooks||(n.preOrderCheckHooks=[])).push(e,o)}i&&(n.preOrderHooks||(n.preOrderHooks=[])).push(0-e,i),s&&((n.preOrderHooks||(n.preOrderHooks=[])).push(e,s),(n.preOrderCheckHooks||(n.preOrderCheckHooks=[])).push(e,s))}(n,s[n],t)}finally{null!==c&&ye(c),Ts(a),o.resolving=!1,tu()}}return i}function cu(e,t,n){return!!(n[t+(e>>5)]&1<<e)}function du(e,t){return!(e&he.Self||e&he.Host&&t)}class mi{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Pl(this._tNode,this._lView,t,r,n)}}function Qo(e){return function(e,t){if("class"===t)return e.classes;if("style"===t)return e.styles;const n=e.attrs;if(n){const r=n.length;let i=0;for(;i<r;){const s=n[i];if(Wr(s))break;if(0===s)i+=2;else if("number"==typeof s)for(i++;i<r&&"string"==typeof n[i];)i++;else{if(s===t)return n[i+1];i+=2}}}return null}(Ct(),e)}const Dr="__parameters__";function Yr(e,t,n){return Dt(()=>{const r=function(e){return function(...n){if(e){const r=e(...n);for(const i in r)this[i]=r[i]}}}(t);function i(...s){if(this instanceof i)return r.apply(this,s),this;const o=new i(...s);return a.annotation=o,a;function a(c,h,y){const C=c.hasOwnProperty(Dr)?c[Dr]:Object.defineProperty(c,Dr,{value:[]})[Dr];for(;C.length<=y;)C.push(null);return(C[y]=C[y]||[]).push(o),c}}return n&&(i.prototype=Object.create(n.prototype)),i.prototype.ngMetadataName=e,i.annotationCls=i,i})}class m{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,"number"==typeof n?this.__NG_ELEMENT_ID__=n:void 0!==n&&(this.\u0275prov=ut({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}toString(){return`InjectionToken ${this._desc}`}}const _=new m("AnalyzeForEntryComponents"),Cn=Function;function On(e,t){void 0===t&&(t=e);for(let n=0;n<e.length;n++){let r=e[n];Array.isArray(r)?(t===e&&(t=e.slice(0,n)),On(r,t)):t!==e&&t.push(r)}return t}function Cr(e,t){e.forEach(n=>Array.isArray(n)?Cr(n,t):t(n))}function hu(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function qi(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}const Zo={},kl="__NG_DI_FLAG__",Fs="ngTempTokenPath",TD=/\n/gm,Vl="__source",jl=Z({provide:String,useValue:Z});let Jo;function Ls(e){const t=Jo;return Jo=e,t}function MD(e,t=he.Default){if(void 0===Jo)throw new Error("inject() must be called from an injection context");return null===Jo?cn(e,void 0,t):Jo.get(e,t&he.Optional?null:void 0,t)}function bt(e,t=he.Default){return(tt||MD)(te(e),t)}function Qi(e){const t=[];for(let n=0;n<e.length;n++){const r=te(e[n]);if(Array.isArray(r)){if(0===r.length)throw new Error("Arguments array must have arguments.");let i,s=he.Default;for(let o=0;o<r.length;o++){const a=r[o],c=AD(a);"number"==typeof c?-1===c?i=a.token:s|=c:i=a}t.push(bt(i,s))}else t.push(bt(r))}return t}function Xo(e,t){return e[kl]=t,e.prototype[kl]=t,e}function AD(e){return e[kl]}function hf(e,t,n,r){const i=e[Fs];throw t[Vl]&&i.unshift(t[Vl]),e.message=function(e,t,n,r=null){e=e&&"\n"===e.charAt(0)&&"\u0275"==e.charAt(1)?e.substr(2):e;let i=G(t);if(Array.isArray(t))i=t.map(G).join(" -> ");else if("object"==typeof t){let s=[];for(let o in t)if(t.hasOwnProperty(o)){let a=t[o];s.push(o+":"+("string"==typeof a?JSON.stringify(a):G(a)))}i=`{${s.join(", ")}}`}return`${n}${r?"("+r+")":""}[${i}]: ${e.replace(TD,"\n  ")}`}("\n"+e.message,i,n,r),e.ngTokenPath=i,e[Fs]=null,e}const ks=Xo(Yr("Inject",e=>({token:e})),-1),Qr=Xo(Yr("Optional"),8),Ei=Xo(Yr("SkipSelf"),4);let yu;function js(e){var t;return(null===(t=function(){if(void 0===yu&&(yu=null,je.trustedTypes))try{yu=je.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch(e){}return yu}())||void 0===t?void 0:t.createHTML(e))||e}class Ki{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see https://g.co/ng/security#xss)`}}class BD extends Ki{getTypeName(){return"HTML"}}class UD extends Ki{getTypeName(){return"Style"}}class HD extends Ki{getTypeName(){return"Script"}}class $D extends Ki{getTypeName(){return"URL"}}class GD extends Ki{getTypeName(){return"ResourceURL"}}function Kr(e){return e instanceof Ki?e.changingThisBreaksApplicationSecurity:e}function Bs(e,t){const n=Ef(e);if(null!=n&&n!==t){if("ResourceURL"===n&&"URL"===t)return!0;throw new Error(`Required a safe ${t}, got a ${n} (see https://g.co/ng/security#xss)`)}return n===t}function Ef(e){return e instanceof Ki&&e.getTypeName()||null}function WD(e){return new BD(e)}function zD(e){return new UD(e)}function YD(e){return new HD(e)}function qD(e){return new $D(e)}function QD(e){return new GD(e)}class KD{constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{const n=(new window.DOMParser).parseFromString(js(t),"text/html").body;return null===n?this.inertDocumentHelper.getInertBodyElement(t):(n.removeChild(n.firstChild),n)}catch(n){return null}}}class ZD{constructor(t){if(this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert"),null==this.inertDocument.body){const n=this.inertDocument.createElement("html");this.inertDocument.appendChild(n);const r=this.inertDocument.createElement("body");n.appendChild(r)}}getInertBodyElement(t){const n=this.inertDocument.createElement("template");if("content"in n)return n.innerHTML=js(t),n;const r=this.inertDocument.createElement("body");return r.innerHTML=js(t),this.defaultDoc.documentMode&&this.stripCustomNsAttrs(r),r}stripCustomNsAttrs(t){const n=t.attributes;for(let i=n.length-1;0<i;i--){const o=n.item(i).name;("xmlns:ns1"===o||0===o.indexOf("ns1:"))&&t.removeAttribute(o)}let r=t.firstChild;for(;r;)r.nodeType===Node.ELEMENT_NODE&&this.stripCustomNsAttrs(r),r=r.nextSibling}}const XD=/^(?:(?:https?|mailto|ftp|tel|file|sms):|[^&:/?#]*(?:[/?#]|$))/gi,e_=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+\/]+=*$/i;function ta(e){return(e=String(e)).match(XD)||e.match(e_)?e:"unsafe:"+e}function Er(e){const t={};for(const n of e.split(","))t[n]=!0;return t}function na(...e){const t={};for(const n of e)for(const r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}const wf=Er("area,br,col,hr,img,wbr"),If=Er("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Tf=Er("rp,rt"),$l=na(wf,na(If,Er("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),na(Tf,Er("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),na(Tf,If)),Gl=Er("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),Wl=Er("srcset"),Sf=na(Gl,Wl,Er("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),Er("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext")),t_=Er("script,style,template");class n_{constructor(){this.sanitizedSomething=!1,this.buf=[]}sanitizeChildren(t){let n=t.firstChild,r=!0;for(;n;)if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild)n=n.firstChild;else for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=this.checkClobberedElement(n,n.nextSibling);if(i){n=i;break}n=this.checkClobberedElement(n,n.parentNode)}return this.buf.join("")}startElement(t){const n=t.nodeName.toLowerCase();if(!$l.hasOwnProperty(n))return this.sanitizedSomething=!0,!t_.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);const r=t.attributes;for(let i=0;i<r.length;i++){const s=r.item(i),o=s.name,a=o.toLowerCase();if(!Sf.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=s.value;Gl[a]&&(c=ta(c)),Wl[a]&&(e=c,c=(e=String(e)).split(",").map(t=>ta(t.trim())).join(", ")),this.buf.push(" ",o,'="',Mf(c),'"')}var e;return this.buf.push(">"),!0}endElement(t){const n=t.nodeName.toLowerCase();$l.hasOwnProperty(n)&&!wf.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(Mf(t))}checkClobberedElement(t,n){if(n&&(t.compareDocumentPosition(n)&Node.DOCUMENT_POSITION_CONTAINED_BY)===Node.DOCUMENT_POSITION_CONTAINED_BY)throw new Error(`Failed to sanitize html because the element is clobbered: ${t.outerHTML}`);return n}}const r_=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,i_=/([^\#-~ |!])/g;function Mf(e){return e.replace(/&/g,"&amp;").replace(r_,function(t){return"&#"+(1024*(t.charCodeAt(0)-55296)+(t.charCodeAt(1)-56320)+65536)+";"}).replace(i_,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}let _u;function Af(e,t){let n=null;try{_u=_u||function(e){const t=new ZD(e);return function(){try{return!!(new window.DOMParser).parseFromString(js(""),"text/html")}catch(e){return!1}}()?new KD(t):t}(e);let r=t?String(t):"";n=_u.getInertBodyElement(r);let i=5,s=r;do{if(0===i)throw new Error("Failed to sanitize html because the input is unstable");i--,r=s,s=n.innerHTML,n=_u.getInertBodyElement(r)}while(r!==s);return js((new n_).sanitizeChildren(zl(n)||n))}finally{if(n){const r=zl(n)||n;for(;r.firstChild;)r.removeChild(r.firstChild)}}}function zl(e){return"content"in e&&function(e){return e.nodeType===Node.ELEMENT_NODE&&"TEMPLATE"===e.nodeName}(e)?e.content:null}var mt=(()=>((mt=mt||{})[mt.NONE=0]="NONE",mt[mt.HTML=1]="HTML",mt[mt.STYLE=2]="STYLE",mt[mt.SCRIPT=3]="SCRIPT",mt[mt.URL=4]="URL",mt[mt.RESOURCE_URL=5]="RESOURCE_URL",mt))();function Yl(e){const t=function(){const e=z();return e&&e[12]}();return t?t.sanitize(mt.URL,e)||"":Bs(e,"URL")?Kr(e):ta(se(e))}const Pf="__ngContext__";function qt(e,t){e[Pf]=t}function Ql(e){const t=function(e){return e[Pf]||null}(e);return t?Array.isArray(t)?t:t.lView:null}function Cu(e){return e.ngOriginalError}function v_(e,...t){e.error(...t)}class Us{constructor(){this._console=console}handleError(t){const n=this._findOriginalError(t),r=this._findContext(t),i=(e=t)&&e.ngErrorLogger||v_;var e;i(this._console,"ERROR",t),n&&i(this._console,"ORIGINAL ERROR",n),r&&i(this._console,"ERROR CONTEXT",r)}_findContext(t){return t?t.ngDebugContext||this._findContext(Cu(t)):null}_findOriginalError(t){let n=t&&Cu(t);for(;n&&Cu(n);)n=Cu(n);return n||null}}const Uf=(()=>("undefined"!=typeof requestAnimationFrame&&requestAnimationFrame||setTimeout).bind(je))();function vr(e){return e instanceof Function?e():e}var br=(()=>((br=br||{})[br.Important=1]="Important",br[br.DashCase=2]="DashCase",br))();function Jl(e,t){return undefined(e,t)}function sa(e){const t=e[3];return zt(t)?t[3]:t}function Xl(e){return zf(e[13])}function ec(e){return zf(e[4])}function zf(e){for(;null!==e&&!zt(e);)e=e[4];return e}function $s(e,t,n,r,i){if(null!=r){let s,o=!1;zt(r)?s=r:nn(r)&&(o=!0,r=r[0]);const a=Oe(r);0===e&&null!==n?null==i?Jf(t,n,a):Zi(t,n,a,i||null,!0):1===e&&null!==n?Zi(t,n,a,i||null,!0):2===e?function(e,t,n){const r=vu(e,t);r&&function(e,t,n,r){ge(e)?e.removeChild(t,n,r):t.removeChild(n)}(e,r,t,n)}(t,a,o):3===e&&t.destroyNode(a),null!=s&&function(e,t,n,r,i){const s=n[7];s!==Oe(n)&&$s(t,e,r,s,i);for(let a=10;a<n.length;a++){const c=n[a];oa(c[1],c,e,t,r,s)}}(t,e,s,n,i)}}function nc(e,t,n){return ge(e)?e.createElement(t,n):null===n?e.createElement(t):e.createElementNS(n,t)}function qf(e,t){const n=e[9],r=n.indexOf(t),i=t[3];1024&t[2]&&(t[2]&=-1025,ms(i,-1)),n.splice(r,1)}function rc(e,t){if(e.length<=10)return;const n=10+t,r=e[n];if(r){const i=r[17];null!==i&&i!==e&&qf(i,r),t>0&&(e[n-1][4]=r[4]);const s=qi(e,10+t);!function(e,t){oa(e,t,t[11],2,null,null),t[0]=null,t[6]=null}(r[1],r);const o=s[19];null!==o&&o.detachView(s[1]),r[3]=null,r[4]=null,r[2]&=-129}return r}function Qf(e,t){if(!(256&t[2])){const n=t[11];ge(n)&&n.destroyNode&&oa(e,t,n,3,null,null),function(e){let t=e[13];if(!t)return ic(e[1],e);for(;t;){let n=null;if(nn(t))n=t[13];else{const r=t[10];r&&(n=r)}if(!n){for(;t&&!t[4]&&t!==e;)nn(t)&&ic(t[1],t),t=t[3];null===t&&(t=e),nn(t)&&ic(t[1],t),n=t&&t[4]}t=n}}(t)}}function ic(e,t){if(!(256&t[2])){t[2]&=-129,t[2]|=256,function(e,t){let n;if(null!=e&&null!=(n=e.destroyHooks))for(let r=0;r<n.length;r+=2){const i=t[n[r]];if(!(i instanceof Hi)){const s=n[r+1];if(Array.isArray(s))for(let o=0;o<s.length;o+=2){const a=i[s[o]],c=s[o+1];try{c.call(a)}finally{}}else try{s.call(i)}finally{}}}}(e,t),function(e,t){const n=e.cleanup,r=t[7];let i=-1;if(null!==n)for(let s=0;s<n.length-1;s+=2)if("string"==typeof n[s]){const o=n[s+1],a="function"==typeof o?o(t):Oe(t[o]),c=r[i=n[s+2]],h=n[s+3];"boolean"==typeof h?a.removeEventListener(n[s],c,h):h>=0?r[i=h]():r[i=-h].unsubscribe(),s+=2}else{const o=r[i=n[s+1]];n[s].call(o)}if(null!==r){for(let s=i+1;s<r.length;s++)r[s]();t[7]=null}}(e,t),1===t[1].type&&ge(t[11])&&t[11].destroy();const n=t[17];if(null!==n&&zt(t[3])){n!==t[3]&&qf(n,t);const r=t[19];null!==r&&r.detachView(e)}}}function Kf(e,t,n){return function(e,t,n){let r=t;for(;null!==r&&40&r.type;)r=(t=r).parent;if(null===r)return n[0];if(2&r.flags){const i=e.data[r.directiveStart].encapsulation;if(i===Pe.None||i===Pe.Emulated)return null}return lt(r,n)}(e,t.parent,n)}function Zi(e,t,n,r,i){ge(e)?e.insertBefore(t,n,r,i):t.insertBefore(n,r,i)}function Jf(e,t,n){ge(e)?e.appendChild(t,n):t.appendChild(n)}function Xf(e,t,n,r,i){null!==r?Zi(e,t,n,r,i):Jf(e,t,n)}function vu(e,t){return ge(e)?e.parentNode(t):t.parentNode}let nh=function(e,t,n){return 40&e.type?lt(e,n):null};function bu(e,t,n,r){const i=Kf(e,r,t),s=t[11],a=function(e,t,n){return nh(e,t,n)}(r.parent||t[6],r,t);if(null!=i)if(Array.isArray(n))for(let c=0;c<n.length;c++)Xf(s,i,n[c],a,!1);else Xf(s,i,n,a,!1)}function wu(e,t){if(null!==t){const n=t.type;if(3&n)return lt(t,e);if(4&n)return oc(-1,e[t.index]);if(8&n){const r=t.child;if(null!==r)return wu(e,r);{const i=e[t.index];return zt(i)?oc(-1,i):Oe(i)}}if(32&n)return Jl(t,e)()||Oe(e[t.index]);{const r=ih(e,t);return null!==r?Array.isArray(r)?r[0]:wu(sa(e[16]),r):wu(e,t.next)}}return null}function ih(e,t){return null!==t?e[16][6].projection[t.projection]:null}function oc(e,t){const n=10+e+1;if(n<t.length){const r=t[n],i=r[1].firstChild;if(null!==i)return wu(r,i)}return t[7]}function ac(e,t,n,r,i,s,o){for(;null!=n;){const a=r[n.index],c=n.type;if(o&&0===t&&(a&&qt(Oe(a),r),n.flags|=4),64!=(64&n.flags))if(8&c)ac(e,t,n.child,r,i,s,!1),$s(t,e,i,a,s);else if(32&c){const h=Jl(n,r);let y;for(;y=h();)$s(t,e,i,y,s);$s(t,e,i,a,s)}else 16&c?oh(e,t,r,n,i,s):$s(t,e,i,a,s);n=o?n.projectionNext:n.next}}function oa(e,t,n,r,i,s){ac(n,r,e.firstChild,t,i,s,!1)}function oh(e,t,n,r,i,s){const o=n[16],c=o[6].projection[r.projection];if(Array.isArray(c))for(let h=0;h<c.length;h++)$s(t,e,i,c[h],s);else ac(e,t,c,o[3],i,s,!0)}function ah(e,t,n){ge(e)?e.setAttribute(t,"style",n):t.style.cssText=n}function uc(e,t,n){ge(e)?""===n?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n):t.className=n}function uh(e,t,n){let r=e.length;for(;;){const i=e.indexOf(t,n);if(-1===i)return i;if(0===i||e.charCodeAt(i-1)<=32){const s=t.length;if(i+s===r||e.charCodeAt(i+s)<=32)return i}n=i+1}}const lh="ng-template";function q_(e,t,n){let r=0;for(;r<e.length;){let i=e[r++];if(n&&"class"===i){if(i=e[r],-1!==uh(i.toLowerCase(),t,0))return!0}else if(1===i){for(;r<e.length&&"string"==typeof(i=e[r++]);)if(i.toLowerCase()===t)return!0;return!1}}return!1}function ch(e){return 4===e.type&&e.value!==lh}function Q_(e,t,n){return t===(4!==e.type||n?e.value:lh)}function K_(e,t,n){let r=4;const i=e.attrs||[],s=function(e){for(let t=0;t<e.length;t++)if(Wr(e[t]))return t;return e.length}(i);let o=!1;for(let a=0;a<t.length;a++){const c=t[a];if("number"!=typeof c){if(!o)if(4&r){if(r=2|1&r,""!==c&&!Q_(e,c,n)||""===c&&1===t.length){if(Yn(r))return!1;o=!0}}else{const h=8&r?c:t[++a];if(8&r&&null!==e.attrs){if(!q_(e.attrs,h,n)){if(Yn(r))return!1;o=!0}continue}const C=Z_(8&r?"class":c,i,ch(e),n);if(-1===C){if(Yn(r))return!1;o=!0;continue}if(""!==h){let v;v=C>s?"":i[C+1].toLowerCase();const S=8&r?v:null;if(S&&-1!==uh(S,h,0)||2&r&&h!==v){if(Yn(r))return!1;o=!0}}}}else{if(!o&&!Yn(r)&&!Yn(c))return!1;if(o&&Yn(c))continue;o=!1,r=c|1&r}}return Yn(r)||o}function Yn(e){return 0==(1&e)}function Z_(e,t,n,r){if(null===t)return-1;let i=0;if(r||!n){let s=!1;for(;i<t.length;){const o=t[i];if(o===e)return i;if(3===o||6===o)s=!0;else{if(1===o||2===o){let a=t[++i];for(;"string"==typeof a;)a=t[++i];continue}if(4===o)break;if(0===o){i+=4;continue}}i+=s?1:2}return-1}return function(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){const r=e[n];if("number"==typeof r)return-1;if(r===t)return n;n++}return-1}(t,e)}function dh(e,t,n=!1){for(let r=0;r<t.length;r++)if(K_(e,t[r],n))return!0;return!1}function fh(e,t){return e?":not("+t.trim()+")":t}function nC(e){let t=e[0],n=1,r=2,i="",s=!1;for(;n<e.length;){let o=e[n];if("string"==typeof o)if(2&r){const a=e[++n];i+="["+o+(a.length>0?'="'+a+'"':"")+"]"}else 8&r?i+="."+o:4&r&&(i+=" "+o);else""!==i&&!Yn(o)&&(t+=fh(s,i),i=""),r=o,s=s||!Yn(r);n++}return""!==i&&(t+=fh(s,i)),t}const Re={};function hh(e){ph(Ue(),z(),$t()+e,ji())}function ph(e,t,n,r){if(!r)if(3==(3&t[2])){const s=e.preOrderCheckHooks;null!==s&&Bi(t,s,n)}else{const s=e.preOrderHooks;null!==s&&Ui(t,s,0,n)}gr(n)}function wh(e,t){const n=e.contentQueries;if(null!==n)for(let r=0;r<n.length;r+=2){const i=n[r],s=n[r+1];if(-1!==s){const o=e.data[s];$r(i),o.contentQueries(2,t[s],s)}}}function aa(e,t,n,r,i,s,o,a,c,h){const y=t.blueprint.slice();return y[0]=i,y[2]=140|r,di(y),y[3]=y[15]=e,y[8]=n,y[10]=o||e&&e[10],y[11]=a||e&&e[11],y[12]=c||e&&e[12]||null,y[9]=h||e&&e[9]||null,y[6]=s,y[16]=2==t.type?e[16]:y,y}function Gs(e,t,n,r,i){let s=e.data[t];if(null===s)s=function(e,t,n,r,i){const s=qa(),o=Ds(),c=e.data[t]=function(e,t,n,r,i,s){return{type:n,index:r,insertBeforeIndex:null,injectorIndex:t?t.injectorIndex:-1,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,propertyBindings:null,flags:0,providerIndexes:0,value:i,attrs:s,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tViews:null,next:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}(0,o?s:s&&s.parent,n,t,r,i);return null===e.firstChild&&(e.firstChild=c),null!==s&&(o?null==s.child&&null!==c.parent&&(s.child=c):null===s.next&&(s.next=c)),c}(e,t,n,r,i),ve.lFrame.inI18n&&(s.flags|=64);else if(64&s.type){s.type=n,s.value=r,s.attrs=i;const o=function(){const e=ve.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}();s.injectorIndex=null===o?-1:o.injectorIndex}return Dn(s,!0),s}function Ws(e,t,n,r){if(0===n)return-1;const i=t.length;for(let s=0;s<n;s++)t.push(r),e.blueprint.push(r),e.data.push(null);return i}function ua(e,t,n){pr(t);try{const r=e.viewQuery;null!==r&&Sc(1,r,n);const i=e.template;null!==i&&Ih(e,t,i,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),e.staticContentQueries&&wh(e,t),e.staticViewQueries&&Sc(2,e.viewQuery,n);const s=e.components;null!==s&&function(e,t){for(let n=0;n<t.length;n++)FC(e,t[n])}(t,s)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[2]&=-5,vs()}}function zs(e,t,n,r){const i=t[2];if(256==(256&i))return;pr(t);const s=ji();try{di(t),function(e){ve.lFrame.bindingIndex=e}(e.bindingStartIndex),null!==n&&Ih(e,t,n,2,r);const o=3==(3&i);if(!s)if(o){const h=e.preOrderCheckHooks;null!==h&&Bi(t,h,null)}else{const h=e.preOrderHooks;null!==h&&Ui(t,h,0,null),bs(t,0)}if(function(e){for(let t=Xl(e);null!==t;t=ec(t)){if(!t[2])continue;const n=t[9];for(let r=0;r<n.length;r++){const i=n[r],s=i[3];0==(1024&i[2])&&ms(s,1),i[2]|=1024}}}(t),function(e){for(let t=Xl(e);null!==t;t=ec(t))for(let n=10;n<t.length;n++){const r=t[n],i=r[1];Lo(r)&&zs(i,r,i.template,r[8])}}(t),null!==e.contentQueries&&wh(e,t),!s)if(o){const h=e.contentCheckHooks;null!==h&&Bi(t,h)}else{const h=e.contentHooks;null!==h&&Ui(t,h,1),bs(t,1)}!function(e,t){const n=e.hostBindingOpCodes;if(null!==n)try{for(let r=0;r<n.length;r++){const i=n[r];if(i<0)gr(~i);else{const s=i,o=n[++r],a=n[++r];Es(o,s),a(2,t[s])}}}finally{gr(-1)}}(e,t);const a=e.components;null!==a&&function(e,t){for(let n=0;n<t.length;n++)OC(e,t[n])}(t,a);const c=e.viewQuery;if(null!==c&&Sc(2,c,r),!s)if(o){const h=e.viewCheckHooks;null!==h&&Bi(t,h)}else{const h=e.viewHooks;null!==h&&Ui(t,h,2),bs(t,2)}!0===e.firstUpdatePass&&(e.firstUpdatePass=!1),s||(t[2]&=-73),1024&t[2]&&(t[2]&=-1025,ms(t[3],-1))}finally{vs()}}function gC(e,t,n,r){const i=t[10],s=!ji(),o=Vi(t);try{s&&!o&&i.begin&&i.begin(),o&&ua(e,t,r),zs(e,t,n,r)}finally{s&&!o&&i.end&&i.end()}}function Ih(e,t,n,r,i){const s=$t(),o=2&r;try{gr(-1),o&&t.length>20&&ph(e,t,20,ji()),n(r,i)}finally{gr(s)}}function _c(e,t,n){!Vo()||(function(e,t,n,r){const i=n.directiveStart,s=n.directiveEnd;e.firstCreatePass||$i(n,t),qt(r,t);const o=n.initialInputs;for(let a=i;a<s;a++){const c=e.data[a],h=Ft(c);h&&AC(t,n,c);const y=zi(t,e,a,n);qt(y,t),null!==o&&RC(0,a-i,y,c,0,o),h&&(Mt(n.index,t)[8]=y)}}(e,t,n,lt(n,t)),128==(128&n.flags)&&function(e,t,n){const r=n.directiveStart,i=n.directiveEnd,o=n.index,a=ve.lFrame.currentDirectiveIndex;try{gr(o);for(let c=r;c<i;c++){const h=e.data[c],y=t[c];Bo(c),(null!==h.hostBindings||0!==h.hostVars||null!==h.hostAttrs)&&Oh(h,y)}}finally{gr(-1),Bo(a)}}(e,t,n))}function Cc(e,t,n=lt){const r=t.localNames;if(null!==r){let i=t.index+1;for(let s=0;s<r.length;s+=2){const o=r[s+1],a=-1===o?n(t,e):e[o];e[i++]=a}}}function Sh(e){const t=e.tView;return null===t||t.incompleteFirstPass?e.tView=Mu(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts):t}function Mu(e,t,n,r,i,s,o,a,c,h){const y=20+r,C=y+i,v=function(e,t){const n=[];for(let r=0;r<t;r++)n.push(r<e?null:Re);return n}(y,C),S="function"==typeof h?h():h;return v[1]={type:e,blueprint:v,template:n,queries:null,viewQuery:a,declTNode:t,data:v.slice().fill(null,y),bindingStartIndex:y,expandoStartIndex:C,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:"function"==typeof s?s():s,pipeRegistry:"function"==typeof o?o():o,firstChild:null,schemas:c,consts:S,incompleteFirstPass:!1}}function Rh(e,t,n,r){const i=Bh(t);null===n?i.push(r):(i.push(n),e.firstCreatePass&&Uh(e).push(r,i.length-1))}function xh(e,t,n){for(let r in e)if(e.hasOwnProperty(r)){const i=e[r];(n=null===n?{}:n).hasOwnProperty(r)?n[r].push(t,i):n[r]=[t,i]}return n}function vn(e,t,n,r,i,s,o,a){const c=lt(t,n);let y,h=t.inputs;!a&&null!=h&&(y=h[r])?(Gh(e,n,y,r,i),si(t)&&function(e,t){const n=Mt(t,e);16&n[2]||(n[2]|=64)}(n,t.index)):3&t.type&&(r=function(e){return"class"===e?"className":"for"===e?"htmlFor":"formaction"===e?"formAction":"innerHtml"===e?"innerHTML":"readonly"===e?"readOnly":"tabindex"===e?"tabIndex":e}(r),i=null!=o?o(i,t.value||"",r):i,ge(s)?s.setProperty(c,r,i):Go(r)||(c.setProperty?c.setProperty(r,i):c[r]=i))}function Ec(e,t,n,r){let i=!1;if(Vo()){const s=function(e,t,n){const r=e.directiveRegistry;let i=null;if(r)for(let s=0;s<r.length;s++){const o=r[s];dh(n,o.selectors,!1)&&(i||(i=[]),As($i(n,t),e,o.type),Ft(o)?(Fh(e,n),i.unshift(o)):i.push(o))}return i}(e,t,n),o=null===r?null:{"":-1};if(null!==s){i=!0,Lh(n,e.data.length,s.length);for(let y=0;y<s.length;y++){const C=s[y];C.providersResolver&&C.providersResolver(C)}let a=!1,c=!1,h=Ws(e,t,s.length,null);for(let y=0;y<s.length;y++){const C=s[y];n.mergedAttrs=Is(n.mergedAttrs,C.hostAttrs),kh(e,n,t,h,C),MC(h,C,o),null!==C.contentQueries&&(n.flags|=8),(null!==C.hostBindings||null!==C.hostAttrs||0!==C.hostVars)&&(n.flags|=128);const v=C.type.prototype;!a&&(v.ngOnChanges||v.ngOnInit||v.ngDoCheck)&&((e.preOrderHooks||(e.preOrderHooks=[])).push(n.index),a=!0),!c&&(v.ngOnChanges||v.ngDoCheck)&&((e.preOrderCheckHooks||(e.preOrderCheckHooks=[])).push(n.index),c=!0),h++}!function(e,t){const r=t.directiveEnd,i=e.data,s=t.attrs,o=[];let a=null,c=null;for(let h=t.directiveStart;h<r;h++){const y=i[h],C=y.inputs,v=null===s||ch(t)?null:xC(C,s);o.push(v),a=xh(C,h,a),c=xh(y.outputs,h,c)}null!==a&&(a.hasOwnProperty("class")&&(t.flags|=16),a.hasOwnProperty("style")&&(t.flags|=32)),t.initialInputs=o,t.inputs=a,t.outputs=c}(e,n)}o&&function(e,t,n){if(t){const r=e.localNames=[];for(let i=0;i<t.length;i+=2){const s=n[t[i+1]];if(null==s)throw new ft("301",`Export of name '${t[i+1]}' not found!`);r.push(t[i],s)}}}(n,r,o)}return n.mergedAttrs=Is(n.mergedAttrs,n.attrs),i}function Nh(e,t,n,r,i,s){const o=s.hostBindings;if(o){let a=e.hostBindingOpCodes;null===a&&(a=e.hostBindingOpCodes=[]);const c=~t.index;(function(e){let t=e.length;for(;t>0;){const n=e[--t];if("number"==typeof n&&n<0)return n}return 0})(a)!=c&&a.push(c),a.push(r,i,o)}}function Oh(e,t){null!==e.hostBindings&&e.hostBindings(1,t)}function Fh(e,t){t.flags|=2,(e.components||(e.components=[])).push(t.index)}function MC(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Ft(t)&&(n[""]=e)}}function Lh(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function kh(e,t,n,r,i){e.data[r]=i;const s=i.factory||(i.factory=yn(i.type)),o=new Hi(s,Ft(i),null);e.blueprint[r]=o,n[r]=o,Nh(e,t,0,r,Ws(e,n,i.hostVars,Re),i)}function AC(e,t,n){const r=lt(t,e),i=Sh(n),s=e[10],o=Au(e,aa(e,i,null,n.onPush?64:16,r,t,s,s.createRenderer(r,n),null,null));e[t.index]=o}function wr(e,t,n,r,i,s){const o=lt(e,t);!function(e,t,n,r,i,s,o){if(null==s)ge(e)?e.removeAttribute(t,i,n):t.removeAttribute(i);else{const a=null==o?se(s):o(s,r||"",i);ge(e)?e.setAttribute(t,i,a,n):n?t.setAttributeNS(n,i,a):t.setAttribute(i,a)}}(t[11],o,s,e.value,n,r,i)}function RC(e,t,n,r,i,s){const o=s[t];if(null!==o){const a=r.setInput;for(let c=0;c<o.length;){const h=o[c++],y=o[c++],C=o[c++];null!==a?r.setInput(n,C,h,y):n[y]=C}}}function xC(e,t){let n=null,r=0;for(;r<t.length;){const i=t[r];if(0!==i)if(5!==i){if("number"==typeof i)break;e.hasOwnProperty(i)&&(null===n&&(n=[]),n.push(i,e[i],t[r+1])),r+=2}else r+=2;else r+=4}return n}function Vh(e,t,n,r){return new Array(e,!0,!1,t,null,0,r,n,null,null)}function OC(e,t){const n=Mt(t,e);if(Lo(n)){const r=n[1];80&n[2]?zs(r,n,r.template,n[8]):n[5]>0&&bc(n)}}function bc(e){for(let r=Xl(e);null!==r;r=ec(r))for(let i=10;i<r.length;i++){const s=r[i];if(1024&s[2]){const o=s[1];zs(o,s,o.template,s[8])}else s[5]>0&&bc(s)}const n=e[1].components;if(null!==n)for(let r=0;r<n.length;r++){const i=Mt(n[r],e);Lo(i)&&i[5]>0&&bc(i)}}function FC(e,t){const n=Mt(t,e),r=n[1];(function(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])})(r,n),ua(r,n,n[8])}function Au(e,t){return e[13]?e[14][4]=t:e[13]=t,e[14]=t,t}function wc(e){for(;e;){e[2]|=64;const t=sa(e);if(To(e)&&!t)return e;e=t}return null}function Tc(e,t,n){const r=t[10];r.begin&&r.begin();try{zs(e,t,e.template,n)}catch(i){throw $h(t,i),i}finally{r.end&&r.end()}}function jh(e){!function(e){for(let t=0;t<e.components.length;t++){const n=e.components[t],r=Ql(n),i=r[1];gC(i,r,i.template,n)}}(e[8])}function Sc(e,t,n){$r(0),t(e,n)}const BC=(()=>Promise.resolve(null))();function Bh(e){return e[7]||(e[7]=[])}function Uh(e){return e.cleanup||(e.cleanup=[])}function $h(e,t){const n=e[9],r=n?n.get(Us,null):null;r&&r.handleError(t)}function Gh(e,t,n,r,i){for(let s=0;s<n.length;){const o=n[s++],a=n[s++],c=t[o],h=e.data[o];null!==h.setInput?h.setInput(c,i,r,a):c[a]=i}}function Xr(e,t,n){const r=function(e,t){return Oe(t[e])}(t,e);!function(e,t,n){ge(e)?e.setValue(t,n):t.textContent=n}(e[11],r,n)}function Ru(e,t,n){let r=n?e.styles:null,i=n?e.classes:null,s=0;if(null!==t)for(let o=0;o<t.length;o++){const a=t[o];"number"==typeof a?s=a:1==s?i=Te(i,a):2==s&&(r=Te(r,a+": "+t[++o]+";"))}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=i:e.classesWithoutHost=i}const Ys=new m("INJECTOR",-1);class Wh{get(t,n=Zo){if(n===Zo){const r=new Error(`NullInjectorError: No provider for ${G(t)}!`);throw r.name="NullInjectorError",r}return n}}const la=new m("Set Injector scope."),ca={},$C={};let Mc;function zh(){return void 0===Mc&&(Mc=new Wh),Mc}function Yh(e,t=null,n=null,r){return new WC(e,n,t||zh(),r)}class WC{constructor(t,n,r,i=null){this.parent=r,this.records=new Map,this.injectorDefTypes=new Set,this.onDestroy=new Set,this._destroyed=!1;const s=[];n&&Cr(n,a=>this.processProvider(a,t,n)),Cr([t],a=>this.processInjectorType(a,[],s)),this.records.set(Ys,qs(void 0,this));const o=this.records.get(la);this.scope=null!=o?o.value:null,this.source=i||("object"==typeof t?null:G(t))}get destroyed(){return this._destroyed}destroy(){this.assertNotDestroyed(),this._destroyed=!0;try{this.onDestroy.forEach(t=>t.ngOnDestroy())}finally{this.records.clear(),this.onDestroy.clear(),this.injectorDefTypes.clear()}}get(t,n=Zo,r=he.Default){this.assertNotDestroyed();const i=Ls(this),s=ye(void 0);try{if(!(r&he.SkipSelf)){let a=this.records.get(t);if(void 0===a){const c=("function"==typeof(e=t)||"object"==typeof e&&e instanceof m)&&wt(t);a=c&&this.injectableDefInScope(c)?qs(Ac(t),ca):null,this.records.set(t,a)}if(null!=a)return this.hydrate(t,a)}return(r&he.Self?zh():this.parent).get(t,n=r&he.Optional&&n===Zo?null:n)}catch(o){if("NullInjectorError"===o.name){if((o[Fs]=o[Fs]||[]).unshift(G(t)),i)throw o;return hf(o,t,"R3InjectorError",this.source)}throw o}finally{ye(s),Ls(i)}var e}_resolveInjectorDefTypes(){this.injectorDefTypes.forEach(t=>this.get(t))}toString(){const t=[];return this.records.forEach((r,i)=>t.push(G(i))),`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new Error("Injector has already been destroyed.")}processInjectorType(t,n,r){if(!(t=te(t)))return!1;let i=Pr(t);const s=null==i&&t.ngModule||void 0,o=void 0===s?t:s,a=-1!==r.indexOf(o);if(void 0!==s&&(i=Pr(s)),null==i)return!1;if(null!=i.imports&&!a){let y;r.push(o);try{Cr(i.imports,C=>{this.processInjectorType(C,n,r)&&(void 0===y&&(y=[]),y.push(C))})}finally{}if(void 0!==y)for(let C=0;C<y.length;C++){const{ngModule:v,providers:S}=y[C];Cr(S,x=>this.processProvider(x,v,S||Be))}}this.injectorDefTypes.add(o);const c=yn(o)||(()=>new o);this.records.set(o,qs(c,ca));const h=i.providers;if(null!=h&&!a){const y=t;Cr(h,C=>this.processProvider(C,y,h))}return void 0!==s&&void 0!==t.providers}processProvider(t,n,r){let i=Qs(t=te(t))?t:te(t&&t.provide);const s=Qh(e=t)?qs(void 0,e.useValue):qs(function(e,t,n){let r;if(Qs(e)){const i=te(e);return yn(i)||Ac(i)}if(Qh(e))r=()=>te(e.useValue);else if(function(e){return!(!e||!e.useFactory)}(e))r=()=>e.useFactory(...Qi(e.deps||[]));else if(function(e){return!(!e||!e.useExisting)}(e))r=()=>bt(te(e.useExisting));else{const i=te(e&&(e.useClass||e.provide));if(!function(e){return!!e.deps}(e))return yn(i)||Ac(i);r=()=>new i(...Qi(e.deps))}return r}(e),ca);var e;if(Qs(t)||!0!==t.multi)this.records.get(i);else{let o=this.records.get(i);o||(o=qs(void 0,ca,!0),o.factory=()=>Qi(o.multi),this.records.set(i,o)),i=t,o.multi.push(t)}this.records.set(i,s)}hydrate(t,n){return n.value===ca&&(n.value=$C,n.value=n.factory()),"object"==typeof n.value&&n.value&&null!==(e=n.value)&&"object"==typeof e&&"function"==typeof e.ngOnDestroy&&this.onDestroy.add(n.value),n.value;var e}injectableDefInScope(t){if(!t.providedIn)return!1;const n=te(t.providedIn);return"string"==typeof n?"any"===n||n===this.scope:this.injectorDefTypes.has(n)}}function Ac(e){const t=wt(e),n=null!==t?t.factory:yn(e);if(null!==n)return n;if(e instanceof m)throw new Error(`Token ${G(e)} is missing a \u0275prov definition.`);if(e instanceof Function)return function(e){const t=e.length;if(t>0){const r=function(e,t){const n=[];for(let r=0;r<e;r++)n.push(t);return n}(t,"?");throw new Error(`Can't resolve all parameters for ${G(e)}: (${r.join(", ")}).`)}const n=function(e){const t=e&&(e[or]||e[Ai]);if(t){const n=function(e){if(e.hasOwnProperty("name"))return e.name;const t=(""+e).match(/^function\s*([^\s(]+)/);return null===t?"":t[1]}(e);return console.warn(`DEPRECATED: DI is instantiating a token "${n}" that inherits its @Injectable decorator but does not provide one itself.\nThis will become an error in a future version of Angular. Please add @Injectable() to the "${n}" class.`),t}return null}(e);return null!==n?()=>n.factory(e):()=>new e}(e);throw new Error("unreachable")}function qs(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function Qh(e){return null!==e&&"object"==typeof e&&jl in e}function Qs(e){return"function"==typeof e}const Kh=function(e,t,n){return function(e,t=null,n=null,r){const i=Yh(e,t,n,r);return i._resolveInjectorDefTypes(),i}({name:n},t,e,n)};let ct=(()=>{class e{static create(n,r){return Array.isArray(n)?Kh(n,r,""):Kh(n.providers,n.parent,n.name||"")}}return e.THROW_IF_NOT_FOUND=Zo,e.NULL=new Wh,e.\u0275prov=ut({token:e,providedIn:"any",factory:()=>bt(Ys)}),e.__NG_ELEMENT_ID__=-1,e})();function fE(e,t){Uo(Ql(e)[1],Ct())}let xu=null;function Ks(){if(!xu){const e=je.Symbol;if(e&&e.iterator)xu=e.iterator;else{const t=Object.getOwnPropertyNames(Map.prototype);for(let n=0;n<t.length;++n){const r=t[n];"entries"!==r&&"size"!==r&&Map.prototype[r]===Map.prototype.entries&&(xu=r)}}}return xu}function fa(e){return!!Lc(e)&&(Array.isArray(e)||!(e instanceof Map)&&Ks()in e)}function Lc(e){return null!==e&&("function"==typeof e||"object"==typeof e)}function Qt(e,t,n){return!Object.is(e[t],n)&&(e[t]=n,!0)}function kc(e,t,n,r){const i=z();return Qt(i,fi(),t)&&(Ue(),wr(at(),i,e,t,n,r)),kc}function wp(e,t,n,r,i,s,o,a){const c=z(),h=Ue(),y=e+20,C=h.firstCreatePass?function(e,t,n,r,i,s,o,a,c){const h=t.consts,y=Gs(t,e,4,o||null,hn(h,a));Ec(t,n,y,hn(h,c)),Uo(t,y);const C=y.tViews=Mu(2,y,r,i,s,t.directiveRegistry,t.pipeRegistry,null,t.schemas,h);return null!==t.queries&&(t.queries.template(t,y),C.queries=t.queries.embeddedTView(y)),y}(y,h,c,t,n,r,i,s,o):h.data[y];Dn(C,!1);const v=c[11].createComment("");bu(h,c,v,C),qt(v,c),Au(c,c[y]=Vh(v,c,v,C)),oi(C)&&_c(h,c,C),null!=o&&Cc(c,C,a)}function Ip(e){return function(e,t){return e[t]}(ve.lFrame.contextLView,20+e)}function pa(e,t=he.Default){const n=z();return null===n?bt(e,t):Pl(Ct(),n,te(e),t)}function Hc(e,t,n){const r=z();return Qt(r,fi(),t)&&vn(Ue(),at(),r,e,t,r[11],n,!1),Hc}function $c(e,t,n,r,i){const o=i?"class":"style";Gh(e,n,t.inputs[o],o,r)}function Gc(e,t,n,r){const i=z(),s=Ue(),o=20+e,a=i[11],c=i[o]=nc(a,t,ve.lFrame.currentNamespace),h=s.firstCreatePass?function(e,t,n,r,i,s,o){const a=t.consts,h=Gs(t,e,2,i,hn(a,s));return Ec(t,n,h,hn(a,o)),null!==h.attrs&&Ru(h,h.attrs,!1),null!==h.mergedAttrs&&Ru(h,h.mergedAttrs,!0),null!==t.queries&&t.queries.elementStart(t,h),h}(o,s,i,0,t,n,r):s.data[o];Dn(h,!0);const y=h.mergedAttrs;null!==y&&ws(a,c,y);const C=h.classes;null!==C&&uc(a,c,C);const v=h.styles;null!==v&&ah(a,c,v),64!=(64&h.flags)&&bu(s,i,c,h),0===ve.lFrame.elementDepthCount&&qt(c,i),ve.lFrame.elementDepthCount++,oi(h)&&(_c(s,i,h),function(e,t,n){if(Li(t)){const i=t.directiveEnd;for(let s=t.directiveStart;s<i;s++){const o=e.data[s];o.contentQueries&&o.contentQueries(1,n[s],s)}}}(s,h,i)),null!==r&&Cc(i,h)}function Wc(){let e=Ct();Ds()?ve.lFrame.isParent=!1:(e=e.parent,Dn(e,!1));const t=e;ve.lFrame.elementDepthCount--;const n=Ue();n.firstCreatePass&&(Uo(n,e),Li(e)&&n.queries.elementEnd(e)),null!=t.classesWithoutHost&&function(e){return 0!=(16&e.flags)}(t)&&$c(n,t,z(),t.classesWithoutHost,!0),null!=t.stylesWithoutHost&&function(e){return 0!=(32&e.flags)}(t)&&$c(n,t,z(),t.stylesWithoutHost,!1)}function kp(e,t,n,r){Gc(e,t,n,r),Wc()}function Bp(){return z()}function zc(e){return!!e&&"function"==typeof e.then}function Up(e){return!!e&&"function"==typeof e.subscribe}const Yc=Up;function qc(e,t,n,r){const i=z(),s=Ue(),o=Ct();return function(e,t,n,r,i,s,o,a){const c=oi(r),y=e.firstCreatePass&&Uh(e),C=t[8],v=Bh(t);let S=!0;if(3&r.type||a){const O=lt(r,t),K=a?a(O):O,$=v.length,ce=a?be=>a(Oe(be[r.index])):r.index;if(ge(n)){let be=null;if(!a&&c&&(be=function(e,t,n,r){const i=e.cleanup;if(null!=i)for(let s=0;s<i.length-1;s+=2){const o=i[s];if(o===n&&i[s+1]===r){const a=t[7],c=i[s+2];return a.length>c?a[c]:null}"string"==typeof o&&(s+=2)}return null}(e,t,i,r.index)),null!==be)(be.__ngLastListenerFn__||be).__ngNextListenerFn__=s,be.__ngLastListenerFn__=s,S=!1;else{s=Qc(r,t,C,s,!1);const Ve=n.listen(K,i,s);v.push(s,Ve),y&&y.push(i,ce,$,$+1)}}else s=Qc(r,t,C,s,!0),K.addEventListener(i,s,o),v.push(s),y&&y.push(i,ce,$,o)}else s=Qc(r,t,C,s,!1);const x=r.outputs;let k;if(S&&null!==x&&(k=x[i])){const O=k.length;if(O)for(let K=0;K<O;K+=2){const Rt=t[k[K]][k[K+1]].subscribe(s),jn=v.length;v.push(s,Rt),y&&y.push(i,r.index,jn,-(jn+1))}}}(s,i,i[11],o,e,t,!!n,r),qc}function Gp(e,t,n,r){try{return!1!==n(r)}catch(i){return $h(e,i),!1}}function Qc(e,t,n,r,i){return function s(o){if(o===Function)return r;const a=2&e.flags?Mt(e.index,t):t;0==(32&t[2])&&wc(a);let c=Gp(t,0,r,o),h=s.__ngNextListenerFn__;for(;h;)c=Gp(t,0,h,o)&&c,h=h.__ngNextListenerFn__;return i&&!1===c&&(o.preventDefault(),o.returnValue=!1),c}}function gg(e,t=""){const n=z(),r=Ue(),i=e+20,s=r.firstCreatePass?Gs(r,i,1,t,null):r.data[i],o=n[i]=function(e,t){return ge(e)?e.createText(t):e.createTextNode(t)}(n[11],t);bu(r,n,o,s),Dn(s,!1)}function Fu(e,t,n){const r=z(),i=function(e,t,n,r){return Qt(e,fi(),n)?t+se(n)+r:Re}(r,e,t,n);return i!==Re&&Xr(r,$t(),i),Fu}function Jc(e,t,n){const r=z();return Qt(r,fi(),t)&&vn(Ue(),at(),r,e,t,r[11],n,!0),Jc}const Xi=void 0;var Vv=["en",[["a","p"],["AM","PM"],Xi],[["AM","PM"],Xi,Xi],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Xi,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Xi,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Xi,"{1} 'at' {0}",Xi],[".",",",";","%","+","-","E","\xd7","\u2030","\u221e","NaN",":"],["#,##0.###","#,##0%","\xa4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",function(e){const t=Math.floor(Math.abs(e)),n=e.toString().replace(/^[^.]*\.?/,"").length;return 1===t&&0===n?1:5}];let uo={};function Xc(e){const t=function(e){return e.toLowerCase().replace(/_/g,"-")}(e);let n=Lg(t);if(n)return n;const r=t.split("-")[0];if(n=Lg(r),n)return n;if("en"===r)return Vv;throw new Error(`Missing locale data for the locale "${e}".`)}function Fg(e){return Xc(e)[Ce.PluralCase]}function Lg(e){return e in uo||(uo[e]=je.ng&&je.ng.common&&je.ng.common.locales&&je.ng.common.locales[e]),uo[e]}var Ce=(()=>((Ce=Ce||{})[Ce.LocaleId=0]="LocaleId",Ce[Ce.DayPeriodsFormat=1]="DayPeriodsFormat",Ce[Ce.DayPeriodsStandalone=2]="DayPeriodsStandalone",Ce[Ce.DaysFormat=3]="DaysFormat",Ce[Ce.DaysStandalone=4]="DaysStandalone",Ce[Ce.MonthsFormat=5]="MonthsFormat",Ce[Ce.MonthsStandalone=6]="MonthsStandalone",Ce[Ce.Eras=7]="Eras",Ce[Ce.FirstDayOfWeek=8]="FirstDayOfWeek",Ce[Ce.WeekendRange=9]="WeekendRange",Ce[Ce.DateFormat=10]="DateFormat",Ce[Ce.TimeFormat=11]="TimeFormat",Ce[Ce.DateTimeFormat=12]="DateTimeFormat",Ce[Ce.NumberSymbols=13]="NumberSymbols",Ce[Ce.NumberFormats=14]="NumberFormats",Ce[Ce.CurrencyCode=15]="CurrencyCode",Ce[Ce.CurrencySymbol=16]="CurrencySymbol",Ce[Ce.CurrencyName=17]="CurrencyName",Ce[Ce.Currencies=18]="Currencies",Ce[Ce.Directionality=19]="Directionality",Ce[Ce.PluralCase=20]="PluralCase",Ce[Ce.ExtraData=21]="ExtraData",Ce))();const Lu="en-US";let kg=Lu;function ed(e){Et(e,"Expected localeId to be defined"),"string"==typeof e&&(kg=e.toLowerCase().replace(/_/g,"-"))}class um{}const cm="ngComponent";class jb{resolveComponentFactory(t){throw function(e){const t=Error(`No component factory found for ${G(e)}. Did you add it to @NgModule.entryComponents?`);return t[cm]=e,t}(t)}}let co=(()=>{class e{}return e.NULL=new jb,e})();function Uu(...e){}function fo(e,t){return new Mr(lt(e,t))}const Hb=function(){return fo(Ct(),z())};let Mr=(()=>{class e{constructor(n){this.nativeElement=n}}return e.__NG_ELEMENT_ID__=Hb,e})();function dm(e){return e instanceof Mr?e.nativeElement:e}class Hu{}let $b=(()=>{class e{}return e.__NG_ELEMENT_ID__=()=>Wb(),e})();const Wb=function(){const e=z(),n=Mt(Ct().index,e);return function(e){return e[11]}(nn(n)?n:e)};let ud=(()=>{class e{}return e.\u0275prov=ut({token:e,providedIn:"root",factory:()=>null}),e})();class fm{constructor(t){this.full=t,this.major=t.split(".")[0],this.minor=t.split(".")[1],this.patch=t.split(".").slice(2).join(".")}}const hm=new fm("12.2.16");class pm{constructor(){}supports(t){return fa(t)}create(t){return new Qb(t)}}const qb=(e,t)=>t;class Qb{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||qb}forEachItem(t){let n;for(n=this._itHead;null!==n;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,i=0,s=null;for(;n||r;){const o=!r||n&&n.currentIndex<mm(r,i,s)?n:r,a=mm(o,i,s),c=o.currentIndex;if(o===r)i--,r=r._nextRemoved;else if(n=n._next,null==o.previousIndex)i++;else{s||(s=[]);const h=a-i,y=c-i;if(h!=y){for(let v=0;v<h;v++){const S=v<s.length?s[v]:s[v]=0,x=S+v;y<=x&&x<h&&(s[v]=S+1)}s[o.previousIndex]=y-h}}a!==c&&t(o,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;null!==n;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;null!==n;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;null!==n;n=n._nextIdentityChange)t(n)}diff(t){if(null==t&&(t=[]),!fa(t))throw new Error(`Error trying to diff '${G(t)}'. Only arrays and iterables are allowed`);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let i,s,o,n=this._itHead,r=!1;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)s=t[a],o=this._trackByFn(a,s),null!==n&&Object.is(n.trackById,o)?(r&&(n=this._verifyReinsertion(n,s,o,a)),Object.is(n.item,s)||this._addIdentityChange(n,s)):(n=this._mismatch(n,s,o,a),r=!0),n=n._next}else i=0,function(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{const n=e[Ks()]();let r;for(;!(r=n.next()).done;)t(r.value)}}(t,a=>{o=this._trackByFn(i,a),null!==n&&Object.is(n.trackById,o)?(r&&(n=this._verifyReinsertion(n,a,o,i)),Object.is(n.item,a)||this._addIdentityChange(n,a)):(n=this._mismatch(n,a,o,i),r=!0),n=n._next,i++}),this.length=i;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return null!==this._additionsHead||null!==this._movesHead||null!==this._removalsHead||null!==this._identityChangesHead}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;null!==t;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;null!==t;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,i){let s;return null===t?s=this._itTail:(s=t._prev,this._remove(t)),null!==(t=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,s,i)):null!==(t=null===this._linkedRecords?null:this._linkedRecords.get(r,i))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,s,i)):t=this._addAfter(new Kb(n,r),s,i),t}_verifyReinsertion(t,n,r,i){let s=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null);return null!==s?t=this._reinsertAfter(s,t._prev,i):t.currentIndex!=i&&(t.currentIndex=i,this._addToMoves(t,i)),t}_truncate(t){for(;null!==t;){const n=t._next;this._addToRemovals(this._unlink(t)),t=n}null!==this._unlinkedRecords&&this._unlinkedRecords.clear(),null!==this._additionsTail&&(this._additionsTail._nextAdded=null),null!==this._movesTail&&(this._movesTail._nextMoved=null),null!==this._itTail&&(this._itTail._next=null),null!==this._removalsTail&&(this._removalsTail._nextRemoved=null),null!==this._identityChangesTail&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){null!==this._unlinkedRecords&&this._unlinkedRecords.remove(t);const i=t._prevRemoved,s=t._nextRemoved;return null===i?this._removalsHead=s:i._nextRemoved=s,null===s?this._removalsTail=i:s._prevRemoved=i,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail=null===this._additionsTail?this._additionsHead=t:this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){const i=null===n?this._itHead:n._next;return t._next=i,t._prev=n,null===i?this._itTail=t:i._prev=t,null===n?this._itHead=t:n._next=t,null===this._linkedRecords&&(this._linkedRecords=new gm),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){null!==this._linkedRecords&&this._linkedRecords.remove(t);const n=t._prev,r=t._next;return null===n?this._itHead=r:n._next=r,null===r?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail=null===this._movesTail?this._movesHead=t:this._movesTail._nextMoved=t),t}_addToRemovals(t){return null===this._unlinkedRecords&&(this._unlinkedRecords=new gm),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,null===this._removalsTail?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail=null===this._identityChangesTail?this._identityChangesHead=t:this._identityChangesTail._nextIdentityChange=t,t}}class Kb{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}}class Zb{constructor(){this._head=null,this._tail=null}add(t){null===this._head?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;null!==r;r=r._nextDup)if((null===n||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){const n=t._prevDup,r=t._nextDup;return null===n?this._head=r:n._nextDup=r,null===r?this._tail=n:r._prevDup=n,null===this._head}}class gm{constructor(){this.map=new Map}put(t){const n=t.trackById;let r=this.map.get(n);r||(r=new Zb,this.map.set(n,r)),r.add(t)}get(t,n){const i=this.map.get(t);return i?i.get(t,n):null}remove(t){const n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return 0===this.map.size}clear(){this.map.clear()}}function mm(e,t,n){const r=e.previousIndex;if(null===r)return r;let i=0;return n&&r<n.length&&(i=n[r]),r+t+i}class ym{constructor(){}supports(t){return t instanceof Map||Lc(t)}create(){return new Jb}}class Jb{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return null!==this._additionsHead||null!==this._changesHead||null!==this._removalsHead}forEachItem(t){let n;for(n=this._mapHead;null!==n;n=n._next)t(n)}forEachPreviousItem(t){let n;for(n=this._previousMapHead;null!==n;n=n._nextPrevious)t(n)}forEachChangedItem(t){let n;for(n=this._changesHead;null!==n;n=n._nextChanged)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}diff(t){if(t){if(!(t instanceof Map||Lc(t)))throw new Error(`Error trying to diff '${G(t)}'. Only maps and objects are allowed`)}else t=new Map;return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(t,(r,i)=>{if(n&&n.key===i)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{const s=this._getOrCreateRecordForKey(i,r);n=this._insertBeforeOrAppend(n,s)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;null!==r;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,n){if(t){const r=t._prev;return n._next=t,n._prev=r,t._prev=n,r&&(r._next=n),t===this._mapHead&&(this._mapHead=n),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(t,n){if(this._records.has(t)){const i=this._records.get(t);this._maybeAddToChanges(i,n);const s=i._prev,o=i._next;return s&&(s._next=o),o&&(o._prev=s),i._next=null,i._prev=null,i}const r=new Xb(t);return this._records.set(t,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;null!==t;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;null!=t;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,n){Object.is(n,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=n,this._addToChanges(t))}_addToAdditions(t){null===this._additionsHead?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){null===this._changesHead?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,n){t instanceof Map?t.forEach(n):Object.keys(t).forEach(r=>n(t[r],r))}}class Xb{constructor(t){this.key=t,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}}function Dm(){return new $u([new pm])}let $u=(()=>{class e{constructor(n){this.factories=n}static create(n,r){if(null!=r){const i=r.factories.slice();n=n.concat(i)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Dm()),deps:[[e,new Ei,new Qr]]}}find(n){const r=this.factories.find(i=>i.supports(n));if(null!=r)return r;throw new Error(`Cannot find a differ supporting object '${n}' of type '${function(e){return e.name||typeof e}(n)}'`)}}return e.\u0275prov=ut({token:e,providedIn:"root",factory:Dm}),e})();function _m(){return new Gu([new ym])}let Gu=(()=>{class e{constructor(n){this.factories=n}static create(n,r){if(r){const i=r.factories.slice();n=n.concat(i)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||_m()),deps:[[e,new Ei,new Qr]]}}find(n){const r=this.factories.find(i=>i.supports(n));if(r)return r;throw new Error(`Cannot find a differ supporting object '${n}'`)}}return e.\u0275prov=ut({token:e,providedIn:"root",factory:_m}),e})();function Wu(e,t,n,r,i=!1){for(;null!==n;){const s=t[n.index];if(null!==s&&r.push(Oe(s)),zt(s))for(let a=10;a<s.length;a++){const c=s[a],h=c[1].firstChild;null!==h&&Wu(c[1],c,h,r)}const o=n.type;if(8&o)Wu(e,t,n.child,r);else if(32&o){const a=Jl(n,t);let c;for(;c=a();)r.push(c)}else if(16&o){const a=ih(t,n);if(Array.isArray(a))r.push(...a);else{const c=sa(t[16]);Wu(c[1],c,a,r,!0)}}n=i?n.projectionNext:n.next}return r}class Ca{constructor(t,n){this._lView=t,this._cdRefInjectingView=n,this._appRef=null,this._attachedToViewContainer=!1}get rootNodes(){const t=this._lView,n=t[1];return Wu(n,t,n.firstChild,[])}get context(){return this._lView[8]}set context(t){this._lView[8]=t}get destroyed(){return 256==(256&this._lView[2])}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){const t=this._lView[3];if(zt(t)){const n=t[8],r=n?n.indexOf(this):-1;r>-1&&(rc(t,r),qi(n,r))}this._attachedToViewContainer=!1}Qf(this._lView[1],this._lView)}onDestroy(t){Rh(this._lView[1],this._lView,null,t)}markForCheck(){wc(this._cdRefInjectingView||this._lView)}detach(){this._lView[2]&=-129}reattach(){this._lView[2]|=128}detectChanges(){Tc(this._lView[1],this._lView,this.context)}checkNoChanges(){!function(e,t,n){Cs(!0);try{Tc(e,t,n)}finally{Cs(!1)}}(this._lView[1],this._lView,this.context)}attachToViewContainerRef(){if(this._appRef)throw new Error("This view is already attached directly to the ApplicationRef!");this._attachedToViewContainer=!0}detachFromAppRef(){var t;this._appRef=null,oa(this._lView[1],t=this._lView,t[11],2,null,null)}attachToAppRef(t){if(this._attachedToViewContainer)throw new Error("This view is already attached to a ViewContainer!");this._appRef=t}}class tw extends Ca{constructor(t){super(t),this._view=t}detectChanges(){jh(this._view)}checkNoChanges(){!function(e){Cs(!0);try{jh(e)}finally{Cs(!1)}}(this._view)}get context(){return null}}const rw=function(e){return function(e,t,n){if(si(e)&&!n){const r=Mt(e.index,t);return new Ca(r,r)}return 47&e.type?new Ca(t[16],t):null}(Ct(),z(),16==(16&e))};let iw=(()=>{class e{}return e.__NG_ELEMENT_ID__=rw,e})();const aw=[new ym],lw=new $u([new pm]),cw=new Gu(aw),fw=function(){return zu(Ct(),z())};let Ea=(()=>{class e{}return e.__NG_ELEMENT_ID__=fw,e})();const hw=Ea,pw=class extends hw{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}createEmbeddedView(t){const n=this._declarationTContainer.tViews,r=aa(this._declarationLView,n,t,16,null,n.declTNode,null,null,null,null);r[17]=this._declarationLView[this._declarationTContainer.index];const s=this._declarationLView[19];return null!==s&&(r[19]=s.createEmbeddedView(n)),ua(n,r,t),new Ca(r)}};function zu(e,t){return 4&e.type?new pw(t,e,fo(e,t)):null}class es{}class Cm{}const yw=function(){return bm(Ct(),z())};let Yu=(()=>{class e{}return e.__NG_ELEMENT_ID__=yw,e})();const _w=Yu,Em=class extends _w{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return fo(this._hostTNode,this._hostLView)}get injector(){return new mi(this._hostTNode,this._hostLView)}get parentInjector(){const t=Wi(this._hostTNode,this._hostLView);if(au(t)){const n=pi(t,this._hostLView),r=zr(t);return new mi(n[1].data[r+8],n)}return new mi(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){const n=vm(this._lContainer);return null!==n&&n[t]||null}get length(){return this._lContainer.length-10}createEmbeddedView(t,n,r){const i=t.createEmbeddedView(n||{});return this.insert(i,r),i}createComponent(t,n,r,i,s){const o=r||this.parentInjector;if(!s&&null==t.ngModule&&o){const c=o.get(es,null);c&&(s=c)}const a=t.create(o,i,void 0,s);return this.insert(a.hostView,n),a}insert(t,n){const r=t._lView,i=r[1];if(zt(r[3])){const y=this.indexOf(t);if(-1!==y)this.detach(y);else{const C=r[3],v=new Em(C,C[6],C[3]);v.detach(v.indexOf(t))}}const s=this._adjustIndex(n),o=this._lContainer;!function(e,t,n,r){const i=10+r,s=n.length;r>0&&(n[i-1][4]=t),r<s-10?(t[4]=n[i],hu(n,10+r,t)):(n.push(t),t[4]=null),t[3]=n;const o=t[17];null!==o&&n!==o&&function(e,t){const n=e[9];t[16]!==t[3][3][16]&&(e[2]=!0),null===n?e[9]=[t]:n.push(t)}(o,t);const a=t[19];null!==a&&a.insertView(e),t[2]|=128}(i,r,o,s);const a=oc(s,o),c=r[11],h=vu(c,o[7]);return null!==h&&function(e,t,n,r,i,s){r[0]=i,r[6]=t,oa(e,r,n,1,i,s)}(i,o[6],c,r,h,a),t.attachToViewContainerRef(),hu(ld(o),s,t),t}move(t,n){return this.insert(t,n)}indexOf(t){const n=vm(this._lContainer);return null!==n?n.indexOf(t):-1}remove(t){const n=this._adjustIndex(t,-1),r=rc(this._lContainer,n);r&&(qi(ld(this._lContainer),n),Qf(r[1],r))}detach(t){const n=this._adjustIndex(t,-1),r=rc(this._lContainer,n);return r&&null!=qi(ld(this._lContainer),n)?new Ca(r):null}_adjustIndex(t,n=0){return null==t?this.length+n:t}};function vm(e){return e[8]}function ld(e){return e[8]||(e[8]=[])}function bm(e,t){let n;const r=t[e.index];if(zt(r))n=r;else{let i;if(8&e.type)i=Oe(r);else{const s=t[11];i=s.createComment("");const o=lt(e,t);Zi(s,vu(s,o),i,function(e,t){return ge(e)?e.nextSibling(t):t.nextSibling}(s,o),!1)}t[e.index]=n=Vh(r,t,i,e),Au(t,n)}return new Em(n,e,t)}const mo={};class $m extends co{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){const n=ht(t);return new Wm(n,this.ngModule)}}function Gm(e){const t=[];for(let n in e)e.hasOwnProperty(n)&&t.push({propName:e[n],templateName:n});return t}const g0=new m("SCHEDULER_TOKEN",{providedIn:"root",factory:()=>Uf});class Wm extends um{constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=t.selectors.map(nC).join(","),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}get inputs(){return Gm(this.componentDef.inputs)}get outputs(){return Gm(this.componentDef.outputs)}create(t,n,r,i){const s=(i=i||this.ngModule)?function(e,t){return{get:(n,r,i)=>{const s=e.get(n,mo,i);return s!==mo||r===mo?s:t.get(n,r,i)}}}(t,i.injector):t,o=s.get(Hu,Je),a=s.get(ud,null),c=o.createRenderer(null,this.componentDef),h=this.componentDef.selectors[0][0]||"div",y=r?function(e,t,n){if(ge(e))return e.selectRootElement(t,n===Pe.ShadowDom);let r="string"==typeof t?e.querySelector(t):t;return r.textContent="",r}(c,r,this.componentDef.encapsulation):nc(o.createRenderer(null,this.componentDef),h,function(e){const t=e.toLowerCase();return"svg"===t?g:"math"===t?"http://www.w3.org/1998/MathML/":null}(h)),C=this.componentDef.onPush?576:528,v=function(e,t){return{components:[],scheduler:e||Uf,clean:BC,playerHandler:t||null,flags:0}}(),S=Mu(0,null,null,1,0,null,null,null,null,null),x=aa(null,S,v,C,null,null,o,c,a,s);let k,O;pr(x);try{const K=function(e,t,n,r,i,s){const o=n[1];n[20]=e;const c=Gs(o,20,2,"#host",null),h=c.mergedAttrs=t.hostAttrs;null!==h&&(Ru(c,h,!0),null!==e&&(ws(i,e,h),null!==c.classes&&uc(i,e,c.classes),null!==c.styles&&ah(i,e,c.styles)));const y=r.createRenderer(e,t),C=aa(n,Sh(t),null,t.onPush?64:16,n[20],c,r,y,s||null,null);return o.firstCreatePass&&(As($i(c,n),o,t.type),Fh(o,c),Lh(c,n.length,1)),Au(n,C),n[20]=C}(y,this.componentDef,x,o,c);if(y)if(r)ws(c,y,["ng-version",hm.full]);else{const{attrs:$,classes:ce}=function(e){const t=[],n=[];let r=1,i=2;for(;r<e.length;){let s=e[r];if("string"==typeof s)2===i?""!==s&&t.push(s,e[++r]):8===i&&n.push(s);else{if(!Yn(i))break;i=s}r++}return{attrs:t,classes:n}}(this.componentDef.selectors[0]);$&&ws(c,y,$),ce&&ce.length>0&&uc(c,y,ce.join(" "))}if(O=Pn(S,20),void 0!==n){const $=O.projection=[];for(let ce=0;ce<this.ngContentSelectors.length;ce++){const be=n[ce];$.push(null!=be?Array.from(be):null)}}k=function(e,t,n,r,i){const s=n[1],o=function(e,t,n){const r=Ct();e.firstCreatePass&&(n.providersResolver&&n.providersResolver(n),kh(e,r,t,Ws(e,t,1,null),n));const i=zi(t,e,r.directiveStart,r);qt(i,t);const s=lt(r,t);return s&&qt(s,t),i}(s,n,t);if(r.components.push(o),e[8]=o,i&&i.forEach(c=>c(o,t)),t.contentQueries){const c=Ct();t.contentQueries(1,o,c.directiveStart)}const a=Ct();return!s.firstCreatePass||null===t.hostBindings&&null===t.hostAttrs||(gr(a.index),Nh(n[1],a,0,a.directiveStart,a.directiveEnd,t),Oh(t,o)),o}(K,this.componentDef,x,v,[fE]),ua(S,x,null)}finally{vs()}return new D0(this.componentType,k,fo(O,x),x,O)}}class D0 extends class{}{constructor(t,n,r,i,s){super(),this.location=r,this._rootLView=i,this._tNode=s,this.instance=n,this.hostView=this.changeDetectorRef=new tw(i),this.componentType=t}get injector(){return new mi(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}}const yo=new Map;class E0 extends es{constructor(t,n){super(),this._parent=n,this._bootstrapComponents=[],this.injector=this,this.destroyCbs=[],this.componentFactoryResolver=new $m(this);const r=Tt(t),i=t[Pi]||null;i&&ed(i),this._bootstrapComponents=vr(r.bootstrap),this._r3Injector=Yh(t,n,[{provide:es,useValue:this},{provide:co,useValue:this.componentFactoryResolver}],G(t)),this._r3Injector._resolveInjectorDefTypes(),this.instance=this.get(t)}get(t,n=ct.THROW_IF_NOT_FOUND,r=he.Default){return t===ct||t===es||t===Ys?this:this._r3Injector.get(t,n,r)}destroy(){const t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}}class vd extends Cm{constructor(t){super(),this.moduleType=t,null!==Tt(t)&&function(e){const t=new Set;!function n(r){const i=Tt(r,!0),s=i.id;null!==s&&(function(e,t,n){if(t&&t!==n)throw new Error(`Duplicate module registered for ${e} - ${G(t)} vs ${G(t.name)}`)}(s,yo.get(s),r),yo.set(s,r));const o=vr(i.imports);for(const a of o)t.has(a)||(t.add(a),n(a))}(e)}(t)}create(t){return new E0(this.moduleType,t)}}function bd(e){return t=>{setTimeout(e,void 0,t)}}const Ar=class extends W.xQ{constructor(t=!1){super(),this.__isAsync=t}emit(t){super.next(t)}subscribe(t,n,r){var i,s,o;let a=t,c=n||(()=>null),h=r;if(t&&"object"==typeof t){const C=t;a=null===(i=C.next)||void 0===i?void 0:i.bind(C),c=null===(s=C.error)||void 0===s?void 0:s.bind(C),h=null===(o=C.complete)||void 0===o?void 0:o.bind(C)}this.__isAsync&&(c=bd(c),a&&(a=bd(a)),h&&(h=bd(h)));const y=super.subscribe({next:a,error:c,complete:h});return t instanceof oe.w&&t.add(y),y}};function B0(){return this._results[Ks()]()}class Ju{constructor(t=!1){this._emitDistinctChangesOnly=t,this.dirty=!0,this._results=[],this._changesDetected=!1,this._changes=null,this.length=0,this.first=void 0,this.last=void 0;const n=Ks(),r=Ju.prototype;r[n]||(r[n]=B0)}get changes(){return this._changes||(this._changes=new Ar)}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){const r=this;r.dirty=!1;const i=On(t);(this._changesDetected=!function(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let i=e[r],s=t[r];if(n&&(i=n(i),s=n(s)),s!==i)return!1}return!0}(r._results,i,n))&&(r._results=i,r.length=i.length,r.last=i[this.length-1],r.first=i[0])}notifyOnChanges(){this._changes&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}setDirty(){this.dirty=!0}destroy(){this.changes.complete(),this.changes.unsubscribe()}}Symbol;class wd{constructor(t){this.queryList=t,this.matches=null}clone(){return new wd(this.queryList)}setDirty(){this.queryList.setDirty()}}class Id{constructor(t=[]){this.queries=t}createEmbeddedView(t){const n=t.queries;if(null!==n){const r=null!==t.contentQueries?t.contentQueries[0]:n.length,i=[];for(let s=0;s<r;s++){const o=n.getByIndex(s);i.push(this.queries[o.indexInDeclarationView].clone())}return new Id(i)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)null!==oy(t,n).matches&&this.queries[n].setDirty()}}class Xm{constructor(t,n,r=null){this.predicate=t,this.flags=n,this.read=r}}class Td{constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){const i=null!==n?n.length:0,s=this.getByIndex(r).embeddedTView(t,i);s&&(s.indexInDeclarationView=r,null!==n?n.push(s):n=[s])}return null!==n?new Td(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}}class Sd{constructor(t,n=-1){this.metadata=t,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new Sd(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&1!=(1&this.metadata.flags)){const n=this._declarationNodeIndex;let r=t.parent;for(;null!==r&&8&r.type&&r.index!==n;)r=r.parent;return n===(null!==r?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){const r=this.metadata.predicate;if(Array.isArray(r))for(let i=0;i<r.length;i++){const s=r[i];this.matchTNodeWithReadOption(t,n,$0(n,s)),this.matchTNodeWithReadOption(t,n,xs(n,t,s,!1,!1))}else r===Ea?4&n.type&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,xs(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(null!==r){const i=this.metadata.read;if(null!==i)if(i===Mr||i===Yu||i===Ea&&4&n.type)this.addMatch(n.index,-2);else{const s=xs(n,t,i,!1,!1);null!==s&&this.addMatch(n.index,s)}else this.addMatch(n.index,r)}}addMatch(t,n){null===this.matches?this.matches=[t,n]:this.matches.push(t,n)}}function $0(e,t){const n=e.localNames;if(null!==n)for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1];return null}function W0(e,t,n,r){return-1===n?function(e,t){return 11&e.type?fo(e,t):4&e.type?zu(e,t):null}(t,e):-2===n?function(e,t,n){return n===Mr?fo(t,e):n===Ea?zu(t,e):n===Yu?bm(t,e):void 0}(e,t,r):zi(e,e[1],n,t)}function ey(e,t,n,r){const i=t[19].queries[r];if(null===i.matches){const s=e.data,o=n.matches,a=[];for(let c=0;c<o.length;c+=2){const h=o[c];a.push(h<0?null:W0(t,s[h],o[c+1],n.metadata.read))}i.matches=a}return i.matches}function Md(e,t,n,r){const i=e.queries.getByIndex(n),s=i.matches;if(null!==s){const o=ey(e,t,i,n);for(let a=0;a<s.length;a+=2){const c=s[a];if(c>0)r.push(o[a/2]);else{const h=s[a+1],y=t[-c];for(let C=10;C<y.length;C++){const v=y[C];v[17]===v[3]&&Md(v[1],v,h,r)}if(null!==y[9]){const C=y[9];for(let v=0;v<C.length;v++){const S=C[v];Md(S[1],S,h,r)}}}}}return r}function ty(e){const t=z(),n=Ue(),r=Ka();$r(r+1);const i=oy(n,r);if(e.dirty&&Vi(t)===(2==(2&i.metadata.flags))){if(null===i.matches)e.reset([]);else{const s=i.crossesNgTemplate?Md(n,t,r,[]):ey(n,t,i,r);e.reset(s,dm),e.notifyOnChanges()}return!0}return!1}function ny(e,t,n,r){const i=Ue();if(i.firstCreatePass){const s=Ct();(function(e,t,n){null===e.queries&&(e.queries=new Td),e.queries.track(new Sd(t,n))})(i,new Xm(t,n,r),s.index),function(e,t){const n=e.contentQueries||(e.contentQueries=[]);t!==(n.length?n[n.length-1]:-1)&&n.push(e.queries.length-1,t)}(i,e),2==(2&n)&&(i.staticContentQueries=!0)}!function(e,t,n){const r=new Ju(4==(4&n));Rh(e,t,r,r.destroy),null===t[19]&&(t[19]=new Id),t[19].queries.push(new wd(r))}(i,z(),n)}function ry(){return e=z(),t=Ka(),e[19].queries[t].queryList;var e,t}function oy(e,t){return e.queries.getByIndex(t)}const Od=new m("Application Initializer");let _o=(()=>{class e{constructor(n){this.appInits=n,this.resolve=Uu,this.reject=Uu,this.initialized=!1,this.done=!1,this.donePromise=new Promise((r,i)=>{this.resolve=r,this.reject=i})}runInitializers(){if(this.initialized)return;const n=[],r=()=>{this.done=!0,this.resolve()};if(this.appInits)for(let i=0;i<this.appInits.length;i++){const s=this.appInits[i]();if(zc(s))n.push(s);else if(Yc(s)){const o=new Promise((a,c)=>{s.subscribe({complete:a,error:c})});n.push(o)}}Promise.all(n).then(()=>{r()}).catch(i=>{this.reject(i)}),0===n.length&&r(),this.initialized=!0}}return e.\u0275fac=function(n){return new(n||e)(bt(Od,8))},e.\u0275prov=ut({token:e,factory:e.\u0275fac}),e})();const wy=new m("AppId"),bI={provide:wy,useFactory:function(){return`${Fd()}${Fd()}${Fd()}`},deps:[]};function Fd(){return String.fromCharCode(97+Math.floor(25*Math.random()))}const Iy=new m("Platform Initializer"),Ty=new m("Platform ID"),Sy=new m("appBootstrapListener");let My=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}}return e.\u0275fac=function(n){return new(n||e)},e.\u0275prov=ut({token:e,factory:e.\u0275fac}),e})();const tl=new m("LocaleId"),Ay=new m("DefaultCurrencyCode");class II{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}}const Ld=function(e){return new vd(e)},TI=Ld,SI=function(e){return Promise.resolve(Ld(e))},Ry=function(e){const t=Ld(e),r=vr(Tt(e).declarations).reduce((i,s)=>{const o=ht(s);return o&&i.push(new Wm(o)),i},[]);return new II(t,r)},MI=Ry,AI=function(e){return Promise.resolve(Ry(e))};let Na=(()=>{class e{constructor(){this.compileModuleSync=TI,this.compileModuleAsync=SI,this.compileModuleAndAllComponentsSync=MI,this.compileModuleAndAllComponentsAsync=AI}clearCache(){}clearCacheFor(n){}getModuleId(n){}}return e.\u0275fac=function(n){return new(n||e)},e.\u0275prov=ut({token:e,factory:e.\u0275fac}),e})();const PI=(()=>Promise.resolve(0))();function kd(e){"undefined"==typeof Zone?PI.then(()=>{e&&e.apply(null,null)}):Zone.current.scheduleMicroTask("scheduleMicrotask",e)}class bn{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new Ar(!1),this.onMicrotaskEmpty=new Ar(!1),this.onStable=new Ar(!1),this.onError=new Ar(!1),"undefined"==typeof Zone)throw new Error("In this configuration Angular requires Zone.js");Zone.assertZonePatched();const i=this;i._nesting=0,i._outer=i._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(i._inner=i._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(i._inner=i._inner.fork(Zone.longStackTraceZoneSpec)),i.shouldCoalesceEventChangeDetection=!r&&n,i.shouldCoalesceRunChangeDetection=r,i.lastRequestAnimationFrameId=-1,i.nativeRequestAnimationFrame=function(){let e=je.requestAnimationFrame,t=je.cancelAnimationFrame;if("undefined"!=typeof Zone&&e&&t){const n=e[Zone.__symbol__("OriginalDelegate")];n&&(e=n);const r=t[Zone.__symbol__("OriginalDelegate")];r&&(t=r)}return{nativeRequestAnimationFrame:e,nativeCancelAnimationFrame:t}}().nativeRequestAnimationFrame,function(e){const t=()=>{!function(e){e.isCheckStableRunning||-1!==e.lastRequestAnimationFrameId||(e.lastRequestAnimationFrameId=e.nativeRequestAnimationFrame.call(je,()=>{e.fakeTopEventTask||(e.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{e.lastRequestAnimationFrameId=-1,jd(e),e.isCheckStableRunning=!0,Vd(e),e.isCheckStableRunning=!1},void 0,()=>{},()=>{})),e.fakeTopEventTask.invoke()}),jd(e))}(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,i,s,o,a)=>{try{return xy(e),n.invokeTask(i,s,o,a)}finally{(e.shouldCoalesceEventChangeDetection&&"eventTask"===s.type||e.shouldCoalesceRunChangeDetection)&&t(),Py(e)}},onInvoke:(n,r,i,s,o,a,c)=>{try{return xy(e),n.invoke(i,s,o,a,c)}finally{e.shouldCoalesceRunChangeDetection&&t(),Py(e)}},onHasTask:(n,r,i,s)=>{n.hasTask(i,s),r===i&&("microTask"==s.change?(e._hasPendingMicrotasks=s.microTask,jd(e),Vd(e)):"macroTask"==s.change&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(n,r,i,s)=>(n.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}(i)}static isInAngularZone(){return!0===Zone.current.get("isAngularZone")}static assertInAngularZone(){if(!bn.isInAngularZone())throw new Error("Expected to be in Angular Zone, but it is not!")}static assertNotInAngularZone(){if(bn.isInAngularZone())throw new Error("Expected to not be in Angular Zone, but it is!")}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,i){const s=this._inner,o=s.scheduleEventTask("NgZoneEvent: "+i,t,OI,Uu,Uu);try{return s.runTask(o,n,r)}finally{s.cancelTask(o)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}}const OI={};function Vd(e){if(0==e._nesting&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function jd(e){e.hasPendingMicrotasks=!!(e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&-1!==e.lastRequestAnimationFrameId)}function xy(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Py(e){e._nesting--,Vd(e)}class kI{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new Ar,this.onMicrotaskEmpty=new Ar,this.onStable=new Ar,this.onError=new Ar}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,i){return t.apply(n,r)}}let Ny=(()=>{class e{constructor(n){this._ngZone=n,this._pendingCount=0,this._isZoneStable=!0,this._didWork=!1,this._callbacks=[],this.taskTrackingZone=null,this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone="undefined"==typeof Zone?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._didWork=!0,this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{bn.assertNotInAngularZone(),kd(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._didWork=!0,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&0===this._pendingCount&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())kd(()=>{for(;0!==this._callbacks.length;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb(this._didWork)}this._didWork=!1});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>!r.updateCb||!r.updateCb(n)||(clearTimeout(r.timeoutId),!1)),this._didWork=!0}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,i){let s=-1;r&&r>0&&(s=setTimeout(()=>{this._callbacks=this._callbacks.filter(o=>o.timeoutId!==s),n(this._didWork,this.getPendingTasks())},r)),this._callbacks.push({doneCb:n,timeoutId:s,updateCb:i})}whenStable(n,r,i){if(i&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,i),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}findProviders(n,r,i){return[]}}return e.\u0275fac=function(n){return new(n||e)(bt(bn))},e.\u0275prov=ut({token:e,factory:e.\u0275fac}),e})(),Oy=(()=>{class e{constructor(){this._applications=new Map,Bd.addToWindow(this)}registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return Bd.findTestabilityInTree(this,n,r)}}return e.\u0275fac=function(n){return new(n||e)},e.\u0275prov=ut({token:e,factory:e.\u0275fac}),e})();class VI{addToWindow(t){}findTestabilityInTree(t,n,r){return null}}function jI(e){Bd=e}let Bd=new VI,Fy=!0,Ly=!1;function BI(){if(Ly)throw new Error("Cannot enable prod mode after platform setup.");Fy=!1}let tr;const Vy=new m("AllowMultipleToken");class YI{constructor(t,n){this.name=t,this.token=n}}function jy(e,t,n=[]){const r=`Platform: ${t}`,i=new m(r);return(s=[])=>{let o=By();if(!o||o.injector.get(Vy,!1))if(e)e(n.concat(s).concat({provide:i,useValue:!0}));else{const a=n.concat(s).concat({provide:i,useValue:!0},{provide:la,useValue:"platform"});!function(e){if(tr&&!tr.destroyed&&!tr.injector.get(Vy,!1))throw new Error("There can be only one platform. Destroy the previous one to create a new one.");tr=e.get(Uy);const t=e.get(Iy,null);t&&t.forEach(n=>n())}(ct.create({providers:a,name:r}))}return function(e){const t=By();if(!t)throw new Error("No platform exists!");if(!t.injector.get(e,null))throw new Error("A platform with a different configuration has been created. Please destroy it first.");return t}(i)}}function By(){return tr&&!tr.destroyed?tr:null}let Uy=(()=>{class e{constructor(n){this._injector=n,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(n,r){const a=function(e,t){let n;return n="noop"===e?new kI:("zone.js"===e?void 0:e)||new bn({enableLongStackTrace:(Ly=!0,Fy),shouldCoalesceEventChangeDetection:!!(null==t?void 0:t.ngZoneEventCoalescing),shouldCoalesceRunChangeDetection:!!(null==t?void 0:t.ngZoneRunCoalescing)}),n}(r?r.ngZone:void 0,{ngZoneEventCoalescing:r&&r.ngZoneEventCoalescing||!1,ngZoneRunCoalescing:r&&r.ngZoneRunCoalescing||!1}),c=[{provide:bn,useValue:a}];return a.run(()=>{const h=ct.create({providers:c,parent:this.injector,name:n.moduleType.name}),y=n.create(h),C=y.injector.get(Us,null);if(!C)throw new Error("No ErrorHandler. Is platform module (BrowserModule) included?");return a.runOutsideAngular(()=>{const v=a.onError.subscribe({next:S=>{C.handleError(S)}});y.onDestroy(()=>{Ud(this._modules,y),v.unsubscribe()})}),function(e,t,n){try{const r=n();return zc(r)?r.catch(i=>{throw t.runOutsideAngular(()=>e.handleError(i)),i}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}(C,a,()=>{const v=y.injector.get(_o);return v.runInitializers(),v.donePromise.then(()=>(ed(y.injector.get(tl,Lu)||Lu),this._moduleDoBootstrap(y),y))})})}bootstrapModule(n,r=[]){const i=Hy({},r);return function(e,t,n){const r=new vd(n);return Promise.resolve(r)}(0,0,n).then(s=>this.bootstrapModuleFactory(s,i))}_moduleDoBootstrap(n){const r=n.injector.get(Oa);if(n._bootstrapComponents.length>0)n._bootstrapComponents.forEach(i=>r.bootstrap(i));else{if(!n.instance.ngDoBootstrap)throw new Error(`The module ${G(n.instance.constructor)} was bootstrapped, but it does not declare "@NgModule.bootstrap" components nor a "ngDoBootstrap" method. Please define one of these.`);n.instance.ngDoBootstrap(r)}this._modules.push(n)}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new Error("The platform has already been destroyed!");this._modules.slice().forEach(n=>n.destroy()),this._destroyListeners.forEach(n=>n()),this._destroyed=!0}get destroyed(){return this._destroyed}}return e.\u0275fac=function(n){return new(n||e)(bt(ct))},e.\u0275prov=ut({token:e,factory:e.\u0275fac}),e})();function Hy(e,t){return Array.isArray(t)?t.reduce(Hy,e):Object.assign(Object.assign({},e),t)}let Oa=(()=>{class e{constructor(n,r,i,s,o){this._zone=n,this._injector=r,this._exceptionHandler=i,this._componentFactoryResolver=s,this._initStatus=o,this._bootstrapListeners=[],this._views=[],this._runningTick=!1,this._stable=!0,this.componentTypes=[],this.components=[],this._onMicrotaskEmptySubscription=this._zone.onMicrotaskEmpty.subscribe({next:()=>{this._zone.run(()=>{this.tick()})}});const a=new I.y(h=>{this._stable=this._zone.isStable&&!this._zone.hasPendingMacrotasks&&!this._zone.hasPendingMicrotasks,this._zone.runOutsideAngular(()=>{h.next(this._stable),h.complete()})}),c=new I.y(h=>{let y;this._zone.runOutsideAngular(()=>{y=this._zone.onStable.subscribe(()=>{bn.assertNotInAngularZone(),kd(()=>{!this._stable&&!this._zone.hasPendingMacrotasks&&!this._zone.hasPendingMicrotasks&&(this._stable=!0,h.next(!0))})})});const C=this._zone.onUnstable.subscribe(()=>{bn.assertInAngularZone(),this._stable&&(this._stable=!1,this._zone.runOutsideAngular(()=>{h.next(!1)}))});return()=>{y.unsubscribe(),C.unsubscribe()}});this.isStable=function(...e){let t=Number.POSITIVE_INFINITY,n=null,r=e[e.length-1];return(0,b.K)(r)?(n=e.pop(),e.length>1&&"number"==typeof e[e.length-1]&&(t=e.pop())):"number"==typeof r&&(t=e.pop()),null===n&&1===e.length&&e[0]instanceof I.y?e[0]:(0,ae.J)(t)((0,me.n)(e,n))}(a,c.pipe(e=>(0,V.x)()(function(e,t){return function(r){let i;i="function"==typeof e?e:function(){return e};const s=Object.create(r,fe.N);return s.source=r,s.subjectFactory=i,s}}(H)(e))))}bootstrap(n,r){if(!this._initStatus.done)throw new Error("Cannot bootstrap as there are still asynchronous initializers running. Bootstrap components in the `ngDoBootstrap` method of the root module.");let i;i=n instanceof um?n:this._componentFactoryResolver.resolveComponentFactory(n),this.componentTypes.push(i.componentType);const s=function(e){return e.isBoundToModule}(i)?void 0:this._injector.get(es),a=i.create(ct.NULL,[],r||i.selector,s),c=a.location.nativeElement,h=a.injector.get(Ny,null),y=h&&a.injector.get(Oy);return h&&y&&y.registerApplication(c,h),a.onDestroy(()=>{this.detachView(a.hostView),Ud(this.components,a),y&&y.unregisterApplication(c)}),this._loadComponent(a),a}tick(){if(this._runningTick)throw new Error("ApplicationRef.tick is called recursively");try{this._runningTick=!0;for(let n of this._views)n.detectChanges()}catch(n){this._zone.runOutsideAngular(()=>this._exceptionHandler.handleError(n))}finally{this._runningTick=!1}}attachView(n){const r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){const r=n;Ud(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(Sy,[]).concat(this._bootstrapListeners).forEach(i=>i(n))}ngOnDestroy(){this._views.slice().forEach(n=>n.destroy()),this._onMicrotaskEmptySubscription.unsubscribe()}get viewCount(){return this._views.length}}return e.\u0275fac=function(n){return new(n||e)(bt(bn),bt(ct),bt(Us),bt(co),bt(_o))},e.\u0275prov=ut({token:e,factory:e.\u0275fac}),e})();function Ud(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class XI{}class tT{}const nT={factoryPathPrefix:"",factoryPathSuffix:".ngfactory"};let rT=(()=>{class e{constructor(n,r){this._compiler=n,this._config=r||nT}load(n){return this.loadAndCompile(n)}loadAndCompile(n){let[r,i]=n.split("#");return void 0===i&&(i="default"),F(255)(r).then(s=>s[i]).then(s=>zy(s,r,i)).then(s=>this._compiler.compileModuleAsync(s))}loadFactory(n){let[r,i]=n.split("#"),s="NgFactory";return void 0===i&&(i="default",s=""),F(255)(this._config.factoryPathPrefix+r+this._config.factoryPathSuffix).then(o=>o[i+s]).then(o=>zy(o,r,i))}}return e.\u0275fac=function(n){return new(n||e)(bt(Na),bt(tT,8))},e.\u0275prov=ut({token:e,factory:e.\u0275fac}),e})();function zy(e,t,n){if(!e)throw new Error(`Cannot find '${n}' in '${t}'`);return e}const dT=function(e){return null},hT=jy(null,"core",[{provide:Ty,useValue:"unknown"},{provide:Uy,deps:[ct]},{provide:Oy,deps:[]},{provide:My,deps:[]}]),DT=[{provide:Oa,useClass:Oa,deps:[bn,ct,Us,co,_o]},{provide:g0,deps:[bn],useFactory:function(e){let t=[];return e.onStable.subscribe(()=>{for(;t.length;)t.pop()()}),function(n){t.push(n)}}},{provide:_o,useClass:_o,deps:[[new Qr,Od]]},{provide:Na,useClass:Na,deps:[]},bI,{provide:$u,useFactory:function(){return lw},deps:[]},{provide:Gu,useFactory:function(){return cw},deps:[]},{provide:tl,useFactory:function(e){return ed(e=e||"undefined"!=typeof $localize&&$localize.locale||Lu),e},deps:[[new ks(tl),new Qr,new Ei]]},{provide:Ay,useValue:"USD"}];let CT=(()=>{class e{constructor(n){}}return e.\u0275fac=function(n){return new(n||e)(bt(Oa))},e.\u0275mod=Vr({type:e}),e.\u0275inj=ot({providers:DT}),e})()},75:(Ye,ie,F)=>{F.d(ie,{b2:()=>Wt,q6:()=>Fr});var I=F(583),b=F(639);class ae extends I.w_{constructor(){super(...arguments),this.supportsDOMEvents=!0}}class me extends ae{static makeCurrent(){(0,I.HT)(new me)}onAndCancel(R,w,N){return R.addEventListener(w,N,!1),()=>{R.removeEventListener(w,N,!1)}}dispatchEvent(R,w){R.dispatchEvent(w)}remove(R){R.parentNode&&R.parentNode.removeChild(R)}createElement(R,w){return(w=w||this.getDefaultDocument()).createElement(R)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(R){return R.nodeType===Node.ELEMENT_NODE}isShadowRoot(R){return R instanceof DocumentFragment}getGlobalEventTarget(R,w){return"window"===w?window:"document"===w?R:"body"===w?R.body:null}getBaseHref(R){const w=(De=De||document.querySelector("base"),De?De.getAttribute("href"):null);return null==w?null:function(P){oe=oe||document.createElement("a"),oe.setAttribute("href",P);const R=oe.pathname;return"/"===R.charAt(0)?R:`/${R}`}(w)}resetBaseElement(){De=null}getUserAgent(){return window.navigator.userAgent}getCookie(R){return(0,I.Mx)(document.cookie,R)}}let oe,De=null;const q=new b.OlP("TRANSITION_ID"),V=[{provide:b.ip1,useFactory:function(P,R,w){return()=>{w.get(b.CZH).donePromise.then(()=>{const N=(0,I.q)(),B=R.querySelectorAll(`style[ng-transition="${P}"]`);for(let le=0;le<B.length;le++)N.remove(B[le])})}},deps:[q,I.K0,b.zs3],multi:!0}];class H{static init(){(0,b.VLi)(new H)}addToWindow(R){b.dqk.getAngularTestability=(N,B=!0)=>{const le=R.findTestabilityInTree(N,B);if(null==le)throw new Error("Could not find testability for element.");return le},b.dqk.getAllAngularTestabilities=()=>R.getAllTestabilities(),b.dqk.getAllAngularRootElements=()=>R.getAllRootElements(),b.dqk.frameworkStabilizers||(b.dqk.frameworkStabilizers=[]),b.dqk.frameworkStabilizers.push(N=>{const B=b.dqk.getAllAngularTestabilities();let le=B.length,Ie=!1;const re=function(pt){Ie=Ie||pt,le--,0==le&&N(Ie)};B.forEach(function(pt){pt.whenStable(re)})})}findTestabilityInTree(R,w,N){if(null==w)return null;const B=R.getTestability(w);return null!=B?B:N?(0,I.q)().isShadowRoot(w)?this.findTestabilityInTree(R,w.host,!0):this.findTestabilityInTree(R,w.parentElement,!0):null}}let Q=(()=>{class P{build(){return new XMLHttpRequest}}return P.\u0275fac=function(w){return new(w||P)},P.\u0275prov=b.Yz7({token:P,factory:P.\u0275fac}),P})();const Ze=new b.OlP("EventManagerPlugins");let Se=(()=>{class P{constructor(w,N){this._zone=N,this._eventNameToPlugin=new Map,w.forEach(B=>B.manager=this),this._plugins=w.slice().reverse()}addEventListener(w,N,B){return this._findPluginFor(N).addEventListener(w,N,B)}addGlobalEventListener(w,N,B){return this._findPluginFor(N).addGlobalEventListener(w,N,B)}getZone(){return this._zone}_findPluginFor(w){const N=this._eventNameToPlugin.get(w);if(N)return N;const B=this._plugins;for(let le=0;le<B.length;le++){const Ie=B[le];if(Ie.supports(w))return this._eventNameToPlugin.set(w,Ie),Ie}throw new Error(`No event manager plugin found for event ${w}`)}}return P.\u0275fac=function(w){return new(w||P)(b.LFG(Ze),b.LFG(b.R0b))},P.\u0275prov=b.Yz7({token:P,factory:P.\u0275fac}),P})();class $e{constructor(R){this._doc=R}addGlobalEventListener(R,w,N){const B=(0,I.q)().getGlobalEventTarget(this._doc,R);if(!B)throw new Error(`Unsupported event target ${B} for event ${w}`);return this.addEventListener(B,w,N)}}let J=(()=>{class P{constructor(){this._stylesSet=new Set}addStyles(w){const N=new Set;w.forEach(B=>{this._stylesSet.has(B)||(this._stylesSet.add(B),N.add(B))}),this.onStylesAdded(N)}onStylesAdded(w){}getAllStyles(){return Array.from(this._stylesSet)}}return P.\u0275fac=function(w){return new(w||P)},P.\u0275prov=b.Yz7({token:P,factory:P.\u0275fac}),P})(),un=(()=>{class P extends J{constructor(w){super(),this._doc=w,this._hostNodes=new Map,this._hostNodes.set(w.head,[])}_addStylesToHost(w,N,B){w.forEach(le=>{const Ie=this._doc.createElement("style");Ie.textContent=le,B.push(N.appendChild(Ie))})}addHost(w){const N=[];this._addStylesToHost(this._stylesSet,w,N),this._hostNodes.set(w,N)}removeHost(w){const N=this._hostNodes.get(w);N&&N.forEach(Jt),this._hostNodes.delete(w)}onStylesAdded(w){this._hostNodes.forEach((N,B)=>{this._addStylesToHost(w,B,N)})}ngOnDestroy(){this._hostNodes.forEach(w=>w.forEach(Jt))}}return P.\u0275fac=function(w){return new(w||P)(b.LFG(I.K0))},P.\u0275prov=b.Yz7({token:P,factory:P.\u0275fac}),P})();function Jt(P){(0,I.q)().remove(P)}const ni={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"},jt=/%COMP%/g;function ln(P,R,w){for(let N=0;N<R.length;N++){let B=R[N];Array.isArray(B)?ln(P,B,w):(B=B.replace(jt,P),w.push(B))}return w}function xt(P){return R=>{if("__ngUnwrap__"===R)return P;!1===P(R)&&(R.preventDefault(),R.returnValue=!1)}}let Le=(()=>{class P{constructor(w,N,B){this.eventManager=w,this.sharedStylesHost=N,this.appId=B,this.rendererByCompId=new Map,this.defaultRenderer=new Si(w)}createRenderer(w,N){if(!w||!N)return this.defaultRenderer;switch(N.encapsulation){case b.ifc.Emulated:{let B=this.rendererByCompId.get(N.id);return B||(B=new Mi(this.eventManager,this.sharedStylesHost,N,this.appId),this.rendererByCompId.set(N.id,B)),B.applyToHost(w),B}case 1:case b.ifc.ShadowDom:return new ot(this.eventManager,this.sharedStylesHost,w,N);default:if(!this.rendererByCompId.has(N.id)){const B=ln(N.id,N.styles,[]);this.sharedStylesHost.addStyles(B),this.rendererByCompId.set(N.id,this.defaultRenderer)}return this.defaultRenderer}}begin(){}end(){}}return P.\u0275fac=function(w){return new(w||P)(b.LFG(Se),b.LFG(un),b.LFG(b.AFp))},P.\u0275prov=b.Yz7({token:P,factory:P.\u0275fac}),P})();class Si{constructor(R){this.eventManager=R,this.data=Object.create(null)}destroy(){}createElement(R,w){return w?document.createElementNS(ni[w]||w,R):document.createElement(R)}createComment(R){return document.createComment(R)}createText(R){return document.createTextNode(R)}appendChild(R,w){R.appendChild(w)}insertBefore(R,w,N){R&&R.insertBefore(w,N)}removeChild(R,w){R&&R.removeChild(w)}selectRootElement(R,w){let N="string"==typeof R?document.querySelector(R):R;if(!N)throw new Error(`The selector "${R}" did not match any elements`);return w||(N.textContent=""),N}parentNode(R){return R.parentNode}nextSibling(R){return R.nextSibling}setAttribute(R,w,N,B){if(B){w=B+":"+w;const le=ni[B];le?R.setAttributeNS(le,w,N):R.setAttribute(w,N)}else R.setAttribute(w,N)}removeAttribute(R,w,N){if(N){const B=ni[N];B?R.removeAttributeNS(B,w):R.removeAttribute(`${N}:${w}`)}else R.removeAttribute(w)}addClass(R,w){R.classList.add(w)}removeClass(R,w){R.classList.remove(w)}setStyle(R,w,N,B){B&(b.JOm.DashCase|b.JOm.Important)?R.style.setProperty(w,N,B&b.JOm.Important?"important":""):R.style[w]=N}removeStyle(R,w,N){N&b.JOm.DashCase?R.style.removeProperty(w):R.style[w]=""}setProperty(R,w,N){R[w]=N}setValue(R,w){R.nodeValue=w}listen(R,w,N){return"string"==typeof R?this.eventManager.addGlobalEventListener(R,w,xt(N)):this.eventManager.addEventListener(R,w,xt(N))}}class Mi extends Si{constructor(R,w,N,B){super(R),this.component=N;const le=ln(B+"-"+N.id,N.styles,[]);w.addStyles(le),this.contentAttr="_ngcontent-%COMP%".replace(jt,B+"-"+N.id),this.hostAttr="_nghost-%COMP%".replace(jt,B+"-"+N.id)}applyToHost(R){super.setAttribute(R,this.hostAttr,"")}createElement(R,w){const N=super.createElement(R,w);return super.setAttribute(N,this.contentAttr,""),N}}class ot extends Si{constructor(R,w,N,B){super(R),this.sharedStylesHost=w,this.hostEl=N,this.shadowRoot=N.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);const le=ln(B.id,B.styles,[]);for(let Ie=0;Ie<le.length;Ie++){const re=document.createElement("style");re.textContent=le[Ie],this.shadowRoot.appendChild(re)}}nodeOrShadowRoot(R){return R===this.hostEl?this.shadowRoot:R}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}appendChild(R,w){return super.appendChild(this.nodeOrShadowRoot(R),w)}insertBefore(R,w,N){return super.insertBefore(this.nodeOrShadowRoot(R),w,N)}removeChild(R,w){return super.removeChild(this.nodeOrShadowRoot(R),w)}parentNode(R){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(R)))}}let wt=(()=>{class P extends $e{constructor(w){super(w)}supports(w){return!0}addEventListener(w,N,B){return w.addEventListener(N,B,!1),()=>this.removeEventListener(w,N,B)}removeEventListener(w,N,B){return w.removeEventListener(N,B)}}return P.\u0275fac=function(w){return new(w||P)(b.LFG(I.K0))},P.\u0275prov=b.Yz7({token:P,factory:P.\u0275fac}),P})();const tt=["alt","control","meta","shift"],ye={"\b":"Backspace","\t":"Tab","\x7f":"Delete","\x1b":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},cn={A:"1",B:"2",C:"3",D:"4",E:"5",F:"6",G:"7",H:"8",I:"9",J:"*",K:"+",M:"-",N:".",O:"/","`":"0","\x90":"NumLock"},as={alt:P=>P.altKey,control:P=>P.ctrlKey,meta:P=>P.metaKey,shift:P=>P.shiftKey};let Pe=(()=>{class P extends $e{constructor(w){super(w)}supports(w){return null!=P.parseEventName(w)}addEventListener(w,N,B){const le=P.parseEventName(N),Ie=P.eventCallback(le.fullKey,B,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>(0,I.q)().onAndCancel(w,le.domEventName,Ie))}static parseEventName(w){const N=w.toLowerCase().split("."),B=N.shift();if(0===N.length||"keydown"!==B&&"keyup"!==B)return null;const le=P._normalizeKey(N.pop());let Ie="";if(tt.forEach(pt=>{const Bt=N.indexOf(pt);Bt>-1&&(N.splice(Bt,1),Ie+=pt+".")}),Ie+=le,0!=N.length||0===le.length)return null;const re={};return re.domEventName=B,re.fullKey=Ie,re}static getEventFullKey(w){let N="",B=function(P){let R=P.key;if(null==R){if(R=P.keyIdentifier,null==R)return"Unidentified";R.startsWith("U+")&&(R=String.fromCharCode(parseInt(R.substring(2),16)),3===P.location&&cn.hasOwnProperty(R)&&(R=cn[R]))}return ye[R]||R}(w);return B=B.toLowerCase()," "===B?B="space":"."===B&&(B="dot"),tt.forEach(le=>{le!=B&&as[le](w)&&(N+=le+".")}),N+=B,N}static eventCallback(w,N,B){return le=>{P.getEventFullKey(le)===w&&B.runGuarded(()=>N(le))}}static _normalizeKey(w){switch(w){case"esc":return"escape";default:return w}}}return P.\u0275fac=function(w){return new(w||P)(b.LFG(I.K0))},P.\u0275prov=b.Yz7({token:P,factory:P.\u0275fac}),P})();const Fr=(0,b.eFA)(b._c5,"browser",[{provide:b.Lbi,useValue:I.bD},{provide:b.g9A,useValue:function(){me.makeCurrent(),H.init()},multi:!0},{provide:I.K0,useFactory:function(){return(0,b.RDi)(document),document},deps:[]}]),Pi=[[],{provide:b.zSh,useValue:"root"},{provide:b.qLn,useFactory:function(){return new b.qLn},deps:[]},{provide:Ze,useClass:wt,multi:!0,deps:[I.K0,b.R0b,b.Lbi]},{provide:Ze,useClass:Pe,multi:!0,deps:[I.K0]},[],{provide:Le,useClass:Le,deps:[Se,un,b.AFp]},{provide:b.FYo,useExisting:Le},{provide:J,useExisting:un},{provide:un,useClass:un,deps:[I.K0]},{provide:b.dDg,useClass:b.dDg,deps:[b.R0b]},{provide:Se,useClass:Se,deps:[Ze,b.R0b]},{provide:I.JF,useClass:Q,deps:[]},[]];let Wt=(()=>{class P{constructor(w){if(w)throw new Error("BrowserModule has already been loaded. If you need access to common directives such as NgIf and NgFor from a lazy loaded module, import CommonModule instead.")}static withServerTransition(w){return{ngModule:P,providers:[{provide:b.AFp,useValue:w.appId},{provide:q,useExisting:b.AFp},V]}}}return P.\u0275fac=function(w){return new(w||P)(b.LFG(P,12))},P.\u0275mod=b.oAB({type:P}),P.\u0275inj=b.cJS({providers:Pi,imports:[I.ez,b.hGG]}),P})();"undefined"!=typeof window&&window},353:(Ye,ie,F)=>{F.d(ie,{Bz:()=>Ol,lC:()=>Gi});var I=F(583),b=F(639),ae=F(869),me=F(693),De=F(87);function W(...d){let l=d[d.length-1];return(0,ae.K)(l)?(d.pop(),(0,De.r)(d,l)):(0,me.n)(d)}var oe=F(709),fe=F(971);class q extends oe.xQ{constructor(l){super(),this._value=l}get value(){return this.getValue()}_subscribe(l){const u=super._subscribe(l);return u&&!u.closed&&l.next(this._value),u}getValue(){if(this.hasError)throw this.thrownError;if(this.closed)throw new fe.N;return this._value}next(l){super.next(this._value=l)}}var L=F(796),V=F(393);class H extends V.L{notifyNext(l,u,f,m,_){this.destination.next(u)}notifyError(l,u){this.destination.error(l)}notifyComplete(l){this.destination.complete()}}class Q extends V.L{constructor(l,u,f){super(),this.parent=l,this.outerValue=u,this.outerIndex=f,this.index=0}_next(l){this.parent.notifyNext(this.outerValue,l,this.outerIndex,this.index++,this)}_error(l){this.parent.notifyError(l,this),this.unsubscribe()}_complete(){this.parent.notifyComplete(this),this.unsubscribe()}}var Z=F(444),X=F(574);function G(d,l,u,f,m=new Q(d,u,f)){if(!m.closed)return l instanceof X.y?l.subscribe(m):(0,Z.s)(l)(m)}const Te={};class we{constructor(l){this.resultSelector=l}call(l,u){return u.subscribe(new te(l,this.resultSelector))}}class te extends H{constructor(l,u){super(l),this.resultSelector=u,this.active=0,this.values=[],this.observables=[]}_next(l){this.values.push(Te),this.observables.push(l)}_complete(){const l=this.observables,u=l.length;if(0===u)this.destination.complete();else{this.active=u,this.toRespond=u;for(let f=0;f<u;f++)this.add(G(this,l[f],void 0,f))}}notifyComplete(l){0==(this.active-=1)&&this.destination.complete()}notifyNext(l,u,f){const m=this.values,T=this.toRespond?m[f]===Te?--this.toRespond:this.toRespond:0;m[f]=u,0===T&&(this.resultSelector?this._tryResultSelector(m):this.destination.next(m.slice()))}_tryResultSelector(l){let u;try{u=this.resultSelector.apply(this,l)}catch(f){return void this.destination.error(f)}this.destination.next(u)}}const gn=(()=>{function d(){return Error.call(this),this.message="no elements in sequence",this.name="EmptyError",this}return d.prototype=Object.create(Error.prototype),d})();var ft=F(282);function wn(...d){return(0,ft.J)(1)(W(...d))}var se=F(402);const Fe=new X.y(d=>d.complete());function et(d){return d?function(d){return new X.y(l=>d.schedule(()=>l.complete()))}(d):Fe}function Se(d){return new X.y(l=>{let u;try{u=d()}catch(m){return void l.error(m)}return(u?(0,se.D)(u):et()).subscribe(l)})}var $e=F(441),J=F(2),un=F(345);function Jt(d,l){return"function"==typeof l?u=>u.pipe(Jt((f,m)=>(0,se.D)(d(f,m)).pipe((0,J.U)((_,T)=>l(f,_,m,T))))):u=>u.lift(new ni(d))}class ni{constructor(l){this.project=l}call(l,u){return u.subscribe(new jt(l,this.project))}}class jt extends un.Ds{constructor(l,u){super(l),this.project=u,this.index=0}_next(l){let u;const f=this.index++;try{u=this.project(l,f)}catch(m){return void this.destination.error(m)}this._innerSub(u)}_innerSub(l){const u=this.innerSubscription;u&&u.unsubscribe();const f=new un.IY(this),m=this.destination;m.add(f),this.innerSubscription=(0,un.ft)(l,f),this.innerSubscription!==f&&m.add(this.innerSubscription)}_complete(){const{innerSubscription:l}=this;(!l||l.closed)&&super._complete(),this.unsubscribe()}_unsubscribe(){this.innerSubscription=void 0}notifyComplete(){this.innerSubscription=void 0,this.isStopped&&super._complete()}notifyNext(l){this.destination.next(l)}}const ri=(()=>{function d(){return Error.call(this),this.message="argument out of range",this.name="ArgumentOutOfRangeError",this}return d.prototype=Object.create(Error.prototype),d})();function Ti(d){return l=>0===d?et():l.lift(new Io(d))}class Io{constructor(l){if(this.total=l,this.total<0)throw new ri}call(l,u){return u.subscribe(new ir(l,this.total))}}class ir extends V.L{constructor(l,u){super(l),this.total=u,this.count=0}_next(l){const u=this.total,f=++this.count;f<=u&&(this.destination.next(l),f===u&&(this.destination.complete(),this.unsubscribe()))}}function ln(d,l){let u=!1;return arguments.length>=2&&(u=!0),function(m){return m.lift(new xt(d,l,u))}}class xt{constructor(l,u,f=!1){this.accumulator=l,this.seed=u,this.hasSeed=f}call(l,u){return u.subscribe(new Et(l,this.accumulator,this.seed,this.hasSeed))}}class Et extends V.L{constructor(l,u,f,m){super(l),this.accumulator=u,this._seed=f,this.hasSeed=m,this.index=0}get seed(){return this._seed}set seed(l){this.hasSeed=!0,this._seed=l}_next(l){if(this.hasSeed)return this._tryNext(l);this.seed=l,this.destination.next(l)}_tryNext(l){const u=this.index++;let f;try{f=this.accumulator(this.seed,l,u)}catch(m){this.destination.error(m)}this.seed=f,this.destination.next(f)}}function Le(d,l){return function(f){return f.lift(new Si(d,l))}}class Si{constructor(l,u){this.predicate=l,this.thisArg=u}call(l,u){return u.subscribe(new Ba(l,this.predicate,this.thisArg))}}class Ba extends V.L{constructor(l,u,f){super(l),this.predicate=u,this.thisArg=f,this.count=0}_next(l){let u;try{u=this.predicate.call(this.thisArg,l,this.count++)}catch(f){return void this.destination.error(f)}u&&this.destination.next(l)}}function In(d){return function(u){const f=new ut(d),m=u.lift(f);return f.caught=m}}class ut{constructor(l){this.selector=l}call(l,u){return u.subscribe(new Mi(l,this.selector,this.caught))}}class Mi extends un.Ds{constructor(l,u,f){super(l),this.selector=u,this.caught=f}error(l){if(!this.isStopped){let u;try{u=this.selector(l,this.caught)}catch(_){return void super.error(_)}this._unsubscribeAndRecycle();const f=new un.IY(this);this.add(f);const m=(0,un.ft)(u,f);m!==f&&this.add(m)}}}var ot=F(773);function wt(d,l){return(0,ot.zg)(d,l,1)}function xr(d){return function(u){return 0===d?et():u.lift(new vt(d))}}class vt{constructor(l){if(this.total=l,this.total<0)throw new ri}call(l,u){return u.subscribe(new sr(l,this.total))}}class sr extends V.L{constructor(l,u){super(l),this.total=u,this.ring=new Array,this.count=0}_next(l){const u=this.ring,f=this.total,m=this.count++;u.length<f?u.push(l):u[m%f]=l}_complete(){const l=this.destination;let u=this.count;if(u>0){const f=this.count>=this.total?this.total:this.count,m=this.ring;for(let _=0;_<f;_++){const T=u++%f;l.next(m[T])}}l.complete()}}function Pr(d=Ai){return l=>l.lift(new or(d))}class or{constructor(l){this.errorFactory=l}call(l,u){return u.subscribe(new Nr(l,this.errorFactory))}}class Nr extends V.L{constructor(l,u){super(l),this.errorFactory=u,this.hasValue=!1}_next(l){this.hasValue=!0,this.destination.next(l)}_complete(){if(this.hasValue)return this.destination.complete();{let l;try{l=this.errorFactory()}catch(u){l=u}this.destination.error(l)}}}function Ai(){return new gn}function Tn(d=null){return l=>l.lift(new he(d))}class he{constructor(l){this.defaultValue=l}call(l,u){return u.subscribe(new tt(l,this.defaultValue))}}class tt extends V.L{constructor(l,u){super(l),this.defaultValue=u,this.isEmpty=!0}_next(l){this.isEmpty=!1,this.destination.next(l)}_complete(){this.isEmpty&&this.destination.next(this.defaultValue),this.destination.complete()}}var pe=F(487);function cn(d,l){const u=arguments.length>=2;return f=>f.pipe(d?Le((m,_)=>d(m,_,f)):pe.y,Ti(1),u?Tn(l):Pr(()=>new gn))}function Xt(){}var Dt=F(105);function Qe(d,l,u){return function(m){return m.lift(new en(d,l,u))}}class en{constructor(l,u,f){this.nextOrObserver=l,this.error=u,this.complete=f}call(l,u){return u.subscribe(new as(l,this.nextOrObserver,this.error,this.complete))}}class as extends V.L{constructor(l,u,f,m){super(l),this._tapNext=Xt,this._tapError=Xt,this._tapComplete=Xt,this._tapError=f||Xt,this._tapComplete=m||Xt,(0,Dt.m)(u)?(this._context=this,this._tapNext=u):u&&(this._context=u,this._tapNext=u.next||Xt,this._tapError=u.error||Xt,this._tapComplete=u.complete||Xt)}_next(l){try{this._tapNext.call(this._context,l)}catch(u){return void this.destination.error(u)}this.destination.next(l)}_error(l){try{this._tapError.call(this._context,l)}catch(u){return void this.destination.error(u)}this.destination.error(l)}_complete(){try{this._tapComplete.call(this._context)}catch(l){return void this.destination.error(l)}return this.destination.complete()}}var Pe=F(319);class Ge{constructor(l){this.callback=l}call(l,u){return u.subscribe(new xi(l,this.callback))}}class xi extends V.L{constructor(l,u){super(l),this.add(new Pe.w(u))}}var Bn=F(307);class je{constructor(l,u){this.id=l,this.url=u}}class Un extends je{constructor(l,u,f="imperative",m=null){super(l,u),this.navigationTrigger=f,this.restoredState=m}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}}class ar extends je{constructor(l,u,f){super(l,u),this.urlAfterRedirects=f}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}}class dn extends je{constructor(l,u,f){super(l,u),this.reason=f}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}}class Be extends je{constructor(l,u,f){super(l,u),this.error=f}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}}class Sn extends je{constructor(l,u,f,m){super(l,u),this.urlAfterRedirects=f,this.state=m}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class ur extends je{constructor(l,u,f,m){super(l,u),this.urlAfterRedirects=f,this.state=m}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class Or extends je{constructor(l,u,f,m,_){super(l,u),this.urlAfterRedirects=f,this.state=m,this.shouldActivate=_}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}}class Fr extends je{constructor(l,u,f,m){super(l,u),this.urlAfterRedirects=f,this.state=m}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class Pi extends je{constructor(l,u,f,m){super(l,u),this.urlAfterRedirects=f,this.state=m}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class Wt{constructor(l){this.route=l}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}}class Hn{constructor(l){this.route=l}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}}class Ni{constructor(l){this.snapshot=l}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class Lr{constructor(l){this.snapshot=l}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class us{constructor(l){this.snapshot=l}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class kr{constructor(l){this.snapshot=l}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class It{constructor(l,u,f){this.routerEvent=l,this.position=u,this.anchor=f}toString(){return`Scroll(anchor: '${this.anchor}', position: '${this.position?`${this.position[0]}, ${this.position[1]}`:null}')`}}const Ee="primary";class Vr{constructor(l){this.params=l||{}}has(l){return Object.prototype.hasOwnProperty.call(this.params,l)}get(l){if(this.has(l)){const u=this.params[l];return Array.isArray(u)?u[0]:u}return null}getAll(l){if(this.has(l)){const u=this.params[l];return Array.isArray(u)?u:[u]}return[]}get keys(){return Object.keys(this.params)}}function tn(d){return new Vr(d)}const Mn="ngNavigationCancelingError";function lr(d){const l=Error("NavigationCancelingError: "+d);return l[Mn]=!0,l}function ht(d,l,u){const f=u.path.split("/");if(f.length>d.length||"full"===u.pathMatch&&(l.hasChildren()||f.length<d.length))return null;const m={};for(let _=0;_<f.length;_++){const T=f[_],A=d[_];if(T.startsWith(":"))m[T.substring(1)]=A;else if(T!==A.path)return null}return{consumed:d.slice(0,f.length),posParams:m}}function _t(d,l){const u=d?Object.keys(d):void 0,f=l?Object.keys(l):void 0;if(!u||!f||u.length!=f.length)return!1;let m;for(let _=0;_<u.length;_++)if(m=u[_],!Tt(d[m],l[m]))return!1;return!0}function Tt(d,l){if(Array.isArray(d)&&Array.isArray(l)){if(d.length!==l.length)return!1;const u=[...d].sort(),f=[...l].sort();return u.every((m,_)=>f[_]===m)}return d===l}function jr(d){return Array.prototype.concat.apply([],d)}function Pt(d){return d.length>0?d[d.length-1]:null}function ue(d,l){for(const u in d)d.hasOwnProperty(u)&&l(d[u],u)}function Ne(d){return(0,b.CqO)(d)?d:(0,b.QGY)(d)?(0,se.D)(Promise.resolve(d)):W(d)}const R={exact:function le(d,l,u){if(!Nt(d.segments,l.segments)||!Bt(d.segments,l.segments,u)||d.numberOfChildren!==l.numberOfChildren)return!1;for(const f in l.children)if(!d.children[f]||!le(d.children[f],l.children[f],u))return!1;return!0},subset:re},w={exact:function(d,l){return _t(d,l)},subset:function(d,l){return Object.keys(l).length<=Object.keys(d).length&&Object.keys(l).every(u=>Tt(d[u],l[u]))},ignored:()=>!0};function N(d,l,u){return R[u.paths](d.root,l.root,u.matrixParams)&&w[u.queryParams](d.queryParams,l.queryParams)&&!("exact"===u.fragment&&d.fragment!==l.fragment)}function re(d,l,u){return pt(d,l,l.segments,u)}function pt(d,l,u,f){if(d.segments.length>u.length){const m=d.segments.slice(0,u.length);return!(!Nt(m,u)||l.hasChildren()||!Bt(m,u,f))}if(d.segments.length===u.length){if(!Nt(d.segments,u)||!Bt(d.segments,u,f))return!1;for(const m in l.children)if(!d.children[m]||!re(d.children[m],l.children[m],f))return!1;return!0}{const m=u.slice(0,d.segments.length),_=u.slice(d.segments.length);return!!(Nt(d.segments,m)&&Bt(d.segments,m,f)&&d.children[Ee])&&pt(d.children[Ee],l,_,f)}}function Bt(d,l,u){return l.every((f,m)=>w[u](d[m].parameters,f.parameters))}class mn{constructor(l,u,f){this.root=l,this.queryParams=u,this.fragment=f}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=tn(this.queryParams)),this._queryParamMap}toString(){return Ua.serialize(this)}}class Ae{constructor(l,u){this.segments=l,this.children=u,this.parent=null,ue(u,(f,m)=>f.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return An(this)}}class gt{constructor(l,u){this.path=l,this.parameters=u}get parameterMap(){return this._parameterMap||(this._parameterMap=tn(this.parameters)),this._parameterMap}toString(){return zt(this)}}function Nt(d,l){return d.length===l.length&&d.every((u,f)=>u.path===l[f].path)}class ze{}class Fi{parse(l){const u=new $a(l);return new mn(u.parseRootSegment(),u.parseQueryParams(),u.parseFragment())}serialize(l){var d;return`${`/${$n(l.root,!0)}`}${function(d){const l=Object.keys(d).map(u=>{const f=d[u];return Array.isArray(f)?f.map(m=>`${Gn(u)}=${Gn(m)}`).join("&"):`${Gn(u)}=${Gn(f)}`}).filter(u=>!!u);return l.length?`?${l.join("&")}`:""}(l.queryParams)}${"string"==typeof l.fragment?`#${d=l.fragment,encodeURI(d)}`:""}`}}const Ua=new Fi;function An(d){return d.segments.map(l=>zt(l)).join("/")}function $n(d,l){if(!d.hasChildren())return An(d);if(l){const u=d.children[Ee]?$n(d.children[Ee],!1):"",f=[];return ue(d.children,(m,_)=>{_!==Ee&&f.push(`${_}:${$n(m,!1)}`)}),f.length>0?`${u}(${f.join("//")})`:u}{const u=function(d,l){let u=[];return ue(d.children,(f,m)=>{m===Ee&&(u=u.concat(l(f,m)))}),ue(d.children,(f,m)=>{m!==Ee&&(u=u.concat(l(f,m)))}),u}(d,(f,m)=>m===Ee?[$n(d.children[Ee],!1)]:[`${m}:${$n(f,!1)}`]);return 1===Object.keys(d.children).length&&null!=d.children[Ee]?`${An(d)}/${u[0]}`:`${An(d)}/(${u.join("//")})`}}function dr(d){return encodeURIComponent(d).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Gn(d){return dr(d).replace(/%3B/gi,";")}function Ke(d){return dr(d).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function ii(d){return decodeURIComponent(d)}function nn(d){return ii(d.replace(/\+/g,"%20"))}function zt(d){return`${Ke(d.path)}${function(d){return Object.keys(d).map(l=>`;${Ke(l)}=${Ke(d[l])}`).join("")}(d.parameters)}`}const oi=/^[^\/()?;=#]+/;function Ft(d){const l=d.match(oi);return l?l[0]:""}const To=/^[^=?&#]+/,So=/^[^?&#]+/;class $a{constructor(l){this.url=l,this.remaining=l}parseRootSegment(){return this.consumeOptional("/"),""===this.remaining||this.peekStartsWith("?")||this.peekStartsWith("#")?new Ae([],{}):new Ae([],this.parseChildren())}parseQueryParams(){const l={};if(this.consumeOptional("?"))do{this.parseQueryParam(l)}while(this.consumeOptional("&"));return l}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(""===this.remaining)return{};this.consumeOptional("/");const l=[];for(this.peekStartsWith("(")||l.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),l.push(this.parseSegment());let u={};this.peekStartsWith("/(")&&(this.capture("/"),u=this.parseParens(!0));let f={};return this.peekStartsWith("(")&&(f=this.parseParens(!1)),(l.length>0||Object.keys(u).length>0)&&(f[Ee]=new Ae(l,u)),f}parseSegment(){const l=Ft(this.remaining);if(""===l&&this.peekStartsWith(";"))throw new Error(`Empty path url segment cannot have parameters: '${this.remaining}'.`);return this.capture(l),new gt(ii(l),this.parseMatrixParams())}parseMatrixParams(){const l={};for(;this.consumeOptional(";");)this.parseParam(l);return l}parseParam(l){const u=Ft(this.remaining);if(!u)return;this.capture(u);let f="";if(this.consumeOptional("=")){const m=Ft(this.remaining);m&&(f=m,this.capture(f))}l[ii(u)]=ii(f)}parseQueryParam(l){const u=function(d){const l=d.match(To);return l?l[0]:""}(this.remaining);if(!u)return;this.capture(u);let f="";if(this.consumeOptional("=")){const T=function(d){const l=d.match(So);return l?l[0]:""}(this.remaining);T&&(f=T,this.capture(f))}const m=nn(u),_=nn(f);if(l.hasOwnProperty(m)){let T=l[m];Array.isArray(T)||(T=[T],l[m]=T),T.push(_)}else l[m]=_}parseParens(l){const u={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){const f=Ft(this.remaining),m=this.remaining[f.length];if("/"!==m&&")"!==m&&";"!==m)throw new Error(`Cannot parse url '${this.url}'`);let _;f.indexOf(":")>-1?(_=f.substr(0,f.indexOf(":")),this.capture(_),this.capture(":")):l&&(_=Ee);const T=this.parseChildren();u[_]=1===Object.keys(T).length?T[Ee]:new Ae([],T),this.consumeOptional("//")}return u}peekStartsWith(l){return this.remaining.startsWith(l)}consumeOptional(l){return!!this.peekStartsWith(l)&&(this.remaining=this.remaining.substring(l.length),!0)}capture(l){if(!this.consumeOptional(l))throw new Error(`Expected "${l}".`)}}class Ao{constructor(l){this._root=l}get root(){return this._root.value}parent(l){const u=this.pathFromRoot(l);return u.length>1?u[u.length-2]:null}children(l){const u=ls(l,this._root);return u?u.children.map(f=>f.value):[]}firstChild(l){const u=ls(l,this._root);return u&&u.children.length>0?u.children[0].value:null}siblings(l){const u=cs(l,this._root);return u.length<2?[]:u[u.length-2].children.map(m=>m.value).filter(m=>m!==l)}pathFromRoot(l){return cs(l,this._root).map(u=>u.value)}}function ls(d,l){if(d===l.value)return l;for(const u of l.children){const f=ls(d,u);if(f)return f}return null}function cs(d,l){if(d===l.value)return[l];for(const u of l.children){const f=cs(d,u);if(f.length)return f.unshift(l),f}return[]}class Rn{constructor(l,u){this.value=l,this.children=u}toString(){return`TreeNode(${this.value})`}}function Br(d){const l={};return d&&d.children.forEach(u=>l[u.value.outlet]=u),l}class Ro extends Ao{constructor(l,u){super(l),this.snapshot=u,Ur(this,l)}toString(){return this.snapshot.toString()}}function xo(d,l){const u=function(d,l){const T=new ds([],{},{},"",{},Ee,l,null,d.root,-1,{});return new Po("",new Rn(T,[]))}(d,l),f=new q([new gt("",{})]),m=new q({}),_=new q({}),T=new q({}),A=new q(""),U=new Wn(f,m,T,A,_,Ee,l,u.root);return U.snapshot=u.root,new Ro(new Rn(U,[]),u)}class Wn{constructor(l,u,f,m,_,T,A,U){this.url=l,this.params=u,this.queryParams=f,this.fragment=m,this.data=_,this.outlet=T,this.component=A,this._futureSnapshot=U}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=this.params.pipe((0,J.U)(l=>tn(l)))),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=this.queryParams.pipe((0,J.U)(l=>tn(l)))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}}function ki(d,l="emptyOnly"){const u=d.pathFromRoot;let f=0;if("always"!==l)for(f=u.length-1;f>=1;){const m=u[f],_=u[f-1];if(m.routeConfig&&""===m.routeConfig.path)f--;else{if(_.component)break;f--}}return function(d){return d.reduce((l,u)=>({params:Object.assign(Object.assign({},l.params),u.params),data:Object.assign(Object.assign({},l.data),u.data),resolve:Object.assign(Object.assign({},l.resolve),u._resolvedData)}),{params:{},data:{},resolve:{}})}(u.slice(f))}class ds{constructor(l,u,f,m,_,T,A,U,ne,We,_e){this.url=l,this.params=u,this.queryParams=f,this.fragment=m,this.data=_,this.outlet=T,this.component=A,this.routeConfig=U,this._urlSegment=ne,this._lastPathIndex=We,this._resolve=_e}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=tn(this.params)),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=tn(this.queryParams)),this._queryParamMap}toString(){return`Route(url:'${this.url.map(f=>f.toString()).join("/")}', path:'${this.routeConfig?this.routeConfig.path:""}')`}}class Po extends Ao{constructor(l,u){super(u),this.url=l,Ur(this,u)}toString(){return ai(this._root)}}function Ur(d,l){l.value._routerState=d,l.children.forEach(u=>Ur(d,u))}function ai(d){const l=d.children.length>0?` { ${d.children.map(ai).join(", ")} } `:"";return`${d.value}${l}`}function fs(d){if(d.snapshot){const l=d.snapshot,u=d._futureSnapshot;d.snapshot=u,_t(l.queryParams,u.queryParams)||d.queryParams.next(u.queryParams),l.fragment!==u.fragment&&d.fragment.next(u.fragment),_t(l.params,u.params)||d.params.next(u.params),function(d,l){if(d.length!==l.length)return!1;for(let u=0;u<d.length;++u)if(!_t(d[u],l[u]))return!1;return!0}(l.url,u.url)||d.url.next(u.url),_t(l.data,u.data)||d.data.next(u.data)}else d.snapshot=d._futureSnapshot,d.data.next(d._futureSnapshot.data)}function hs(d,l){return _t(d.params,l.params)&&function(d,l){return Nt(d,l)&&d.every((u,f)=>_t(u.parameters,l[f].parameters))}(d.url,l.url)&&!(!d.parent!=!l.parent)&&(!d.parent||hs(d.parent,l.parent))}function yn(d,l,u){if(u&&d.shouldReuseRoute(l.value,u.value.snapshot)){const f=u.value;f._futureSnapshot=l.value;const m=function(d,l,u){return l.children.map(f=>{for(const m of u.children)if(d.shouldReuseRoute(f.value,m.value.snapshot))return yn(d,f,m);return yn(d,f)})}(d,l,u);return new Rn(f,m)}{if(d.shouldAttach(l.value)){const _=d.retrieve(l.value);if(null!==_){const T=_.route;return ps(l,T),T}}const f=function(d){return new Wn(new q(d.url),new q(d.params),new q(d.queryParams),new q(d.fragment),new q(d.data),d.outlet,d.component,d)}(l.value),m=l.children.map(_=>yn(d,_));return new Rn(f,m)}}function ps(d,l){if(d.value.routeConfig!==l.value.routeConfig)throw new Error("Cannot reattach ActivatedRouteSnapshot created from a different route");if(d.children.length!==l.children.length)throw new Error("Cannot reattach ActivatedRouteSnapshot with a different number of children");l.value._futureSnapshot=d.value;for(let u=0;u<d.children.length;++u)ps(d.children[u],l.children[u])}function ui(d){return"object"==typeof d&&null!=d&&!d.outlets&&!d.segmentPath}function zn(d){return"object"==typeof d&&null!=d&&d.outlets}function li(d,l,u,f,m){let _={};return f&&ue(f,(T,A)=>{_[A]=Array.isArray(T)?T.map(U=>`${U}`):`${T}`}),new mn(u.root===d?l:Fo(u.root,d,l),_,m)}function Fo(d,l,u){const f={};return ue(d.children,(m,_)=>{f[_]=m===l?u:Fo(m,l,u)}),new Ae(d.segments,f)}class ci{constructor(l,u,f){if(this.isAbsolute=l,this.numberOfDoubleDots=u,this.commands=f,l&&f.length>0&&ui(f[0]))throw new Error("Root segment cannot have matrix parameters");const m=f.find(zn);if(m&&m!==Pt(f))throw new Error("{outlets:{}} has to be the last command")}toRoot(){return this.isAbsolute&&1===this.commands.length&&"/"==this.commands[0]}}class D{constructor(l,u,f){this.segmentGroup=l,this.processChildren=u,this.index=f}}function j(d,l,u){if(d||(d=new Ae([],{})),0===d.segments.length&&d.hasChildren())return Y(d,l,u);const f=function(d,l,u){let f=0,m=l;const _={match:!1,pathIndex:0,commandIndex:0};for(;m<d.segments.length;){if(f>=u.length)return _;const T=d.segments[m],A=u[f];if(zn(A))break;const U=`${A}`,ne=f<u.length-1?u[f+1]:null;if(m>0&&void 0===U)break;if(U&&ne&&"object"==typeof ne&&void 0===ne.outlets){if(!ke(U,ne,T))return _;f+=2}else{if(!ke(U,{},T))return _;f++}m++}return{match:!0,pathIndex:m,commandIndex:f}}(d,l,u),m=u.slice(f.commandIndex);if(f.match&&f.pathIndex<d.segments.length){const _=new Ae(d.segments.slice(0,f.pathIndex),{});return _.children[Ee]=new Ae(d.segments.slice(f.pathIndex),d.children),Y(_,0,m)}return f.match&&0===m.length?new Ae(d.segments,{}):f.match&&!d.hasChildren()?ge(d,l,u):f.match?Y(d,0,m):ge(d,l,u)}function Y(d,l,u){if(0===u.length)return new Ae(d.segments,{});{const f=function(d){return zn(d[0])?d[0].outlets:{[Ee]:d}}(u),m={};return ue(f,(_,T)=>{"string"==typeof _&&(_=[_]),null!==_&&(m[T]=j(d.children[T],l,_))}),ue(d.children,(_,T)=>{void 0===f[T]&&(m[T]=_)}),new Ae(d.segments,m)}}function ge(d,l,u){const f=d.segments.slice(0,l);let m=0;for(;m<u.length;){const _=u[m];if(zn(_)){const U=rt(_.outlets);return new Ae(f,U)}if(0===m&&ui(u[0])){f.push(new gt(d.segments[l].path,Je(u[0]))),m++;continue}const T=zn(_)?_.outlets[Ee]:`${_}`,A=m<u.length-1?u[m+1]:null;T&&A&&ui(A)?(f.push(new gt(T,Je(A))),m+=2):(f.push(new gt(T,{})),m++)}return new Ae(f,{})}function rt(d){const l={};return ue(d,(u,f)=>{"string"==typeof u&&(u=[u]),null!==u&&(l[f]=ge(new Ae([],{}),0,u))}),l}function Je(d){const l={};return ue(d,(u,f)=>l[f]=`${u}`),l}function ke(d,l,u){return d==u.path&&_t(l,u.parameters)}class it{constructor(l,u,f,m){this.routeReuseStrategy=l,this.futureState=u,this.currState=f,this.forwardEvent=m}activate(l){const u=this.futureState._root,f=this.currState?this.currState._root:null;this.deactivateChildRoutes(u,f,l),fs(this.futureState.root),this.activateChildRoutes(u,f,l)}deactivateChildRoutes(l,u,f){const m=Br(u);l.children.forEach(_=>{const T=_.value.outlet;this.deactivateRoutes(_,m[T],f),delete m[T]}),ue(m,(_,T)=>{this.deactivateRouteAndItsChildren(_,f)})}deactivateRoutes(l,u,f){const m=l.value,_=u?u.value:null;if(m===_)if(m.component){const T=f.getContext(m.outlet);T&&this.deactivateChildRoutes(l,u,T.children)}else this.deactivateChildRoutes(l,u,f);else _&&this.deactivateRouteAndItsChildren(u,f)}deactivateRouteAndItsChildren(l,u){this.routeReuseStrategy.shouldDetach(l.value.snapshot)?this.detachAndStoreRouteSubtree(l,u):this.deactivateRouteAndOutlet(l,u)}detachAndStoreRouteSubtree(l,u){const f=u.getContext(l.value.outlet);if(f&&f.outlet){const m=f.outlet.detach(),_=f.children.onOutletDeactivated();this.routeReuseStrategy.store(l.value.snapshot,{componentRef:m,route:l,contexts:_})}}deactivateRouteAndOutlet(l,u){const f=u.getContext(l.value.outlet),m=f&&l.value.component?f.children:u,_=Br(l);for(const T of Object.keys(_))this.deactivateRouteAndItsChildren(_[T],m);f&&f.outlet&&(f.outlet.deactivate(),f.children.onOutletDeactivated(),f.attachRef=null,f.resolver=null,f.route=null)}activateChildRoutes(l,u,f){const m=Br(u);l.children.forEach(_=>{this.activateRoutes(_,m[_.value.outlet],f),this.forwardEvent(new kr(_.value.snapshot))}),l.children.length&&this.forwardEvent(new Lr(l.value.snapshot))}activateRoutes(l,u,f){const m=l.value,_=u?u.value:null;if(fs(m),m===_)if(m.component){const T=f.getOrCreateContext(m.outlet);this.activateChildRoutes(l,u,T.children)}else this.activateChildRoutes(l,u,f);else if(m.component){const T=f.getOrCreateContext(m.outlet);if(this.routeReuseStrategy.shouldAttach(m.snapshot)){const A=this.routeReuseStrategy.retrieve(m.snapshot);this.routeReuseStrategy.store(m.snapshot,null),T.children.onOutletReAttached(A.contexts),T.attachRef=A.componentRef,T.route=A.route.value,T.outlet&&T.outlet.attach(A.componentRef,A.route.value),St(A.route)}else{const A=function(d){for(let l=d.parent;l;l=l.parent){const u=l.routeConfig;if(u&&u._loadedConfig)return u._loadedConfig;if(u&&u.component)return null}return null}(m.snapshot),U=A?A.module.componentFactoryResolver:null;T.attachRef=null,T.route=m,T.resolver=U,T.outlet&&T.outlet.activateWith(m,U),this.activateChildRoutes(l,null,T.children)}}else this.activateChildRoutes(l,null,f)}}function St(d){fs(d.value),d.children.forEach(St)}class lt{constructor(l,u){this.routes=l,this.module=u}}function rn(d){return"function"==typeof d}function Ut(d){return d instanceof mn}const hn=Symbol("INITIAL_VALUE");function di(){return Jt(d=>function(...d){let l,u;return(0,ae.K)(d[d.length-1])&&(u=d.pop()),"function"==typeof d[d.length-1]&&(l=d.pop()),1===d.length&&(0,L.k)(d[0])&&(d=d[0]),(0,me.n)(d,u).lift(new we(l))}(d.map(l=>l.pipe(Ti(1),function(...d){const l=d[d.length-1];return(0,ae.K)(l)?(d.pop(),u=>wn(d,u,l)):u=>wn(d,u)}(hn)))).pipe(ln((l,u)=>{let f=!1;return u.reduce((m,_,T)=>m!==hn?m:(_===hn&&(f=!0),f||!1!==_&&T!==u.length-1&&!Ut(_)?m:_),l)},hn),Le(l=>l!==hn),(0,J.U)(l=>Ut(l)?l:!0===l),Ti(1)))}let ms=(()=>{class d{}return d.\u0275fac=function(u){return new(u||d)},d.\u0275cmp=b.Xpm({type:d,selectors:[["ng-component"]],decls:1,vars:0,template:function(u,f){1&u&&b._UZ(0,"router-outlet")},directives:function(){return[Gi]},encapsulation:2}),d})();function ve(d,l=""){for(let u=0;u<d.length;u++){const f=d[u];uf(f,pl(l,f))}}function uf(d,l){d.children&&ve(d.children,l)}function pl(d,l){return l?d||l.path?d&&!l.path?`${d}/`:!d&&l.path?l.path:`${d}/${l.path}`:"":d}function ko(d){const l=d.children&&d.children.map(ko),u=l?Object.assign(Object.assign({},d),{children:l}):Object.assign({},d);return!u.component&&(l||u.loadChildren)&&u.outlet&&u.outlet!==Ee&&(u.component=ms),u}function pn(d){return d.outlet||Ee}function Vo(d,l){const u=d.filter(f=>pn(f)===l);return u.push(...d.filter(f=>pn(f)!==l)),u}const za={matched:!1,consumedSegments:[],lastChild:0,parameters:{},positionalParamSegments:{}};function ys(d,l,u){var f;if(""===l.path)return"full"===l.pathMatch&&(d.hasChildren()||u.length>0)?Object.assign({},za):{matched:!0,consumedSegments:[],lastChild:0,parameters:{},positionalParamSegments:{}};const _=(l.matcher||ht)(u,d,l);if(!_)return Object.assign({},za);const T={};ue(_.posParams,(U,ne)=>{T[ne]=U.path});const A=_.consumed.length>0?Object.assign(Object.assign({},T),_.consumed[_.consumed.length-1].parameters):T;return{matched:!0,consumedSegments:_.consumed,lastChild:_.consumed.length,parameters:A,positionalParamSegments:null!==(f=_.posParams)&&void 0!==f?f:{}}}function z(d,l,u,f,m="corrected"){if(u.length>0&&function(d,l,u){return u.some(f=>hr(d,l,f)&&pn(f)!==Ee)}(d,u,f)){const T=new Ae(l,function(d,l,u,f){const m={};m[Ee]=f,f._sourceSegment=d,f._segmentIndexShift=l.length;for(const _ of u)if(""===_.path&&pn(_)!==Ee){const T=new Ae([],{});T._sourceSegment=d,T._segmentIndexShift=l.length,m[pn(_)]=T}return m}(d,l,f,new Ae(u,d.children)));return T._sourceSegment=d,T._segmentIndexShift=l.length,{segmentGroup:T,slicedSegments:[]}}if(0===u.length&&function(d,l,u){return u.some(f=>hr(d,l,f))}(d,u,f)){const T=new Ae(d.segments,function(d,l,u,f,m,_){const T={};for(const A of f)if(hr(d,u,A)&&!m[pn(A)]){const U=new Ae([],{});U._sourceSegment=d,U._segmentIndexShift="legacy"===_?d.segments.length:l.length,T[pn(A)]=U}return Object.assign(Object.assign({},m),T)}(d,l,u,f,d.children,m));return T._sourceSegment=d,T._segmentIndexShift=l.length,{segmentGroup:T,slicedSegments:u}}const _=new Ae(d.segments,d.children);return _._sourceSegment=d,_._segmentIndexShift=l.length,{segmentGroup:_,slicedSegments:u}}function hr(d,l,u){return(!(d.hasChildren()||l.length>0)||"full"!==u.pathMatch)&&""===u.path}function Dn(d,l,u,f){return!!(pn(d)===f||f!==Ee&&hr(l,u,d))&&("**"===d.path||ys(l,d,u).matched)}function Ds(d,l,u){return 0===l.length&&!d.children[u]}class Hr{constructor(l){this.segmentGroup=l||null}}class gl{constructor(l){this.urlTree=l}}function _s(d){return new X.y(l=>l.error(new Hr(d)))}function ji(d){return new X.y(l=>l.error(new gl(d)))}function Cs(d){return new X.y(l=>l.error(new Error(`Only absolute redirects can have named outlets. redirectTo: '${d}'`)))}class Qa{constructor(l,u,f,m,_){this.configLoader=u,this.urlSerializer=f,this.urlTree=m,this.config=_,this.allowRedirects=!0,this.ngModule=l.get(b.h0i)}apply(){const l=z(this.urlTree.root,[],[],this.config).segmentGroup,u=new Ae(l.segments,l.children);return this.expandSegmentGroup(this.ngModule,this.config,u,Ee).pipe((0,J.U)(_=>this.createUrlTree(_n(_),this.urlTree.queryParams,this.urlTree.fragment))).pipe(In(_=>{if(_ instanceof gl)return this.allowRedirects=!1,this.match(_.urlTree);throw _ instanceof Hr?this.noMatchError(_):_}))}match(l){return this.expandSegmentGroup(this.ngModule,this.config,l.root,Ee).pipe((0,J.U)(m=>this.createUrlTree(_n(m),l.queryParams,l.fragment))).pipe(In(m=>{throw m instanceof Hr?this.noMatchError(m):m}))}noMatchError(l){return new Error(`Cannot match any routes. URL Segment: '${l.segmentGroup}'`)}createUrlTree(l,u,f){const m=l.segments.length>0?new Ae([],{[Ee]:l}):l;return new mn(m,u,f)}expandSegmentGroup(l,u,f,m){return 0===f.segments.length&&f.hasChildren()?this.expandChildren(l,u,f).pipe((0,J.U)(_=>new Ae([],_))):this.expandSegment(l,f,u,f.segments,m,!0)}expandChildren(l,u,f){const m=[];for(const _ of Object.keys(f.children))"primary"===_?m.unshift(_):m.push(_);return(0,se.D)(m).pipe(wt(_=>{const T=f.children[_],A=Vo(u,_);return this.expandSegmentGroup(l,A,T,_).pipe((0,J.U)(U=>({segment:U,outlet:_})))}),ln((_,T)=>(_[T.outlet]=T.segment,_),{}),function(d,l){const u=arguments.length>=2;return f=>f.pipe(d?Le((m,_)=>d(m,_,f)):pe.y,xr(1),u?Tn(l):Pr(()=>new gn))}())}expandSegment(l,u,f,m,_,T){return(0,se.D)(f).pipe(wt(A=>this.expandSegmentAgainstRoute(l,u,f,A,m,_,T).pipe(In(ne=>{if(ne instanceof Hr)return W(null);throw ne}))),cn(A=>!!A),In((A,U)=>{if(A instanceof gn||"EmptyError"===A.name){if(Ds(u,m,_))return W(new Ae([],{}));throw new Hr(u)}throw A}))}expandSegmentAgainstRoute(l,u,f,m,_,T,A){return Dn(m,u,_,T)?void 0===m.redirectTo?this.matchSegmentAgainstRoute(l,u,m,_,T):A&&this.allowRedirects?this.expandSegmentAgainstRouteUsingRedirect(l,u,f,m,_,T):_s(u):_s(u)}expandSegmentAgainstRouteUsingRedirect(l,u,f,m,_,T){return"**"===m.path?this.expandWildCardWithParamsAgainstRouteUsingRedirect(l,f,m,T):this.expandRegularSegmentAgainstRouteUsingRedirect(l,u,f,m,_,T)}expandWildCardWithParamsAgainstRouteUsingRedirect(l,u,f,m){const _=this.applyRedirectCommands([],f.redirectTo,{});return f.redirectTo.startsWith("/")?ji(_):this.lineralizeSegments(f,_).pipe((0,ot.zg)(T=>{const A=new Ae(T,{});return this.expandSegment(l,A,u,T,m,!1)}))}expandRegularSegmentAgainstRouteUsingRedirect(l,u,f,m,_,T){const{matched:A,consumedSegments:U,lastChild:ne,positionalParamSegments:We}=ys(u,m,_);if(!A)return _s(u);const _e=this.applyRedirectCommands(U,m.redirectTo,We);return m.redirectTo.startsWith("/")?ji(_e):this.lineralizeSegments(m,_e).pipe((0,ot.zg)(st=>this.expandSegment(l,u,f,st.concat(_.slice(ne)),T,!1)))}matchSegmentAgainstRoute(l,u,f,m,_){if("**"===f.path)return f.loadChildren?(f._loadedConfig?W(f._loadedConfig):this.configLoader.load(l.injector,f)).pipe((0,J.U)(st=>(f._loadedConfig=st,new Ae(m,{})))):W(new Ae(m,{}));const{matched:T,consumedSegments:A,lastChild:U}=ys(u,f,m);if(!T)return _s(u);const ne=m.slice(U);return this.getChildConfig(l,f,m).pipe((0,ot.zg)(_e=>{const st=_e.module,He=_e.routes,{segmentGroup:qr,slicedSegments:_r}=z(u,A,ne,He),Yt=new Ae(qr.segments,qr.children);if(0===_r.length&&Yt.hasChildren())return this.expandChildren(st,He,Yt).pipe((0,J.U)(_i=>new Ae(A,_i)));if(0===He.length&&0===_r.length)return W(new Ae(A,{}));const Ns=pn(f)===_;return this.expandSegment(st,Yt,He,_r,Ns?Ee:_,!0).pipe((0,J.U)(Cn=>new Ae(A.concat(Cn.segments),Cn.children)))}))}getChildConfig(l,u,f){return u.children?W(new lt(u.children,l)):u.loadChildren?void 0!==u._loadedConfig?W(u._loadedConfig):this.runCanLoadGuards(l.injector,u,f).pipe((0,ot.zg)(m=>{return m?this.configLoader.load(l.injector,u).pipe((0,J.U)(_=>(u._loadedConfig=_,_))):(d=u,new X.y(l=>l.error(lr(`Cannot load children because the guard of the route "path: '${d.path}'" returned false`))));var d})):W(new lt([],l))}runCanLoadGuards(l,u,f){const m=u.canLoad;return m&&0!==m.length?W(m.map(T=>{const A=l.get(T);let U;if((d=A)&&rn(d.canLoad))U=A.canLoad(u,f);else{if(!rn(A))throw new Error("Invalid CanLoad guard");U=A(u,f)}var d;return Ne(U)})).pipe(di(),Qe(T=>{if(!Ut(T))return;const A=lr(`Redirecting to "${this.urlSerializer.serialize(T)}"`);throw A.url=T,A}),(0,J.U)(T=>!0===T)):W(!0)}lineralizeSegments(l,u){let f=[],m=u.root;for(;;){if(f=f.concat(m.segments),0===m.numberOfChildren)return W(f);if(m.numberOfChildren>1||!m.children[Ee])return Cs(l.redirectTo);m=m.children[Ee]}}applyRedirectCommands(l,u,f){return this.applyRedirectCreatreUrlTree(u,this.urlSerializer.parse(u),l,f)}applyRedirectCreatreUrlTree(l,u,f,m){const _=this.createSegmentGroup(l,u.root,f,m);return new mn(_,this.createQueryParams(u.queryParams,this.urlTree.queryParams),u.fragment)}createQueryParams(l,u){const f={};return ue(l,(m,_)=>{if("string"==typeof m&&m.startsWith(":")){const A=m.substring(1);f[_]=u[A]}else f[_]=m}),f}createSegmentGroup(l,u,f,m){const _=this.createSegments(l,u.segments,f,m);let T={};return ue(u.children,(A,U)=>{T[U]=this.createSegmentGroup(l,A,f,m)}),new Ae(_,T)}createSegments(l,u,f,m){return u.map(_=>_.path.startsWith(":")?this.findPosParam(l,_,m):this.findOrReturn(_,f))}findPosParam(l,u,f){const m=f[u.path.substring(1)];if(!m)throw new Error(`Cannot redirect to '${l}'. Cannot find '${u.path}'.`);return m}findOrReturn(l,u){let f=0;for(const m of u){if(m.path===l.path)return u.splice(f),m;f++}return l}}function _n(d){const l={};for(const f of Object.keys(d.children)){const _=_n(d.children[f]);(_.segments.length>0||_.hasChildren())&&(l[f]=_)}return function(d){if(1===d.numberOfChildren&&d.children[Ee]){const l=d.children[Ee];return new Ae(d.segments.concat(l.segments),l.children)}return d}(new Ae(d.segments,l))}class jo{constructor(l){this.path=l,this.route=this.path[this.path.length-1]}}class Es{constructor(l,u){this.component=l,this.route=u}}function yl(d,l,u){const f=d._root;return $r(f,l?l._root:null,u,[f.value])}function hi(d,l,u){const f=function(d){if(!d)return null;for(let l=d.parent;l;l=l.parent){const u=l.routeConfig;if(u&&u._loadedConfig)return u._loadedConfig}return null}(l);return(f?f.module.injector:u).get(d)}function $r(d,l,u,f,m={canDeactivateChecks:[],canActivateChecks:[]}){const _=Br(l);return d.children.forEach(T=>{(function(d,l,u,f,m={canDeactivateChecks:[],canActivateChecks:[]}){const _=d.value,T=l?l.value:null,A=u?u.getContext(d.value.outlet):null;if(T&&_.routeConfig===T.routeConfig){const U=function(d,l,u){if("function"==typeof u)return u(d,l);switch(u){case"pathParamsChange":return!Nt(d.url,l.url);case"pathParamsOrQueryParamsChange":return!Nt(d.url,l.url)||!_t(d.queryParams,l.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!hs(d,l)||!_t(d.queryParams,l.queryParams);case"paramsChange":default:return!hs(d,l)}}(T,_,_.routeConfig.runGuardsAndResolvers);U?m.canActivateChecks.push(new jo(f)):(_.data=T.data,_._resolvedData=T._resolvedData),$r(d,l,_.component?A?A.children:null:u,f,m),U&&A&&A.outlet&&A.outlet.isActivated&&m.canDeactivateChecks.push(new Es(A.outlet.component,T))}else T&&pr(l,A,m),m.canActivateChecks.push(new jo(f)),$r(d,null,_.component?A?A.children:null:u,f,m)})(T,_[T.value.outlet],u,f.concat([T.value]),m),delete _[T.value.outlet]}),ue(_,(T,A)=>pr(T,u.getContext(A),m)),m}function pr(d,l,u){const f=Br(d),m=d.value;ue(f,(_,T)=>{pr(_,m.component?l?l.children.getContext(T):null:l,u)}),u.canDeactivateChecks.push(new Es(m.component&&l&&l.outlet&&l.outlet.isActivated?l.outlet.component:null,m))}class gr{}function at(d){return new X.y(l=>l.error(d))}class El{constructor(l,u,f,m,_,T){this.rootComponentType=l,this.config=u,this.urlTree=f,this.url=m,this.paramsInheritanceStrategy=_,this.relativeLinkResolution=T}recognize(){const l=z(this.urlTree.root,[],[],this.config.filter(T=>void 0===T.redirectTo),this.relativeLinkResolution).segmentGroup,u=this.processSegmentGroup(this.config,l,Ee);if(null===u)return null;const f=new ds([],Object.freeze({}),Object.freeze(Object.assign({},this.urlTree.queryParams)),this.urlTree.fragment,{},Ee,this.rootComponentType,null,this.urlTree.root,-1,{}),m=new Rn(f,u),_=new Po(this.url,m);return this.inheritParamsAndData(_._root),_}inheritParamsAndData(l){const u=l.value,f=ki(u,this.paramsInheritanceStrategy);u.params=Object.freeze(f.params),u.data=Object.freeze(f.data),l.children.forEach(m=>this.inheritParamsAndData(m))}processSegmentGroup(l,u,f){return 0===u.segments.length&&u.hasChildren()?this.processChildren(l,u):this.processSegment(l,u,u.segments,f)}processChildren(l,u){const f=[];for(const _ of Object.keys(u.children)){const T=u.children[_],A=Vo(l,_),U=this.processSegmentGroup(A,T,_);if(null===U)return null;f.push(...U)}const m=iu(f);return m.sort((l,u)=>l.value.outlet===Ee?-1:u.value.outlet===Ee?1:l.value.outlet.localeCompare(u.value.outlet)),m}processSegment(l,u,f,m){for(const _ of l){const T=this.processSegmentAgainstRoute(_,u,f,m);if(null!==T)return T}return Ds(u,f,m)?[]:null}processSegmentAgainstRoute(l,u,f,m){if(l.redirectTo||!Dn(l,u,f,m))return null;let _,T=[],A=[];if("**"===l.path){const He=f.length>0?Pt(f).parameters:{};_=new ds(f,He,Object.freeze(Object.assign({},this.urlTree.queryParams)),this.urlTree.fragment,bs(l),pn(l),l.component,l,Bi(u),Ui(u)+f.length,Ho(l))}else{const He=ys(u,l,f);if(!He.matched)return null;T=He.consumedSegments,A=f.slice(He.lastChild),_=new ds(T,He.parameters,Object.freeze(Object.assign({},this.urlTree.queryParams)),this.urlTree.fragment,bs(l),pn(l),l.component,l,Bi(u),Ui(u)+T.length,Ho(l))}const U=(d=l).children?d.children:d.loadChildren?d._loadedConfig.routes:[],{segmentGroup:ne,slicedSegments:We}=z(u,T,A,U.filter(He=>void 0===He.redirectTo),this.relativeLinkResolution);var d;if(0===We.length&&ne.hasChildren()){const He=this.processChildren(U,ne);return null===He?null:[new Rn(_,He)]}if(0===U.length&&0===We.length)return[new Rn(_,[])];const _e=pn(l)===m,st=this.processSegment(U,ne,We,_e?Ee:m);return null===st?null:[new Rn(_,st)]}}function bl(d){const l=d.value.routeConfig;return l&&""===l.path&&void 0===l.redirectTo}function iu(d){const l=[],u=new Set;for(const f of d){if(!bl(f)){l.push(f);continue}const m=l.find(_=>f.value.routeConfig===_.value.routeConfig);void 0!==m?(m.children.push(...f.children),u.add(m)):l.push(f)}for(const f of u){const m=iu(f.children);l.push(new Rn(f.value,m))}return l.filter(f=>!u.has(f))}function Bi(d){let l=d;for(;l._sourceSegment;)l=l._sourceSegment;return l}function Ui(d){let l=d,u=l._segmentIndexShift?l._segmentIndexShift:0;for(;l._sourceSegment;)l=l._sourceSegment,u+=l._segmentIndexShift?l._segmentIndexShift:0;return u-1}function bs(d){return d.data||{}}function Ho(d){return d.resolve||{}}function Gr(d){return Jt(l=>{const u=d(l);return u?(0,se.D)(u).pipe((0,J.U)(()=>l)):W(l)})}class Ml extends class{shouldDetach(l){return!1}store(l,u){}shouldAttach(l){return!1}retrieve(l){return null}shouldReuseRoute(l,u){return l.routeConfig===u.routeConfig}}{}const su=new b.OlP("ROUTES");class Al{constructor(l,u,f,m){this.loader=l,this.compiler=u,this.onLoadStartListener=f,this.onLoadEndListener=m}load(l,u){if(u._loader$)return u._loader$;this.onLoadStartListener&&this.onLoadStartListener(u);const m=this.loadModuleFactory(u.loadChildren).pipe((0,J.U)(_=>{this.onLoadEndListener&&this.onLoadEndListener(u);const T=_.create(l);return new lt(jr(T.injector.get(su,void 0,b.XFs.Self|b.XFs.Optional)).map(ko),T)}),In(_=>{throw u._loader$=void 0,_}));return u._loader$=new $e.c(m,()=>new oe.xQ).pipe((0,Bn.x)()),u._loader$}loadModuleFactory(l){return"string"==typeof l?(0,se.D)(this.loader.load(l)):Ne(l()).pipe((0,ot.zg)(u=>u instanceof b.YKP?W(u):(0,se.D)(this.compiler.compileModuleAsync(u))))}}class ws{constructor(){this.outlet=null,this.route=null,this.resolver=null,this.children=new Wr,this.attachRef=null}}class Wr{constructor(){this.contexts=new Map}onChildOutletCreated(l,u){const f=this.getOrCreateContext(l);f.outlet=u,this.contexts.set(l,f)}onChildOutletDestroyed(l){const u=this.getContext(l);u&&(u.outlet=null,u.attachRef=null)}onOutletDeactivated(){const l=this.contexts;return this.contexts=new Map,l}onOutletReAttached(l){this.contexts=l}getOrCreateContext(l){let u=this.getContext(l);return u||(u=new ws,this.contexts.set(l,u)),u}getContext(l){return this.contexts.get(l)||null}}class Is{shouldProcessUrl(l){return!0}extract(l){return l}merge(l,u){return l}}function ou(d){throw d}function au(d,l,u){return l.parse("/")}function zr(d,l){return W(null)}const Rl={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},pi={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"};let Lt=(()=>{class d{constructor(u,f,m,_,T,A,U,ne){this.rootComponentType=u,this.urlSerializer=f,this.rootContexts=m,this.location=_,this.config=ne,this.lastSuccessfulNavigation=null,this.currentNavigation=null,this.disposed=!1,this.lastLocationChangeInfo=null,this.navigationId=0,this.currentPageId=0,this.isNgZoneEnabled=!1,this.events=new oe.xQ,this.errorHandler=ou,this.malformedUriErrorHandler=au,this.navigated=!1,this.lastSuccessfulId=-1,this.hooks={beforePreactivation:zr,afterPreactivation:zr},this.urlHandlingStrategy=new Is,this.routeReuseStrategy=new Ml,this.onSameUrlNavigation="ignore",this.paramsInheritanceStrategy="emptyOnly",this.urlUpdateStrategy="deferred",this.relativeLinkResolution="corrected",this.canceledNavigationResolution="replace",this.ngModule=T.get(b.h0i),this.console=T.get(b.c2e);const st=T.get(b.R0b);this.isNgZoneEnabled=st instanceof b.R0b&&b.R0b.isInAngularZone(),this.resetConfig(ne),this.currentUrlTree=new mn(new Ae([],{}),{},null),this.rawUrlTree=this.currentUrlTree,this.browserUrlTree=this.currentUrlTree,this.configLoader=new Al(A,U,He=>this.triggerEvent(new Wt(He)),He=>this.triggerEvent(new Hn(He))),this.routerState=xo(this.currentUrlTree,this.rootComponentType),this.transitions=new q({id:0,targetPageId:0,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,extractedUrl:this.urlHandlingStrategy.extract(this.currentUrlTree),urlAfterRedirects:this.urlHandlingStrategy.extract(this.currentUrlTree),rawUrl:this.currentUrlTree,extras:{},resolve:null,reject:null,promise:Promise.resolve(!0),source:"imperative",restoredState:null,currentSnapshot:this.routerState.snapshot,targetSnapshot:null,currentRouterState:this.routerState,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.navigations=this.setupNavigations(this.transitions),this.processNavigations()}get browserPageId(){var u;return null===(u=this.location.getState())||void 0===u?void 0:u.\u0275routerPageId}setupNavigations(u){const f=this.events;return u.pipe(Le(m=>0!==m.id),(0,J.U)(m=>Object.assign(Object.assign({},m),{extractedUrl:this.urlHandlingStrategy.extract(m.rawUrl)})),Jt(m=>{let _=!1,T=!1;return W(m).pipe(Qe(A=>{this.currentNavigation={id:A.id,initialUrl:A.currentRawUrl,extractedUrl:A.extractedUrl,trigger:A.source,extras:A.extras,previousNavigation:this.lastSuccessfulNavigation?Object.assign(Object.assign({},this.lastSuccessfulNavigation),{previousNavigation:null}):null}}),Jt(A=>{const U=this.browserUrlTree.toString(),ne=!this.navigated||A.extractedUrl.toString()!==U||U!==this.currentUrlTree.toString();if(("reload"===this.onSameUrlNavigation||ne)&&this.urlHandlingStrategy.shouldProcessUrl(A.rawUrl))return Wo(A.source)&&(this.browserUrlTree=A.extractedUrl),W(A).pipe(Jt(_e=>{const st=this.transitions.getValue();return f.next(new Un(_e.id,this.serializeUrl(_e.extractedUrl),_e.source,_e.restoredState)),st!==this.transitions.getValue()?Fe:Promise.resolve(_e)}),function(d,l,u,f){return Jt(m=>function(d,l,u,f,m){return new Qa(d,l,u,f,m).apply()}(d,l,u,m.extractedUrl,f).pipe((0,J.U)(_=>Object.assign(Object.assign({},m),{urlAfterRedirects:_}))))}(this.ngModule.injector,this.configLoader,this.urlSerializer,this.config),Qe(_e=>{this.currentNavigation=Object.assign(Object.assign({},this.currentNavigation),{finalUrl:_e.urlAfterRedirects})}),function(d,l,u,f,m){return(0,ot.zg)(_=>function(d,l,u,f,m="emptyOnly",_="legacy"){try{const T=new El(d,l,u,f,m,_).recognize();return null===T?at(new gr):W(T)}catch(T){return at(T)}}(d,l,_.urlAfterRedirects,u(_.urlAfterRedirects),f,m).pipe((0,J.U)(T=>Object.assign(Object.assign({},_),{targetSnapshot:T}))))}(this.rootComponentType,this.config,_e=>this.serializeUrl(_e),this.paramsInheritanceStrategy,this.relativeLinkResolution),Qe(_e=>{"eager"===this.urlUpdateStrategy&&(_e.extras.skipLocationChange||this.setBrowserUrl(_e.urlAfterRedirects,_e),this.browserUrlTree=_e.urlAfterRedirects);const st=new Sn(_e.id,this.serializeUrl(_e.extractedUrl),this.serializeUrl(_e.urlAfterRedirects),_e.targetSnapshot);f.next(st)}));if(ne&&this.rawUrlTree&&this.urlHandlingStrategy.shouldProcessUrl(this.rawUrlTree)){const{id:st,extractedUrl:He,source:qr,restoredState:_r,extras:Yt}=A,Ns=new Un(st,this.serializeUrl(He),qr,_r);f.next(Ns);const nt=xo(He,this.rootComponentType).snapshot;return W(Object.assign(Object.assign({},A),{targetSnapshot:nt,urlAfterRedirects:He,extras:Object.assign(Object.assign({},Yt),{skipLocationChange:!1,replaceUrl:!1})}))}return this.rawUrlTree=A.rawUrl,this.browserUrlTree=A.urlAfterRedirects,A.resolve(null),Fe}),Gr(A=>{const{targetSnapshot:U,id:ne,extractedUrl:We,rawUrl:_e,extras:{skipLocationChange:st,replaceUrl:He}}=A;return this.hooks.beforePreactivation(U,{navigationId:ne,appliedUrlTree:We,rawUrlTree:_e,skipLocationChange:!!st,replaceUrl:!!He})}),Qe(A=>{const U=new ur(A.id,this.serializeUrl(A.extractedUrl),this.serializeUrl(A.urlAfterRedirects),A.targetSnapshot);this.triggerEvent(U)}),(0,J.U)(A=>Object.assign(Object.assign({},A),{guards:yl(A.targetSnapshot,A.currentSnapshot,this.rootContexts)})),function(d,l){return(0,ot.zg)(u=>{const{targetSnapshot:f,currentSnapshot:m,guards:{canActivateChecks:_,canDeactivateChecks:T}}=u;return 0===T.length&&0===_.length?W(Object.assign(Object.assign({},u),{guardsResult:!0})):function(d,l,u,f){return(0,se.D)(d).pipe((0,ot.zg)(m=>function(d,l,u,f,m){const _=l&&l.routeConfig?l.routeConfig.canDeactivate:null;return _&&0!==_.length?W(_.map(A=>{const U=hi(A,l,m);let ne;if(function(d){return d&&rn(d.canDeactivate)}(U))ne=Ne(U.canDeactivate(d,l,u,f));else{if(!rn(U))throw new Error("Invalid CanDeactivate guard");ne=Ne(U(d,l,u,f))}return ne.pipe(cn())})).pipe(di()):W(!0)}(m.component,m.route,u,l,f)),cn(m=>!0!==m,!0))}(T,f,m,d).pipe((0,ot.zg)(A=>A&&function(d){return"boolean"==typeof d}(A)?function(d,l,u,f){return(0,se.D)(l).pipe(wt(m=>wn(function(d,l){return null!==d&&l&&l(new Ni(d)),W(!0)}(m.route.parent,f),function(d,l){return null!==d&&l&&l(new us(d)),W(!0)}(m.route,f),function(d,l,u){const f=l[l.length-1],_=l.slice(0,l.length-1).reverse().map(T=>function(d){const l=d.routeConfig?d.routeConfig.canActivateChild:null;return l&&0!==l.length?{node:d,guards:l}:null}(T)).filter(T=>null!==T).map(T=>Se(()=>W(T.guards.map(U=>{const ne=hi(U,T.node,u);let We;if(function(d){return d&&rn(d.canActivateChild)}(ne))We=Ne(ne.canActivateChild(f,d));else{if(!rn(ne))throw new Error("Invalid CanActivateChild guard");We=Ne(ne(f,d))}return We.pipe(cn())})).pipe(di())));return W(_).pipe(di())}(d,m.path,u),function(d,l,u){const f=l.routeConfig?l.routeConfig.canActivate:null;return f&&0!==f.length?W(f.map(_=>Se(()=>{const T=hi(_,l,u);let A;if(function(d){return d&&rn(d.canActivate)}(T))A=Ne(T.canActivate(l,d));else{if(!rn(T))throw new Error("Invalid CanActivate guard");A=Ne(T(l,d))}return A.pipe(cn())}))).pipe(di()):W(!0)}(d,m.route,u))),cn(m=>!0!==m,!0))}(f,_,d,l):W(A)),(0,J.U)(A=>Object.assign(Object.assign({},u),{guardsResult:A})))})}(this.ngModule.injector,A=>this.triggerEvent(A)),Qe(A=>{if(Ut(A.guardsResult)){const ne=lr(`Redirecting to "${this.serializeUrl(A.guardsResult)}"`);throw ne.url=A.guardsResult,ne}const U=new Or(A.id,this.serializeUrl(A.extractedUrl),this.serializeUrl(A.urlAfterRedirects),A.targetSnapshot,!!A.guardsResult);this.triggerEvent(U)}),Le(A=>!!A.guardsResult||(this.restoreHistory(A),this.cancelNavigationTransition(A,""),!1)),Gr(A=>{if(A.guards.canActivateChecks.length)return W(A).pipe(Qe(U=>{const ne=new Fr(U.id,this.serializeUrl(U.extractedUrl),this.serializeUrl(U.urlAfterRedirects),U.targetSnapshot);this.triggerEvent(ne)}),Jt(U=>{let ne=!1;return W(U).pipe(function(d,l){return(0,ot.zg)(u=>{const{targetSnapshot:f,guards:{canActivateChecks:m}}=u;if(!m.length)return W(u);let _=0;return(0,se.D)(m).pipe(wt(T=>function(d,l,u,f){return function(d,l,u,f){const m=Object.keys(d);if(0===m.length)return W({});const _={};return(0,se.D)(m).pipe((0,ot.zg)(T=>function(d,l,u,f){const m=hi(d,l,f);return Ne(m.resolve?m.resolve(l,u):m(l,u))}(d[T],l,u,f).pipe(Qe(A=>{_[T]=A}))),xr(1),(0,ot.zg)(()=>Object.keys(_).length===m.length?W(_):Fe))}(d._resolve,d,l,f).pipe((0,J.U)(_=>(d._resolvedData=_,d.data=Object.assign(Object.assign({},d.data),ki(d,u).resolve),null)))}(T.route,f,d,l)),Qe(()=>_++),xr(1),(0,ot.zg)(T=>_===m.length?W(u):Fe))})}(this.paramsInheritanceStrategy,this.ngModule.injector),Qe({next:()=>ne=!0,complete:()=>{ne||(this.restoreHistory(U),this.cancelNavigationTransition(U,"At least one route resolver didn't emit any value."))}}))}),Qe(U=>{const ne=new Pi(U.id,this.serializeUrl(U.extractedUrl),this.serializeUrl(U.urlAfterRedirects),U.targetSnapshot);this.triggerEvent(ne)}))}),Gr(A=>{const{targetSnapshot:U,id:ne,extractedUrl:We,rawUrl:_e,extras:{skipLocationChange:st,replaceUrl:He}}=A;return this.hooks.afterPreactivation(U,{navigationId:ne,appliedUrlTree:We,rawUrlTree:_e,skipLocationChange:!!st,replaceUrl:!!He})}),(0,J.U)(A=>{const U=function(d,l,u){const f=yn(d,l._root,u?u._root:void 0);return new Ro(f,l)}(this.routeReuseStrategy,A.targetSnapshot,A.currentRouterState);return Object.assign(Object.assign({},A),{targetRouterState:U})}),Qe(A=>{this.currentUrlTree=A.urlAfterRedirects,this.rawUrlTree=this.urlHandlingStrategy.merge(A.urlAfterRedirects,A.rawUrl),this.routerState=A.targetRouterState,"deferred"===this.urlUpdateStrategy&&(A.extras.skipLocationChange||this.setBrowserUrl(this.rawUrlTree,A),this.browserUrlTree=A.urlAfterRedirects)}),((d,l,u)=>(0,J.U)(f=>(new it(l,f.targetRouterState,f.currentRouterState,u).activate(d),f)))(this.rootContexts,this.routeReuseStrategy,A=>this.triggerEvent(A)),Qe({next(){_=!0},complete(){_=!0}}),function(d){return l=>l.lift(new Ge(d))}(()=>{var A;if(!_&&!T){const U=`Navigation ID ${m.id} is not equal to the current navigation id ${this.navigationId}`;"replace"===this.canceledNavigationResolution?(this.restoreHistory(m),this.cancelNavigationTransition(m,U)):this.cancelNavigationTransition(m,U)}(null===(A=this.currentNavigation)||void 0===A?void 0:A.id)===m.id&&(this.currentNavigation=null)}),In(A=>{if(T=!0,function(d){return d&&d[Mn]}(A)){const U=Ut(A.url);U||(this.navigated=!0,this.restoreHistory(m,!0));const ne=new dn(m.id,this.serializeUrl(m.extractedUrl),A.message);f.next(ne),U?setTimeout(()=>{const We=this.urlHandlingStrategy.merge(A.url,this.rawUrlTree),_e={skipLocationChange:m.extras.skipLocationChange,replaceUrl:"eager"===this.urlUpdateStrategy||Wo(m.source)};this.scheduleNavigation(We,"imperative",null,_e,{resolve:m.resolve,reject:m.reject,promise:m.promise})},0):m.resolve(!1)}else{this.restoreHistory(m,!0);const U=new Be(m.id,this.serializeUrl(m.extractedUrl),A);f.next(U);try{m.resolve(this.errorHandler(A))}catch(ne){m.reject(ne)}}return Fe}))}))}resetRootComponentType(u){this.rootComponentType=u,this.routerState.root.component=this.rootComponentType}getTransition(){const u=this.transitions.value;return u.urlAfterRedirects=this.browserUrlTree,u}setTransition(u){this.transitions.next(Object.assign(Object.assign({},this.getTransition()),u))}initialNavigation(){this.setUpLocationChangeListener(),0===this.navigationId&&this.navigateByUrl(this.location.path(!0),{replaceUrl:!0})}setUpLocationChangeListener(){this.locationSubscription||(this.locationSubscription=this.location.subscribe(u=>{const f=this.extractLocationChangeInfoFromEvent(u);this.shouldScheduleNavigation(this.lastLocationChangeInfo,f)&&setTimeout(()=>{const{source:m,state:_,urlTree:T}=f,A={replaceUrl:!0};if(_){const U=Object.assign({},_);delete U.navigationId,delete U.\u0275routerPageId,0!==Object.keys(U).length&&(A.state=U)}this.scheduleNavigation(T,m,_,A)},0),this.lastLocationChangeInfo=f}))}extractLocationChangeInfoFromEvent(u){var f;return{source:"popstate"===u.type?"popstate":"hashchange",urlTree:this.parseUrl(u.url),state:(null===(f=u.state)||void 0===f?void 0:f.navigationId)?u.state:null,transitionId:this.getTransition().id}}shouldScheduleNavigation(u,f){if(!u)return!0;const m=f.urlTree.toString()===u.urlTree.toString();return f.transitionId!==u.transitionId||!m||!("hashchange"===f.source&&"popstate"===u.source||"popstate"===f.source&&"hashchange"===u.source)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.currentNavigation}triggerEvent(u){this.events.next(u)}resetConfig(u){ve(u),this.config=u.map(ko),this.navigated=!1,this.lastSuccessfulId=-1}ngOnDestroy(){this.dispose()}dispose(){this.transitions.complete(),this.locationSubscription&&(this.locationSubscription.unsubscribe(),this.locationSubscription=void 0),this.disposed=!0}createUrlTree(u,f={}){const{relativeTo:m,queryParams:_,fragment:T,queryParamsHandling:A,preserveFragment:U}=f,ne=m||this.routerState.root,We=U?this.currentUrlTree.fragment:T;let _e=null;switch(A){case"merge":_e=Object.assign(Object.assign({},this.currentUrlTree.queryParams),_);break;case"preserve":_e=this.currentUrlTree.queryParams;break;default:_e=_||null}return null!==_e&&(_e=this.removeEmptyProps(_e)),function(d,l,u,f,m){if(0===u.length)return li(l.root,l.root,l,f,m);const _=function(d){if("string"==typeof d[0]&&1===d.length&&"/"===d[0])return new ci(!0,0,d);let l=0,u=!1;const f=d.reduce((m,_,T)=>{if("object"==typeof _&&null!=_){if(_.outlets){const A={};return ue(_.outlets,(U,ne)=>{A[ne]="string"==typeof U?U.split("/"):U}),[...m,{outlets:A}]}if(_.segmentPath)return[...m,_.segmentPath]}return"string"!=typeof _?[...m,_]:0===T?(_.split("/").forEach((A,U)=>{0==U&&"."===A||(0==U&&""===A?u=!0:".."===A?l++:""!=A&&m.push(A))}),m):[...m,_]},[]);return new ci(u,l,f)}(u);if(_.toRoot())return li(l.root,new Ae([],{}),l,f,m);const T=function(d,l,u){if(d.isAbsolute)return new D(l.root,!0,0);if(-1===u.snapshot._lastPathIndex){const _=u.snapshot._urlSegment;return new D(_,_===l.root,0)}const f=ui(d.commands[0])?0:1;return function(d,l,u){let f=d,m=l,_=u;for(;_>m;){if(_-=m,f=f.parent,!f)throw new Error("Invalid number of '../'");m=f.segments.length}return new D(f,!1,m-_)}(u.snapshot._urlSegment,u.snapshot._lastPathIndex+f,d.numberOfDoubleDots)}(_,l,d),A=T.processChildren?Y(T.segmentGroup,T.index,_.commands):j(T.segmentGroup,T.index,_.commands);return li(T.segmentGroup,A,l,f,m)}(ne,this.currentUrlTree,u,_e,null!=We?We:null)}navigateByUrl(u,f={skipLocationChange:!1}){const m=Ut(u)?u:this.parseUrl(u),_=this.urlHandlingStrategy.merge(m,this.rawUrlTree);return this.scheduleNavigation(_,"imperative",null,f)}navigate(u,f={skipLocationChange:!1}){return function(d){for(let l=0;l<d.length;l++){const u=d[l];if(null==u)throw new Error(`The requested path contains ${u} segment at index ${l}`)}}(u),this.navigateByUrl(this.createUrlTree(u,f),f)}serializeUrl(u){return this.urlSerializer.serialize(u)}parseUrl(u){let f;try{f=this.urlSerializer.parse(u)}catch(m){f=this.malformedUriErrorHandler(m,this.urlSerializer,u)}return f}isActive(u,f){let m;if(m=!0===f?Object.assign({},Rl):!1===f?Object.assign({},pi):f,Ut(u))return N(this.currentUrlTree,u,m);const _=this.parseUrl(u);return N(this.currentUrlTree,_,m)}removeEmptyProps(u){return Object.keys(u).reduce((f,m)=>{const _=u[m];return null!=_&&(f[m]=_),f},{})}processNavigations(){this.navigations.subscribe(u=>{this.navigated=!0,this.lastSuccessfulId=u.id,this.currentPageId=u.targetPageId,this.events.next(new ar(u.id,this.serializeUrl(u.extractedUrl),this.serializeUrl(this.currentUrlTree))),this.lastSuccessfulNavigation=this.currentNavigation,u.resolve(!0)},u=>{this.console.warn(`Unhandled Navigation Error: ${u}`)})}scheduleNavigation(u,f,m,_,T){var A,U;if(this.disposed)return Promise.resolve(!1);const ne=this.getTransition(),We=Wo(f)&&ne&&!Wo(ne.source),He=(this.lastSuccessfulId===ne.id||this.currentNavigation?ne.rawUrl:ne.urlAfterRedirects).toString()===u.toString();if(We&&He)return Promise.resolve(!0);let qr,_r,Yt;T?(qr=T.resolve,_r=T.reject,Yt=T.promise):Yt=new Promise((Cn,_i)=>{qr=Cn,_r=_i});const Ns=++this.navigationId;let nt;return"computed"===this.canceledNavigationResolution?(0===this.currentPageId&&(m=this.location.getState()),nt=m&&m.\u0275routerPageId?m.\u0275routerPageId:_.replaceUrl||_.skipLocationChange?null!==(A=this.browserPageId)&&void 0!==A?A:0:(null!==(U=this.browserPageId)&&void 0!==U?U:0)+1):nt=0,this.setTransition({id:Ns,targetPageId:nt,source:f,restoredState:m,currentUrlTree:this.currentUrlTree,currentRawUrl:this.rawUrlTree,rawUrl:u,extras:_,resolve:qr,reject:_r,promise:Yt,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),Yt.catch(Cn=>Promise.reject(Cn))}setBrowserUrl(u,f){const m=this.urlSerializer.serialize(u),_=Object.assign(Object.assign({},f.extras.state),this.generateNgRouterState(f.id,f.targetPageId));this.location.isCurrentPathEqualTo(m)||f.extras.replaceUrl?this.location.replaceState(m,"",_):this.location.go(m,"",_)}restoreHistory(u,f=!1){var m,_;if("computed"===this.canceledNavigationResolution){const T=this.currentPageId-u.targetPageId;"popstate"!==u.source&&"eager"!==this.urlUpdateStrategy&&this.currentUrlTree!==(null===(m=this.currentNavigation)||void 0===m?void 0:m.finalUrl)||0===T?this.currentUrlTree===(null===(_=this.currentNavigation)||void 0===_?void 0:_.finalUrl)&&0===T&&(this.resetState(u),this.browserUrlTree=u.currentUrlTree,this.resetUrlToCurrentUrlTree()):this.location.historyGo(T)}else"replace"===this.canceledNavigationResolution&&(f&&this.resetState(u),this.resetUrlToCurrentUrlTree())}resetState(u){this.routerState=u.currentRouterState,this.currentUrlTree=u.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,u.rawUrl)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}cancelNavigationTransition(u,f){const m=new dn(u.id,this.serializeUrl(u.extractedUrl),f);this.triggerEvent(m),u.resolve(!1)}generateNgRouterState(u,f){return"computed"===this.canceledNavigationResolution?{navigationId:u,\u0275routerPageId:f}:{navigationId:u}}}return d.\u0275fac=function(u){return new(u||d)(b.LFG(b.DyG),b.LFG(ze),b.LFG(Wr),b.LFG(I.Ye),b.LFG(b.zs3),b.LFG(b.v3s),b.LFG(b.Sil),b.LFG(void 0))},d.\u0275prov=b.Yz7({token:d,factory:d.\u0275fac}),d})();function Wo(d){return"imperative"!==d}let Gi=(()=>{class d{constructor(u,f,m,_,T){this.parentContexts=u,this.location=f,this.resolver=m,this.changeDetector=T,this.activated=null,this._activatedRoute=null,this.activateEvents=new b.vpe,this.deactivateEvents=new b.vpe,this.name=_||Ee,u.onChildOutletCreated(this.name,this)}ngOnDestroy(){this.parentContexts.onChildOutletDestroyed(this.name)}ngOnInit(){if(!this.activated){const u=this.parentContexts.getContext(this.name);u&&u.route&&(u.attachRef?this.attach(u.attachRef,u.route):this.activateWith(u.route,u.resolver||null))}}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new Error("Outlet is not activated");this.location.detach();const u=this.activated;return this.activated=null,this._activatedRoute=null,u}attach(u,f){this.activated=u,this._activatedRoute=f,this.location.insert(u.hostView)}deactivate(){if(this.activated){const u=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(u)}}activateWith(u,f){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=u;const T=(f=f||this.resolver).resolveComponentFactory(u._futureSnapshot.routeConfig.component),A=this.parentContexts.getOrCreateContext(this.name).children,U=new zo(u,A,this.location.injector);this.activated=this.location.createComponent(T,this.location.length,U),this.changeDetector.markForCheck(),this.activateEvents.emit(this.activated.instance)}}return d.\u0275fac=function(u){return new(u||d)(b.Y36(Wr),b.Y36(b.s_b),b.Y36(b._Vd),b.$8M("name"),b.Y36(b.sBO))},d.\u0275dir=b.lG2({type:d,selectors:[["router-outlet"]],outputs:{activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"]}),d})();class zo{constructor(l,u,f){this.route=l,this.childContexts=u,this.parent=f}get(l,u){return l===Wn?this.route:l===Wr?this.childContexts:this.parent.get(l,u)}}class Wi{}class uu{preload(l,u){return W(null)}}let Yo=(()=>{class d{constructor(u,f,m,_,T){this.router=u,this.injector=_,this.preloadingStrategy=T,this.loader=new Al(f,m,ne=>u.triggerEvent(new Wt(ne)),ne=>u.triggerEvent(new Hn(ne)))}setUpPreloading(){this.subscription=this.router.events.pipe(Le(u=>u instanceof ar),wt(()=>this.preload())).subscribe(()=>{})}preload(){const u=this.injector.get(b.h0i);return this.processRoutes(u,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(u,f){const m=[];for(const _ of f)if(_.loadChildren&&!_.canLoad&&_._loadedConfig){const T=_._loadedConfig;m.push(this.processRoutes(T.module,T.routes))}else _.loadChildren&&!_.canLoad?m.push(this.preloadConfig(u,_)):_.children&&m.push(this.processRoutes(u,_.children));return(0,se.D)(m).pipe((0,ft.J)(),(0,J.U)(_=>{}))}preloadConfig(u,f){return this.preloadingStrategy.preload(f,()=>(f._loadedConfig?W(f._loadedConfig):this.loader.load(u.injector,f)).pipe((0,ot.zg)(_=>(f._loadedConfig=_,this.processRoutes(_.module,_.routes)))))}}return d.\u0275fac=function(u){return new(u||d)(b.LFG(Lt),b.LFG(b.v3s),b.LFG(b.Sil),b.LFG(b.zs3),b.LFG(Wi))},d.\u0275prov=b.Yz7({token:d,factory:d.\u0275fac}),d})(),Rs=(()=>{class d{constructor(u,f,m={}){this.router=u,this.viewportScroller=f,this.options=m,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},m.scrollPositionRestoration=m.scrollPositionRestoration||"disabled",m.anchorScrolling=m.anchorScrolling||"disabled"}init(){"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.router.events.subscribe(u=>{u instanceof Un?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=u.navigationTrigger,this.restoredId=u.restoredState?u.restoredState.navigationId:0):u instanceof ar&&(this.lastId=u.id,this.scheduleScrollEvent(u,this.router.parseUrl(u.urlAfterRedirects).fragment))})}consumeScrollEvents(){return this.router.events.subscribe(u=>{u instanceof It&&(u.position?"top"===this.options.scrollPositionRestoration?this.viewportScroller.scrollToPosition([0,0]):"enabled"===this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition(u.position):u.anchor&&"enabled"===this.options.anchorScrolling?this.viewportScroller.scrollToAnchor(u.anchor):"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(u,f){this.router.triggerEvent(new It(u,"popstate"===this.lastSource?this.store[this.restoredId]:null,f))}ngOnDestroy(){this.routerEventsSubscription&&this.routerEventsSubscription.unsubscribe(),this.scrollEventsSubscription&&this.scrollEventsSubscription.unsubscribe()}}return d.\u0275fac=function(u){return new(u||d)(b.LFG(Lt),b.LFG(I.EM),b.LFG(void 0))},d.\u0275prov=b.Yz7({token:d,factory:d.\u0275fac}),d})();const yr=new b.OlP("ROUTER_CONFIGURATION"),lu=new b.OlP("ROUTER_FORROOT_GUARD"),xs=[I.Ye,{provide:ze,useClass:Fi},{provide:Lt,useFactory:function(d,l,u,f,m,_,T,A={},U,ne){const We=new Lt(null,d,l,u,f,m,_,jr(T));return U&&(We.urlHandlingStrategy=U),ne&&(We.routeReuseStrategy=ne),function(d,l){d.errorHandler&&(l.errorHandler=d.errorHandler),d.malformedUriErrorHandler&&(l.malformedUriErrorHandler=d.malformedUriErrorHandler),d.onSameUrlNavigation&&(l.onSameUrlNavigation=d.onSameUrlNavigation),d.paramsInheritanceStrategy&&(l.paramsInheritanceStrategy=d.paramsInheritanceStrategy),d.relativeLinkResolution&&(l.relativeLinkResolution=d.relativeLinkResolution),d.urlUpdateStrategy&&(l.urlUpdateStrategy=d.urlUpdateStrategy)}(A,We),A.enableTracing&&We.events.subscribe(_e=>{var st,He;null===(st=console.group)||void 0===st||st.call(console,`Router Event: ${_e.constructor.name}`),console.log(_e.toString()),console.log(_e),null===(He=console.groupEnd)||void 0===He||He.call(console)}),We},deps:[ze,Wr,I.Ye,b.zs3,b.v3s,b.Sil,su,yr,[class{},new b.FiY],[class{},new b.FiY]]},Wr,{provide:Wn,useFactory:function(d){return d.routerState.root},deps:[Lt]},{provide:b.v3s,useClass:b.EAV},Yo,uu,class{preload(l,u){return u().pipe(In(()=>W(null)))}},{provide:yr,useValue:{enableTracing:!1}}];function zi(){return new b.PXZ("Router",Lt)}let Ol=(()=>{class d{constructor(u,f){}static forRoot(u,f){return{ngModule:d,providers:[xs,fu(u),{provide:lu,useFactory:mi,deps:[[Lt,new b.FiY,new b.tp0]]},{provide:yr,useValue:f||{}},{provide:I.S$,useFactory:du,deps:[I.lw,[new b.tBr(I.mr),new b.FiY],yr]},{provide:Rs,useFactory:cu,deps:[Lt,I.EM,yr]},{provide:Wi,useExisting:f&&f.preloadingStrategy?f.preloadingStrategy:uu},{provide:b.PXZ,multi:!0,useFactory:zi},[Dr,{provide:b.ip1,multi:!0,useFactory:Di,deps:[Dr]},{provide:Ps,useFactory:Yi,deps:[Dr]},{provide:b.tb,multi:!0,useExisting:Ps}]]}}static forChild(u){return{ngModule:d,providers:[fu(u)]}}}return d.\u0275fac=function(u){return new(u||d)(b.LFG(lu,8),b.LFG(Lt,8))},d.\u0275mod=b.oAB({type:d}),d.\u0275inj=b.cJS({}),d})();function cu(d,l,u){return u.scrollOffset&&l.setOffset(u.scrollOffset),new Rs(d,l,u)}function du(d,l,u={}){return u.useHash?new I.Do(d,l):new I.b0(d,l)}function mi(d){return"guarded"}function fu(d){return[{provide:b.deG,multi:!0,useValue:d},{provide:su,multi:!0,useValue:d}]}let Dr=(()=>{class d{constructor(u){this.injector=u,this.initNavigation=!1,this.destroyed=!1,this.resultOfPreactivationDone=new oe.xQ}appInitializer(){return this.injector.get(I.V_,Promise.resolve(null)).then(()=>{if(this.destroyed)return Promise.resolve(!0);let f=null;const m=new Promise(A=>f=A),_=this.injector.get(Lt),T=this.injector.get(yr);return"disabled"===T.initialNavigation?(_.setUpLocationChangeListener(),f(!0)):"enabled"===T.initialNavigation||"enabledBlocking"===T.initialNavigation?(_.hooks.afterPreactivation=()=>this.initNavigation?W(null):(this.initNavigation=!0,f(!0),this.resultOfPreactivationDone),_.initialNavigation()):f(!0),m})}bootstrapListener(u){const f=this.injector.get(yr),m=this.injector.get(Yo),_=this.injector.get(Rs),T=this.injector.get(Lt),A=this.injector.get(b.z2F);u===A.components[0]&&(("enabledNonBlocking"===f.initialNavigation||void 0===f.initialNavigation)&&T.initialNavigation(),m.setUpPreloading(),_.init(),T.resetRootComponentType(A.componentTypes[0]),this.resultOfPreactivationDone.next(null),this.resultOfPreactivationDone.complete())}ngOnDestroy(){this.destroyed=!0}}return d.\u0275fac=function(u){return new(u||d)(b.LFG(b.zs3))},d.\u0275prov=b.Yz7({token:d,factory:d.\u0275fac}),d})();function Di(d){return d.appInitializer.bind(d)}function Yi(d){return d.bootstrapListener.bind(d)}const Ps=new b.OlP("Router Initializer")},574:(Ye,ie,F)=>{F.d(ie,{y:()=>V});var I=F(393),ae=F(181),me=F(490),W=F(554),oe=F(487);var L=F(494);let V=(()=>{class Q{constructor(X){this._isScalar=!1,X&&(this._subscribe=X)}lift(X){const G=new Q;return G.source=this,G.operator=X,G}subscribe(X,G,Te){const{operator:qe}=this,we=function(Q,Z,X){if(Q){if(Q instanceof I.L)return Q;if(Q[ae.b])return Q[ae.b]()}return Q||Z||X?new I.L(Q,Z,X):new I.L(me.c)}(X,G,Te);if(we.add(qe?qe.call(we,this.source):this.source||L.v.useDeprecatedSynchronousErrorHandling&&!we.syncErrorThrowable?this._subscribe(we):this._trySubscribe(we)),L.v.useDeprecatedSynchronousErrorHandling&&we.syncErrorThrowable&&(we.syncErrorThrowable=!1,we.syncErrorThrown))throw we.syncErrorValue;return we}_trySubscribe(X){try{return this._subscribe(X)}catch(G){L.v.useDeprecatedSynchronousErrorHandling&&(X.syncErrorThrown=!0,X.syncErrorValue=G),function(Q){for(;Q;){const{closed:Z,destination:X,isStopped:G}=Q;if(Z||G)return!1;Q=X&&X instanceof I.L?X:null}return!0}(X)?X.error(G):console.warn(G)}}forEach(X,G){return new(G=H(G))((Te,qe)=>{let we;we=this.subscribe(te=>{try{X(te)}catch(Gt){qe(Gt),we&&we.unsubscribe()}},qe,Te)})}_subscribe(X){const{source:G}=this;return G&&G.subscribe(X)}[W.L](){return this}pipe(...X){return 0===X.length?this:function(Q){return 0===Q.length?oe.y:1===Q.length?Q[0]:function(X){return Q.reduce((G,Te)=>Te(G),X)}}(X)(this)}toPromise(X){return new(X=H(X))((G,Te)=>{let qe;this.subscribe(we=>qe=we,we=>Te(we),()=>G(qe))})}}return Q.create=Z=>new Q(Z),Q})();function H(Q){if(Q||(Q=L.v.Promise||Promise),!Q)throw new Error("no Promise impl found");return Q}},490:(Ye,ie,F)=>{F.d(ie,{c:()=>ae});var I=F(494),b=F(449);const ae={closed:!0,next(me){},error(me){if(I.v.useDeprecatedSynchronousErrorHandling)throw me;(0,b.z)(me)},complete(){}}},709:(Ye,ie,F)=>{F.d(ie,{xQ:()=>fe,Yc:()=>oe});var I=F(574),b=F(393),ae=F(319),me=F(971);class De extends ae.w{constructor(V,H){super(),this.subject=V,this.subscriber=H,this.closed=!1}unsubscribe(){if(this.closed)return;this.closed=!0;const V=this.subject,H=V.observers;if(this.subject=null,!H||0===H.length||V.isStopped||V.closed)return;const Q=H.indexOf(this.subscriber);-1!==Q&&H.splice(Q,1)}}var W=F(181);class oe extends b.L{constructor(V){super(V),this.destination=V}}let fe=(()=>{class L extends I.y{constructor(){super(),this.observers=[],this.closed=!1,this.isStopped=!1,this.hasError=!1,this.thrownError=null}[W.b](){return new oe(this)}lift(H){const Q=new q(this,this);return Q.operator=H,Q}next(H){if(this.closed)throw new me.N;if(!this.isStopped){const{observers:Q}=this,Z=Q.length,X=Q.slice();for(let G=0;G<Z;G++)X[G].next(H)}}error(H){if(this.closed)throw new me.N;this.hasError=!0,this.thrownError=H,this.isStopped=!0;const{observers:Q}=this,Z=Q.length,X=Q.slice();for(let G=0;G<Z;G++)X[G].error(H);this.observers.length=0}complete(){if(this.closed)throw new me.N;this.isStopped=!0;const{observers:H}=this,Q=H.length,Z=H.slice();for(let X=0;X<Q;X++)Z[X].complete();this.observers.length=0}unsubscribe(){this.isStopped=!0,this.closed=!0,this.observers=null}_trySubscribe(H){if(this.closed)throw new me.N;return super._trySubscribe(H)}_subscribe(H){if(this.closed)throw new me.N;return this.hasError?(H.error(this.thrownError),ae.w.EMPTY):this.isStopped?(H.complete(),ae.w.EMPTY):(this.observers.push(H),new De(this,H))}asObservable(){const H=new I.y;return H.source=this,H}}return L.create=(V,H)=>new q(V,H),L})();class q extends fe{constructor(V,H){super(),this.destination=V,this.source=H}next(V){const{destination:H}=this;H&&H.next&&H.next(V)}error(V){const{destination:H}=this;H&&H.error&&this.destination.error(V)}complete(){const{destination:V}=this;V&&V.complete&&this.destination.complete()}_subscribe(V){const{source:H}=this;return H?this.source.subscribe(V):ae.w.EMPTY}}},393:(Ye,ie,F)=>{F.d(ie,{L:()=>oe});var I=F(105),b=F(490),ae=F(319),me=F(181),De=F(494),W=F(449);class oe extends ae.w{constructor(L,V,H){switch(super(),this.syncErrorValue=null,this.syncErrorThrown=!1,this.syncErrorThrowable=!1,this.isStopped=!1,arguments.length){case 0:this.destination=b.c;break;case 1:if(!L){this.destination=b.c;break}if("object"==typeof L){L instanceof oe?(this.syncErrorThrowable=L.syncErrorThrowable,this.destination=L,L.add(this)):(this.syncErrorThrowable=!0,this.destination=new fe(this,L));break}default:this.syncErrorThrowable=!0,this.destination=new fe(this,L,V,H)}}[me.b](){return this}static create(L,V,H){const Q=new oe(L,V,H);return Q.syncErrorThrowable=!1,Q}next(L){this.isStopped||this._next(L)}error(L){this.isStopped||(this.isStopped=!0,this._error(L))}complete(){this.isStopped||(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe())}_next(L){this.destination.next(L)}_error(L){this.destination.error(L),this.unsubscribe()}_complete(){this.destination.complete(),this.unsubscribe()}_unsubscribeAndRecycle(){const{_parentOrParents:L}=this;return this._parentOrParents=null,this.unsubscribe(),this.closed=!1,this.isStopped=!1,this._parentOrParents=L,this}}class fe extends oe{constructor(L,V,H,Q){super(),this._parentSubscriber=L;let Z,X=this;(0,I.m)(V)?Z=V:V&&(Z=V.next,H=V.error,Q=V.complete,V!==b.c&&(X=Object.create(V),(0,I.m)(X.unsubscribe)&&this.add(X.unsubscribe.bind(X)),X.unsubscribe=this.unsubscribe.bind(this))),this._context=X,this._next=Z,this._error=H,this._complete=Q}next(L){if(!this.isStopped&&this._next){const{_parentSubscriber:V}=this;De.v.useDeprecatedSynchronousErrorHandling&&V.syncErrorThrowable?this.__tryOrSetError(V,this._next,L)&&this.unsubscribe():this.__tryOrUnsub(this._next,L)}}error(L){if(!this.isStopped){const{_parentSubscriber:V}=this,{useDeprecatedSynchronousErrorHandling:H}=De.v;if(this._error)H&&V.syncErrorThrowable?(this.__tryOrSetError(V,this._error,L),this.unsubscribe()):(this.__tryOrUnsub(this._error,L),this.unsubscribe());else if(V.syncErrorThrowable)H?(V.syncErrorValue=L,V.syncErrorThrown=!0):(0,W.z)(L),this.unsubscribe();else{if(this.unsubscribe(),H)throw L;(0,W.z)(L)}}}complete(){if(!this.isStopped){const{_parentSubscriber:L}=this;if(this._complete){const V=()=>this._complete.call(this._context);De.v.useDeprecatedSynchronousErrorHandling&&L.syncErrorThrowable?(this.__tryOrSetError(L,V),this.unsubscribe()):(this.__tryOrUnsub(V),this.unsubscribe())}else this.unsubscribe()}}__tryOrUnsub(L,V){try{L.call(this._context,V)}catch(H){if(this.unsubscribe(),De.v.useDeprecatedSynchronousErrorHandling)throw H;(0,W.z)(H)}}__tryOrSetError(L,V,H){if(!De.v.useDeprecatedSynchronousErrorHandling)throw new Error("bad call");try{V.call(this._context,H)}catch(Q){return De.v.useDeprecatedSynchronousErrorHandling?(L.syncErrorValue=Q,L.syncErrorThrown=!0,!0):((0,W.z)(Q),!0)}return!1}_unsubscribe(){const{_parentSubscriber:L}=this;this._context=null,this._parentSubscriber=null,L.unsubscribe()}}},319:(Ye,ie,F)=>{F.d(ie,{w:()=>W});var I=F(796),b=F(555),ae=F(105);const De=(()=>{function fe(q){return Error.call(this),this.message=q?`${q.length} errors occurred during unsubscription:\n${q.map((L,V)=>`${V+1}) ${L.toString()}`).join("\n  ")}`:"",this.name="UnsubscriptionError",this.errors=q,this}return fe.prototype=Object.create(Error.prototype),fe})();class W{constructor(q){this.closed=!1,this._parentOrParents=null,this._subscriptions=null,q&&(this._ctorUnsubscribe=!0,this._unsubscribe=q)}unsubscribe(){let q;if(this.closed)return;let{_parentOrParents:L,_ctorUnsubscribe:V,_unsubscribe:H,_subscriptions:Q}=this;if(this.closed=!0,this._parentOrParents=null,this._subscriptions=null,L instanceof W)L.remove(this);else if(null!==L)for(let Z=0;Z<L.length;++Z)L[Z].remove(this);if((0,ae.m)(H)){V&&(this._unsubscribe=void 0);try{H.call(this)}catch(Z){q=Z instanceof De?oe(Z.errors):[Z]}}if((0,I.k)(Q)){let Z=-1,X=Q.length;for(;++Z<X;){const G=Q[Z];if((0,b.K)(G))try{G.unsubscribe()}catch(Te){q=q||[],Te instanceof De?q=q.concat(oe(Te.errors)):q.push(Te)}}}if(q)throw new De(q)}add(q){let L=q;if(!q)return W.EMPTY;switch(typeof q){case"function":L=new W(q);case"object":if(L===this||L.closed||"function"!=typeof L.unsubscribe)return L;if(this.closed)return L.unsubscribe(),L;if(!(L instanceof W)){const Q=L;L=new W,L._subscriptions=[Q]}break;default:throw new Error("unrecognized teardown "+q+" added to Subscription.")}let{_parentOrParents:V}=L;if(null===V)L._parentOrParents=this;else if(V instanceof W){if(V===this)return L;L._parentOrParents=[V,this]}else{if(-1!==V.indexOf(this))return L;V.push(this)}const H=this._subscriptions;return null===H?this._subscriptions=[L]:H.push(L),L}remove(q){const L=this._subscriptions;if(L){const V=L.indexOf(q);-1!==V&&L.splice(V,1)}}}var fe;function oe(fe){return fe.reduce((q,L)=>q.concat(L instanceof De?L.errors:L),[])}W.EMPTY=((fe=new W).closed=!0,fe)},494:(Ye,ie,F)=>{F.d(ie,{v:()=>b});let I=!1;const b={Promise:void 0,set useDeprecatedSynchronousErrorHandling(ae){if(ae){const me=new Error;console.warn("DEPRECATED! RxJS was set to use deprecated synchronous error handling behavior by code at: \n"+me.stack)}else I&&console.log("RxJS: Back to a better error behavior. Thank you. <3");I=ae},get useDeprecatedSynchronousErrorHandling(){return I}}},345:(Ye,ie,F)=>{F.d(ie,{IY:()=>me,Ds:()=>W,ft:()=>fe});var I=F(393),b=F(574),ae=F(444);class me extends I.L{constructor(L){super(),this.parent=L}_next(L){this.parent.notifyNext(L)}_error(L){this.parent.notifyError(L),this.unsubscribe()}_complete(){this.parent.notifyComplete(),this.unsubscribe()}}class W extends I.L{notifyNext(L){this.destination.next(L)}notifyError(L){this.destination.error(L)}notifyComplete(){this.destination.complete()}}function fe(q,L){if(L.closed)return;if(q instanceof b.y)return q.subscribe(L);let V;try{V=(0,ae.s)(q)(L)}catch(H){L.error(H)}return V}},441:(Ye,ie,F)=>{F.d(ie,{c:()=>De,N:()=>W});var I=F(709),b=F(574),ae=F(319),me=F(307);class De extends b.y{constructor(V,H){super(),this.source=V,this.subjectFactory=H,this._refCount=0,this._isComplete=!1}_subscribe(V){return this.getSubject().subscribe(V)}getSubject(){const V=this._subject;return(!V||V.isStopped)&&(this._subject=this.subjectFactory()),this._subject}connect(){let V=this._connection;return V||(this._isComplete=!1,V=this._connection=new ae.w,V.add(this.source.subscribe(new oe(this.getSubject(),this))),V.closed&&(this._connection=null,V=ae.w.EMPTY)),V}refCount(){return(0,me.x)()(this)}}const W=(()=>{const L=De.prototype;return{operator:{value:null},_refCount:{value:0,writable:!0},_subject:{value:null,writable:!0},_connection:{value:null,writable:!0},_subscribe:{value:L._subscribe},_isComplete:{value:L._isComplete,writable:!0},getSubject:{value:L.getSubject},connect:{value:L.connect},refCount:{value:L.refCount}}})();class oe extends I.Yc{constructor(V,H){super(V),this.connectable=H}_error(V){this._unsubscribe(),super._error(V)}_complete(){this.connectable._isComplete=!0,this._unsubscribe(),super._complete()}_unsubscribe(){const V=this.connectable;if(V){this.connectable=null;const H=V._connection;V._refCount=0,V._subject=null,V._connection=null,H&&H.unsubscribe()}}}},402:(Ye,ie,F)=>{F.d(ie,{D:()=>X});var I=F(574),b=F(444),ae=F(319),me=F(554),oe=F(87),fe=F(377),V=F(72),H=F(489);function X(G,Te){return Te?function(G,Te){if(null!=G){if(function(G){return G&&"function"==typeof G[me.L]}(G))return function(G,Te){return new I.y(qe=>{const we=new ae.w;return we.add(Te.schedule(()=>{const te=G[me.L]();we.add(te.subscribe({next(Gt){we.add(Te.schedule(()=>qe.next(Gt)))},error(Gt){we.add(Te.schedule(()=>qe.error(Gt)))},complete(){we.add(Te.schedule(()=>qe.complete()))}}))})),we})}(G,Te);if((0,V.t)(G))return function(G,Te){return new I.y(qe=>{const we=new ae.w;return we.add(Te.schedule(()=>G.then(te=>{we.add(Te.schedule(()=>{qe.next(te),we.add(Te.schedule(()=>qe.complete()))}))},te=>{we.add(Te.schedule(()=>qe.error(te)))}))),we})}(G,Te);if((0,H.z)(G))return(0,oe.r)(G,Te);if(function(G){return G&&"function"==typeof G[fe.hZ]}(G)||"string"==typeof G)return function(G,Te){if(!G)throw new Error("Iterable cannot be null");return new I.y(qe=>{const we=new ae.w;let te;return we.add(()=>{te&&"function"==typeof te.return&&te.return()}),we.add(Te.schedule(()=>{te=G[fe.hZ](),we.add(Te.schedule(function(){if(qe.closed)return;let Gt,gn;try{const ft=te.next();Gt=ft.value,gn=ft.done}catch(ft){return void qe.error(ft)}gn?qe.complete():(qe.next(Gt),this.schedule())}))})),we})}(G,Te)}throw new TypeError((null!==G&&typeof G||G)+" is not observable")}(G,Te):G instanceof I.y?G:new I.y((0,b.s)(G))}},693:(Ye,ie,F)=>{F.d(ie,{n:()=>me});var I=F(574),b=F(15),ae=F(87);function me(De,W){return W?(0,ae.r)(De,W):new I.y((0,b.V)(De))}},2:(Ye,ie,F)=>{F.d(ie,{U:()=>b});var I=F(393);function b(De,W){return function(fe){if("function"!=typeof De)throw new TypeError("argument is not a function. Are you looking for `mapTo()`?");return fe.lift(new ae(De,W))}}class ae{constructor(W,oe){this.project=W,this.thisArg=oe}call(W,oe){return oe.subscribe(new me(W,this.project,this.thisArg))}}class me extends I.L{constructor(W,oe,fe){super(W),this.project=oe,this.count=0,this.thisArg=fe||this}_next(W){let oe;try{oe=this.project.call(this.thisArg,W,this.count++)}catch(fe){return void this.destination.error(fe)}this.destination.next(oe)}}},282:(Ye,ie,F)=>{F.d(ie,{J:()=>ae});var I=F(773),b=F(487);function ae(me=Number.POSITIVE_INFINITY){return(0,I.zg)(b.y,me)}},773:(Ye,ie,F)=>{F.d(ie,{zg:()=>me});var I=F(2),b=F(402),ae=F(345);function me(fe,q,L=Number.POSITIVE_INFINITY){return"function"==typeof q?V=>V.pipe(me((H,Q)=>(0,b.D)(fe(H,Q)).pipe((0,I.U)((Z,X)=>q(H,Z,Q,X))),L)):("number"==typeof q&&(L=q),V=>V.lift(new De(fe,L)))}class De{constructor(q,L=Number.POSITIVE_INFINITY){this.project=q,this.concurrent=L}call(q,L){return L.subscribe(new W(q,this.project,this.concurrent))}}class W extends ae.Ds{constructor(q,L,V=Number.POSITIVE_INFINITY){super(q),this.project=L,this.concurrent=V,this.hasCompleted=!1,this.buffer=[],this.active=0,this.index=0}_next(q){this.active<this.concurrent?this._tryNext(q):this.buffer.push(q)}_tryNext(q){let L;const V=this.index++;try{L=this.project(q,V)}catch(H){return void this.destination.error(H)}this.active++,this._innerSub(L)}_innerSub(q){const L=new ae.IY(this),V=this.destination;V.add(L);const H=(0,ae.ft)(q,L);H!==L&&V.add(H)}_complete(){this.hasCompleted=!0,0===this.active&&0===this.buffer.length&&this.destination.complete(),this.unsubscribe()}notifyNext(q){this.destination.next(q)}notifyComplete(){const q=this.buffer;this.active--,q.length>0?this._next(q.shift()):0===this.active&&this.hasCompleted&&this.destination.complete()}}},307:(Ye,ie,F)=>{F.d(ie,{x:()=>b});var I=F(393);function b(){return function(W){return W.lift(new ae(W))}}class ae{constructor(W){this.connectable=W}call(W,oe){const{connectable:fe}=this;fe._refCount++;const q=new me(W,fe),L=oe.subscribe(q);return q.closed||(q.connection=fe.connect()),L}}class me extends I.L{constructor(W,oe){super(W),this.connectable=oe}_unsubscribe(){const{connectable:W}=this;if(!W)return void(this.connection=null);this.connectable=null;const oe=W._refCount;if(oe<=0)return void(this.connection=null);if(W._refCount=oe-1,oe>1)return void(this.connection=null);const{connection:fe}=this,q=W._connection;this.connection=null,q&&(!fe||q===fe)&&q.unsubscribe()}}},87:(Ye,ie,F)=>{F.d(ie,{r:()=>ae});var I=F(574),b=F(319);function ae(me,De){return new I.y(W=>{const oe=new b.w;let fe=0;return oe.add(De.schedule(function(){fe!==me.length?(W.next(me[fe++]),W.closed||oe.add(this.schedule())):W.complete()})),oe})}},377:(Ye,ie,F)=>{F.d(ie,{hZ:()=>b});const b="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"},554:(Ye,ie,F)=>{F.d(ie,{L:()=>I});const I="function"==typeof Symbol&&Symbol.observable||"@@observable"},181:(Ye,ie,F)=>{F.d(ie,{b:()=>I});const I="function"==typeof Symbol?Symbol("rxSubscriber"):"@@rxSubscriber_"+Math.random()},971:(Ye,ie,F)=>{F.d(ie,{N:()=>b});const b=(()=>{function ae(){return Error.call(this),this.message="object unsubscribed",this.name="ObjectUnsubscribedError",this}return ae.prototype=Object.create(Error.prototype),ae})()},449:(Ye,ie,F)=>{function I(b){setTimeout(()=>{throw b},0)}F.d(ie,{z:()=>I})},487:(Ye,ie,F)=>{function I(b){return b}F.d(ie,{y:()=>I})},796:(Ye,ie,F)=>{F.d(ie,{k:()=>I});const I=Array.isArray||(b=>b&&"number"==typeof b.length)},489:(Ye,ie,F)=>{F.d(ie,{z:()=>I});const I=b=>b&&"number"==typeof b.length&&"function"!=typeof b},105:(Ye,ie,F)=>{function I(b){return"function"==typeof b}F.d(ie,{m:()=>I})},555:(Ye,ie,F)=>{function I(b){return null!==b&&"object"==typeof b}F.d(ie,{K:()=>I})},72:(Ye,ie,F)=>{function I(b){return!!b&&"function"!=typeof b.subscribe&&"function"==typeof b.then}F.d(ie,{t:()=>I})},869:(Ye,ie,F)=>{function I(b){return b&&"function"==typeof b.schedule}F.d(ie,{K:()=>I})},444:(Ye,ie,F)=>{F.d(ie,{s:()=>V});var I=F(15),b=F(449),me=F(377),W=F(554),fe=F(489),q=F(72),L=F(555);const V=H=>{if(H&&"function"==typeof H[W.L])return(H=>Q=>{const Z=H[W.L]();if("function"!=typeof Z.subscribe)throw new TypeError("Provided object does not correctly implement Symbol.observable");return Z.subscribe(Q)})(H);if((0,fe.z)(H))return(0,I.V)(H);if((0,q.t)(H))return(H=>Q=>(H.then(Z=>{Q.closed||(Q.next(Z),Q.complete())},Z=>Q.error(Z)).then(null,b.z),Q))(H);if(H&&"function"==typeof H[me.hZ])return(H=>Q=>{const Z=H[me.hZ]();for(;;){let X;try{X=Z.next()}catch(G){return Q.error(G),Q}if(X.done){Q.complete();break}if(Q.next(X.value),Q.closed)break}return"function"==typeof Z.return&&Q.add(()=>{Z.return&&Z.return()}),Q})(H);{const Z=`You provided ${(0,L.K)(H)?"an invalid object":`'${H}'`} where a stream was expected. You can provide an Observable, Promise, Array, or Iterable.`;throw new TypeError(Z)}}},15:(Ye,ie,F)=>{F.d(ie,{V:()=>I});const I=b=>ae=>{for(let me=0,De=b.length;me<De&&!ae.closed;me++)ae.next(b[me]);ae.complete()}}}]);