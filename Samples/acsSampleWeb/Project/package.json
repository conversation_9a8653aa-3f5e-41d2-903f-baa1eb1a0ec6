{"name": "ACSCallingSample", "version": "0.3.0", "private": true, "dependencies": {"@azure/communication-administration": "1.0.0-beta.4", "@azure/communication-calling": "1.2.0-beta.1", "@fluentui/react": "^7.158.1", "react": "^16.14.0", "react-dom": "^16.14.0"}, "scripts": {"start": "webpack-dev-server --port 80 --mode development", "build": "webpack --mode development"}, "devDependencies": {"@babel/core": "^7.8.7", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.7", "@babel/preset-react": "^7.8.3", "@babel/runtime": "^7.8.7", "babel-loader": "^8.0.6", "css-loader": "^5.2.6", "html-loader": "^0.5.5", "html-webpack-plugin": "^3.2.0", "style-loader": "^1.1.3", "webpack": "^4.42.0", "webpack-cli": "^3.3.11", "webpack-dev-server": "^3.11.2"}, "peerDependencies": {"@azure/logger": "^1.0.2"}}