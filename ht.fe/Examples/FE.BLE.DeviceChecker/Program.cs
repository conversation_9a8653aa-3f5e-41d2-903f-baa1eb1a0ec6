﻿using InTheHand.Bluetooth;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;

namespace FE.BLE.DeviceChecker
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Starting...Checking Bluetooth available");
            Console.WriteLine($"Bluetooth = {BLEDeviceManager.GetBluetoothAvailable().GetAwaiter().GetResult()} ");
            var htDevice = new HTDevice { BLENamePrefix = "Pulse Oximeter" };
            var devices = BLEDeviceManager.DiscoverDevice(htDevice).GetAwaiter().GetResult();
            foreach (var dev in devices)
            {
                Console.WriteLine($"dev={dev.Name} {dev.Id}");
            };
            if (devices?.Count>0)
            {
                htDevice.BTDevice = devices.First();
                
                var bm = new BLEDeviceManager(htDevice);
                bm.GetDeviceInfo().GetAwaiter().GetResult();

                Console.WriteLine();
                Console.WriteLine("Going to read the stream");
                //bm.ConnectToDevice().GetAwaiter().GetResult();
                //bm.ReadDeviceStream().GetAwaiter().GetResult();

            }

            Console.WriteLine("Done");
            Console.ReadLine();
        }

       
    }
}
