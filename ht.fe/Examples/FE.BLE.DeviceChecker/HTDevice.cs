﻿using InTheHand.Bluetooth;
using System;
using System.Collections.Generic;
using System.Text;

namespace FE.BLE.DeviceChecker
{
    public class HTDevice
    {
        public string Name { get; set; }
        public string Description { get; set; } 
        public string Type { get; set; }
        public string Id { get; set; }
        public string BLENamePrefix { get; set; }
        public string BLEId { get; set; }   
        public BluetoothDevice BTDevice { get; set; }

        public static string ByteArrayToString(byte[] data)
        {
            if (data == null)
                return "<NULL>";

            StringBuilder sb = new StringBuilder();

            foreach (byte b in data)
            {
                sb.Append(b.ToString("X"));
            }

            return sb.ToString();
        }
    }
}
