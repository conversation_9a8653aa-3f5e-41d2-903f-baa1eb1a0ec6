﻿using System;
using System.Collections.Generic;
using System.Text;

namespace FE.BLE.DeviceChecker.Classes
{
    /// <summary>
    /// Details of a Gatt Service
    // <seealso href="https://www.bluetooth.com/specifications/specs/?status=active&show_latest_version=0&show_latest_version=1&keyword=pulse&filter=" />
    /// </summary>
    public class GattService
    {
        public string DeviceName { get; set; }
        public string ServiceName { get; set; }
        public string ServiceId { get; set; }
        public ushort AssignedNumber { get;set;  }

        public string ServiceType { get; set; } 
    }
}
