﻿
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using System.Linq;
using System.Diagnostics;
using InTheHand.Net.Sockets;
using InTheHand.Net;
using System.IO;

namespace FE.BLE.DeviceChecker
{
    public class BLEDeviceManager
    {
        private HTDevice _device { get; set; }
        private BluetoothClient _btClient {get;set;}
        private ulong _macAddr { get; set; }
        private BluetoothAddress _btAddr { get; set; }
        
        public BLEDeviceManager(HTDevice device)    
        {
            this._device = device;
            //_macAddr = ulong.Parse(device.BTDevice.Id);
        }
        public BLEDeviceManager()
        {
            
        }

        public async Task<bool> ConnectToDevice()
        {
            var clnt = GetBTClient();
            if (!clnt.Connected)
            {
                clnt.Connect(_macAddr, InTheHand.Net.Bluetooth.BluetoothService.SerialPort);
            }
            return (clnt.Connected);
        }

        public async Task<string> ReadDeviceStream()
        {
            using (var sw = new StreamReader(_btClient.GetStream()))
            {
                var s = sw.ReadToEnd();
                Console.WriteLine($"we got: {s}");
                return (s);
            }
        }

        public async Task GetDeviceInfo(HTDevice device=null)
        {

            var dev = (device??_device).BTDevice;
            await dev.Gatt.ConnectAsync();
            var servs = await dev.Gatt.GetPrimaryServicesAsync();
            foreach (var serv in servs)
                {
                    var rssi = await dev.Gatt.ReadRssi();
                    Console.WriteLine($"{rssi} {serv.Uuid} Primary:{serv.IsPrimary}");
    
                    foreach (var chars in await serv.GetCharacteristicsAsync())
                    {
                        Console.WriteLine($"\t{chars.Uuid} UserDescription:{chars.UserDescription} Properties:{chars.Properties}");



                    foreach (var descriptors in await chars.GetDescriptorsAsync())
                    {
                        Console.WriteLine($"\t\tDescriptor:{descriptors.Uuid}");

                        var val2 = await descriptors.ReadValueAsync();

                        if (descriptors.Uuid == GattDescriptorUuids.ClientCharacteristicConfiguration)
                        {
                            Console.WriteLine($"\t\tNotifying:{val2[0] > 0}");
                        }
                        else if (descriptors.Uuid == GattDescriptorUuids.CharacteristicUserDescription)
                        {
                            Console.WriteLine($"\t\tUserDescription:{HTDevice.ByteArrayToString(val2)}");
                        }
                        else
                        {
                            Console.WriteLine("\t\t" + HTDevice.ByteArrayToString(val2));
                        }

                    }                        
                }
            }
        }

        public static async Task<bool> GetBluetoothAvailable()
        {
            return (await Bluetooth.GetAvailabilityAsync());
        }

        public static async Task<IReadOnlyCollection<BluetoothDevice>> DiscoverDevice(HTDevice dev)
        {
            var iCount = 5;
            var devices = new List<BluetoothDevice>();
            Console.WriteLine("Scanning for devices...");
            var req = new RequestDeviceOptions { AcceptAllDevices = true };
            
            while (true && iCount>0)
            {
                var discoveredDevices = await Bluetooth.ScanForDevicesAsync(req);
                devices = (from d in discoveredDevices
                           where d.Name.StartsWith(dev.BLENamePrefix)
                           select d).ToList();

                Console.WriteLine($"found {devices?.Count} devices");
                if (devices.Count > 0)
                    break;

                Thread.Sleep(1000);
                iCount--;
            }
            return (devices);
        }

        private BluetoothClient GetBTClient()
        {
            if (_btClient == null)
                _btClient = new BluetoothClient();


            return (_btClient);


        }
    }
}
