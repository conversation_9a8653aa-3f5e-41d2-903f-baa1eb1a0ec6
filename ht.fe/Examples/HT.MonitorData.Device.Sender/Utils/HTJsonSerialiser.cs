﻿using System.Text.Json;

namespace HT.MonitorData.Device.Sender;

public class HTJsonSerialiser
{
    public static JsonSerializerOptions opts = new JsonSerializerOptions
    {
        PropertyNameCaseInsensitive = true,
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
    };

    public static string Serialise(object o)
    {
        return JsonSerializer.Serialize(o, opts);
    }

    public static string Serialize(object o)
        => HTJsonSerialiser.Serialise(o);

    public static T Deserialise<T>(string o)
        => JsonSerializer.Deserialize<T>(o, opts);

    public static T Deserialize<T>(string o)
        => JsonSerializer.Deserialize<T>(o);

}
