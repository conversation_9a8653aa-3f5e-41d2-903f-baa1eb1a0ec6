﻿using bt.bff.common;
using bt.bff.common.Models.Security;
using Microsoft.Identity.Client;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Threading.Tasks;

namespace BFF.Secure.Client
{
    class Program
    {
        static authClient _secClient = null;
        static void Main(string[] args)
        {
            //Console.WriteLine("Logging in as a user securely...");
            //LoginWithUsernamePass().GetAwaiter().GetResult();

            Console.WriteLine("Making a call to the BFF Securely!");
            PerformASecureCallAgainstAADThenApp().GetAwaiter().GetResult();

            Console.WriteLine("Finished.");
            Console.ReadLine();
        }

        static async Task PerformASecureCallAgainstAADThenApp()
        {
            _secClient = authClient.ReadFromJsonFile("appsettings.json");

            //Call AAD GraphAPI to get a 'System' Token.
            Console.WriteLine("Acquiring token");
            var res = await _secClient.AcquireAccessToken(); //token values are transferred here internally in this class.
            Console.WriteLine("Tok:" + res?.AccessToken);

            //Use System Token to Login and get back App Token
            Console.WriteLine("\r\nCalling App Login to get App Token:");
            var client = await _secClient.GetHttpClientAsync(); 

            var rr = new RestRequest("/api/Client/Login");
            var rrResponse = await client.GetAsync<baseResponse>(rr);

            //Get and Use the App Returned App Token with additional app claims
            var appToken = rrResponse.Message;
            _secClient.AccessToken = appToken;
            client = await _secClient.GetHttpClientAsync("App"); //Custom Auth scheme used "Authorization: App <token>"

            rr = new RestRequest("/api/Client/SecureCall");
            rrResponse = await client.GetAsync<baseResponse>(rr);
            var resp = rrResponse;
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine(JsonConvert.SerializeObject(resp));
            Console.WriteLine(resp.Message);

            Console.ForegroundColor = ConsoleColor.White;
            Console.WriteLine("Calling Secure Call Through to Backend");

            Console.ForegroundColor = ConsoleColor.Green;
            rr = new RestRequest("/api/Client/SecureCallToBackend");
            resp = await client.GetAsync<baseResponse>(rr);
            Console.WriteLine(JsonConvert.SerializeObject(resp));
            Console.WriteLine(resp.Message);

            Console.ForegroundColor = ConsoleColor.White;

        }


        static async Task LoginWithUsernamePass()
        {
            var Scopes = new string[] { "User.Read", "User.ReadBasic.All" };

            var clientIdToLoginTo = "2d7b5160-c65f-465f-8e3e-730e3e59d62a"; //AAD test app registration.
            var app = PublicClientApplicationBuilder.Create(clientIdToLoginTo)
                .WithAuthority(AadAuthorityAudience.AzureAdMultipleOrgs)
                .Build();

            Console.WriteLine("Attempting to login");

            var user = "<INSERT USER>";
            var pass = "<INSERT PASS>";  //
            var secPass = new System.Security.SecureString();
            foreach (char c in pass) secPass.AppendChar(c);

            var myApp = new PublicAppUsingUsernamePassword(app);

            //var pb = await app.AcquireTokenInteractive(Scopes).ExecuteAsync();
            

            var res = await myApp.AcquireATokenFromCacheOrUsernamePasswordAsync(Scopes, user, secPass);


            Console.WriteLine("result=" + res.AccessToken);
            
            

        }
    }
}
