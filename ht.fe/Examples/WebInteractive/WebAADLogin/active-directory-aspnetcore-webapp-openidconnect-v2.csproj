<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>aspnet-active_directory_aspnetcore_webapp_openidconnect_v2-EBA77524-2042-46B7-839E-366CB0F19D8C</UserSecretsId>
    <RootNamespace>active_directory_aspnetcore_webapp_openidconnect_v2</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.0" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.3.0" />
    <PackageReference Include="Microsoft.Identity.Web.MicrosoftGraph" Version="3.3.0" />
    <PackageReference Include="Microsoft.Identity.Web.UI" Version="3.3.0" />
  </ItemGroup>

</Project>
