{
  "AzureAd": {
    "Instance": "https://login.microsoftonline.com/",
    "Domain": "healthteams.onmicrosoft.com",
    "ClientId": "2d7b5160-c65f-465f-8e3e-730e3e59d62a",
    "TenantId": "common", //"6b127268-dbaf-4ffc-a262-23fcb73619f9",
    "ClientSecret": "**********************************",
    "ClientCertificates": [
    ],
    "CallbackPath": "/signin-oidc"
  },
  "DownstreamApi": {
    /*
     'Scopes' contains space separated scopes of the Web API you want to call. This can be:
      - a scope for a V2 application (for instance api:b3682cc7-8b30-4bd2-aaba-080c6bf0fd31/access_as_user)
      - a scope corresponding to a V1 application (for instance <App ID URI>/.default, where  <App ID URI> is the
        App ID URI of a legacy v1 Web application
      Applications are registered in the https:portal.azure.com portal.
    */
    "BaseUrl": "https://graph.microsoft.com/v1.0",
    "Scopes": "user.read"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*"
}
