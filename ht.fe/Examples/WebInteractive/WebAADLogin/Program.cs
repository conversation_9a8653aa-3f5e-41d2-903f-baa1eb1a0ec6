using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;

namespace active_directory_aspnetcore_webapp_openidconnect_v2
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();

            System.Net.ServicePointManager.DefaultConnectionLimit = 400;
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                });

        
    }
}
