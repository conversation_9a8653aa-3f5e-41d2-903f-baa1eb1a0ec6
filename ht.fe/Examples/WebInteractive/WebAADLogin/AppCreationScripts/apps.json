{"Sample": {"Title": "Signing users to an ASP.NET Core Web App and call Microsoft Graph", "Level": 200, "Client": "ASP.NET Core 3.1"}, "AppRegistrations": [{"x-ms-id": "active-directory-aspnetcore-webapp-openidconnect-v2", "x-ms-name": "aspnetcore-webapp-openidconnect-v2", "x-ms-version": "2.0", "replyUrlsWithType": [{"url": "https://localhost:44321/signin-oidc", "type": "Web"}], "logoutUrl": "https://localhost:44321/signout-callback-oidc", "requiredResourceAccess": [{"x-ms-resourceAppName": "Microsoft Graph", "resourceAppId": "00000003-0000-0000-c000-000000000000", "resourceAccess": [{"id": "e1fe6dd8-ba31-4d61-89e7-88639da4683d", "type": "<PERSON><PERSON>", "x-ms-name": "user.read"}]}], "codeConfigurations": [{"settingFile": "/appsettings.json", "replaceTokens": {"appId": "Enter_the_Application_Id_here", "tenantId": "common", "authorityEndpointHost": "https://login.microsoftonline.com/", "msgraphEndpointHost": "https://graph.microsoft.com/", "clientSecret": "Enter_the_Client_Secret_Here"}}]}]}