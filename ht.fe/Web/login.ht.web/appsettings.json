{
/*
The following identity settings need to be configured
before the project can be successfully executed.
For more info see https://aka.ms/dotnet-template-ms-identity-platform 
*/
  "AzureAD": {
    "Instance": "https://htclients.b2clogin.com/",
    "Domain": "htclients.onmicrosoft.com",
    "TenantId": "3ff199f6-9552-495c-b9a9-d1624d30c884",
    "ClientId": "021466d9-3809-4032-87c9-f14c803d486e",
    "CallbackPath": "/signin-oidc",
    "SignUpSignInPolicyId": "B2C_1_ht_signin_signup"


  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*"
}
