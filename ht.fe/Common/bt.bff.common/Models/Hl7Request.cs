﻿using ht.data.common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace bt.bff.common.Models
{
    public class Hl7Request :baseRequest
    {
        public List<string> ExNoteIds { get; set; }
        public bool IsAllProgressNotes { get; set; }
        public string? ExResidentId { get; set; }
        public bool IsAmended { get; set; } = false;
        public Hl7Provider? ReferToProvider { get; set; }
        public Hl7Provider? CopyToProvider { get; set; }

    }
    public class Hl7Provider
    {
        public string FamilyNameLastNamePrefix { get; set; }
        public string GivenName { get; set; }
        public string MiddleInitialOrName { get; set; }
        public string Prefix { get; set; }
    }
}
