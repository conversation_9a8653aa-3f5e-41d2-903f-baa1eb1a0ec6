﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace bt.bff.common.Models
{
    public class Hl7Response:baseResponse
    {
        public List<Hl7Data> Hl7Data { get; set; }
        public HL7Acknowledgment HL7Acknowledgment { get; set; }
    }

    public class Hl7Data
    {
        public int ID { get; set; }
        public string MessageID { get; set; }
        public string SendingApplication { get; set; }
        public string SendingEDI { get; set; }
        public string ReceivingApplication { get; set; }
        public string ReceivingEDI { get; set; }
        public string MessageType { get; set; }
        public DateTime? MessageTimestamp { get; set; }
        public string MessageContent { get; set; }
        public string Status { get; set; }
        public string Acknowledgment { get; set; }
        public string ExResidentId { get; set; }
        public string ExFacilityId { get; set; }
    }
    public class HL7Acknowledgment
    {
        public int ID { get; set; }
        public string MessageID { get; set; }
        public string AcknowledgmentCode { get; set; }
        public string ErrorMessage { get; set; }
        public string AcknowledgmentMessage { get; set; }
        public DateTime? AcknowledgmentTimestamp { get; set; }
        public string SendingApplication { get; set; }
        public string SendingEDI { get; set; }
        public string ReceivingApplication { get; set; }
        public string ReceivingEDI { get; set; }
        public string Status { get; set; }
    }
}
