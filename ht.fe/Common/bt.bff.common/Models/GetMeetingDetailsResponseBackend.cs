﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace bt.bff.common.Models
{
    public class GetMeetingDetailsResponseBackend : baseResponse
    {
        public string MeetingId { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public DateTime MeetingDateUtc { get; set; }
        public List<Meetuser> MeetUsers { get; set; }
        

        public class Meetuser
        {
            public string DisplayName { get; set; }
            public string UserId { get; set; }
            public string Mobile { get; set; }
            public string Email { get; set; }
            public string Role { get; set; }
            public string AcsUserId { get; set; }
        }

    }

    public class GetMeetingDetailsResponse: GetMeetingDetailsResponseBackend
    {

    }
}
