﻿using Microsoft.Extensions.Configuration;
using Microsoft.Identity.Client;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace bt.bff.common.Models.Security
{
    public class authClient
    {

        public string Instance { get; set; } = "https://login.microsoftonline.com/{0}";
        public string TenantId { get; set; }
        public string ClientId { get; set; }
        public string Authority
        {
            get
            {
                return String.Format(CultureInfo.InvariantCulture,
                                     Instance, TenantId);
            }
        }
        public string ClientSecret { get; set; }
        public string BaseAddress { get; set; }
        public string ResourceID { get; set; }

        private string[] ResourceIds;

        private string ErrorMessage { get; set; }

        //Opertional values


        public IConfidentialClientApplication app { get; set; }
        public string AccessToken { get; set; }
        public string RefreshToken { get; set; }
        public DateTime? RefreshTimeUtc { get; set; }

        public async Task<AuthenticationResult> AcquireAccessToken()
        {
            if (app == null)
            {
                app = ConfidentialClientApplicationBuilder.Create(this.ClientId)
                   .WithClientSecret(this.ClientSecret)
                   .WithAuthority(new Uri(this.Authority))
                   .Build();

                ResourceIds = new string[] { this.ResourceID };
            }


            AuthenticationResult result = null;
            try
            {
                result = await app.AcquireTokenForClient(ResourceIds).ExecuteAsync();
                this.AccessToken = result.AccessToken;
                this.RefreshToken = result.AccessToken;
                this.RefreshTimeUtc = result.ExpiresOn.UtcDateTime;

                return (result);
            }
            catch (MsalClientException ex)
            {
                ErrorMessage = ex.Message;
                return (null);
            }
        }

        public static authClient ReadFromJsonFile(string path)
        {
            IConfiguration Configuration;

            var builder = new ConfigurationBuilder()
              .SetBasePath(Directory.GetCurrentDirectory())
              .AddJsonFile(path);

            Configuration = builder.Build();

            return Configuration.Get<authClient>();
        }

        //This passes the required parameters through to the backend APIs
        public async Task<RestClient> GetHttpClientAsync(Tuple<string, string> facAndToken)
        {
            string authScheme = "Bearer";
            var clnt = new RestClient(this.BaseAddress);
            clnt.AddDefaultHeader("Authorization", $"{authScheme} {this.AccessToken}");
            if (!string.IsNullOrEmpty(facAndToken.Item1))
                clnt.AddDefaultHeader("x-client-tok", facAndToken.Item1);
            if (!string.IsNullOrEmpty(facAndToken.Item2))
                clnt.AddDefaultHeader("x-exfacilityid", facAndToken.Item2);

            return (clnt);
        }
        public async Task<HttpClient> GetHttpClientFactoryAsync(Tuple<string, string> facAndToken, HttpClient client)
        {

            string authScheme = "Bearer";
            client.BaseAddress = new Uri(this.BaseAddress);
            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Authorization", $"{authScheme} {this.AccessToken}");
            if (!string.IsNullOrEmpty(facAndToken.Item1))
                client.DefaultRequestHeaders.Add("x-client-tok", facAndToken.Item1);
            if (!string.IsNullOrEmpty(facAndToken.Item2))
                client.DefaultRequestHeaders.Add("x-exfacilityid", facAndToken.Item2);

            return (client);
        }
        public async Task<HttpClient> GetHttpClientFactoryAsync(HttpClient client, string clientTok = "")
        {

            string authScheme = "Bearer";
            client.BaseAddress = new Uri(this.BaseAddress);
            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Authorization", $"{authScheme} {this.AccessToken}");
            if (!string.IsNullOrEmpty(clientTok))
                client.DefaultRequestHeaders.Add("x-client-tok", clientTok);

            return (client);
        }

        public async Task<RestClient> GetHttpClientAsync(string clientTok = "")
        {
            string authScheme = "Bearer";
            var clnt = new RestClient(this.BaseAddress);
            clnt.AddDefaultHeader("Authorization", $"{authScheme} {this.AccessToken}");
            if (!string.IsNullOrEmpty(clientTok))
                clnt.AddDefaultHeader("x-client-tok", clientTok);
            return (clnt);
        }

        public async Task<HttpClient> GetHttpClientNoAuthAsync()
        {
            HttpClient client =  new();
            client.BaseAddress = new Uri(this.BaseAddress);
            return client;
        }

        public async Task<HttpClient> GetHttpClientNoAuthFactoryAsync(HttpClient client)
        {
            client.BaseAddress = new Uri(this.BaseAddress);

            return client;
        }
    }

}
