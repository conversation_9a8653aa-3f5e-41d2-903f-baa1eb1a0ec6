﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace bt.bff.common.Models
{
    public class UpsertUserResponse : baseResponse
    {
        public string MeetingTitle { get; set; }
        public string MeetingDescription { get; set; }
        public string MeetingDateUtc { get; set; }

        public string DisplayName { get; set; }
        public string UserExId { get; set; }
        public string UserAcsId { get; set; }
        public string AcsToken { get; set; }
        public DateTime? AcsTokenExpiresUtc { get; set; }
        public string Role { get; set; }
   
    }

    public class UpsertUsersResponseBackend : baseResponse
    {
        public List<UpsertUserResponse> UpsertUserResponses { get; set; }
    }
}
