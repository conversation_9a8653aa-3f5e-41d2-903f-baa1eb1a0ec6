﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace bt.bff.common
{
    public class baseResponse
    {
        public string Status { get; set; }
        public string Message { get; set; }
        public DateTime? ResponseDateUtc { get; set; }
        public string ResponseId { get; set; }
        public long? TimeTaken
        {
            get
            {
                return _clock.ElapsedMilliseconds;
            }
        }

        public Dictionary<string, string>? Properties { set; get; }

        public List<string> Errors { get; set; }

        [JsonIgnore]
        private Stopwatch _clock { get; set; } = Stopwatch.StartNew();

        [JsonIgnore]
        public string? ApiRequest;
        [JsonIgnore]
        public string? ApiResponse;

        public baseResponse()
        {
            ResponseDateUtc = DateTime.UtcNow;
            ResponseId = Guid.NewGuid().ToString();
        }
        public baseResponse(string responseId)
        {
            ResponseDateUtc = DateTime.UtcNow;
            ResponseId = responseId;
        }
        public bool IsError()
        {
            return (Status == "Error");
        }

        public void FromException(Exception ex)
        {
            Message = ex.Message + "\r\n" + ex.StackTrace;
            if (ex.InnerException != null)
            {
                Errors = new List<string>();
                var iex = ex.InnerException;
                while (iex != null)
                {

                    Errors.Add(iex.Message);
                    iex = iex.InnerException;
                }
            }
            Status = "Error";
        }
    }
}
