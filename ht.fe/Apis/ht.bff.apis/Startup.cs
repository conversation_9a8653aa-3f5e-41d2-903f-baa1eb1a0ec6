using bt.bff.common;
using bt.bff.common.Models.Security;

using ht.bff.apis.Helpers;
using ht.bff.apis.Services;

using Microsoft.ApplicationInsights.DependencyCollector;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpsPolicy;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;

using Newtonsoft.Json;

using Polly;
using Polly.Extensions.Http;

using Swashbuckle.AspNetCore.Filters;

using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Security.Claims;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace ht.bff.apis;

/// <summary>
/// Testing a build trigger maybe
/// </summary>
public class Startup
{
    public string Apim_Uri = null;
    public Startup(IConfiguration configuration)
    {
        Configuration = configuration;
        foreach (var cfg in configuration.AsEnumerable())
        {
            Settings.Properties[cfg.Key] = cfg.Value;
        }
        Settings.HT_KV_URI = configuration["HT_KV_URI"];

        BlobHelper.acctName = configuration["IotStorageAccountName"];
        BlobHelper.acctKey = configuration["IotStorageAccountKey"];


        //Auth to the backend.
        Settings.BE_API_AuthClient = JsonConvert.DeserializeObject<authClient>(Settings.Properties["beapi-creds"]);
        Settings.BE_API_AuthClient?.AcquireAccessToken();

        Settings.BFF_API_Auth = JsonConvert.DeserializeObject<authServer>(Settings.Properties["bffapi-security"]);
        Settings.BFF_API_CustomAuth_SigningKey = configuration["JwtKey"]; ;// "bsdfsd4435dxZfgfdhf7867037f361a4d351e7c0de65f0776bfc2f478ea8d312c763bb6caca";// Settings.Properties["bffapi-customauth-signingkey"];
        Settings.BFF_Issuer= configuration["JwtIssuer"];
        Settings.BFF_API_Auth.AADResourceId = configuration["JwtAudience"];
        SignalRService._baseUrl = "https://apiuat.healthteams.com.au/events/v1"; //TODO: this needs to come from Config.

        EmailVerificationService.ApiKey = Settings.Properties["zb-email-apikey"];
        Apim_Uri = configuration["apim-uri"];

        if (Settings.Properties.ContainsKey("HT_BE_API_Local") && !String.IsNullOrEmpty(Settings.Properties["HT_BE_API_Local"]))
        {
            Settings.BE_API_AuthClient.BaseAddress = Settings.Properties["HT_BE_API_Local"];
        }
    }

    public IConfiguration Configuration { get; }

    // This method gets called by the runtime. Use this method to add services to the container.
    public void ConfigureServices(IServiceCollection services)
    {

        services.AddAuthentication(
            opt =>
            {
                opt.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                opt.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                opt.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;

            }).AddJwtBearer(opt =>
            {
                opt.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidIssuer = Settings.BFF_Issuer,
                    ValidAudience = Settings.BFF_API_Auth.AADResourceId,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(Settings.BFF_API_CustomAuth_SigningKey)),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                   ValidateIssuerSigningKey = true
                };
                //opt.Audience = "Audience1234"; // Settings.BFF_API_Auth.AADResourceId;
                //opt.Authority = $"{Settings.BFF_API_Auth.AADInstance}{Settings.BFF_API_Auth.AADTenantId}";
                opt.IncludeErrorDetails = true;

            });

        //services.AddAuthentication(CustomAuthenticationDefaults.AuthenticationScheme)
        //    .AddScheme<CustomAuthOptions, CustomAuthenticationHandler>(CustomAuthenticationDefaults.AuthenticationScheme,
        //    o => o.UserInfoEndpoint = "http://someUrl");

        /*
            .AddJwtBearer("API", opt =>
            {
                opt.Audience = "https://localhost:5001";
                opt.Authority = "https://localhost:5001/identity/";
                opt.IncludeErrorDetails = true;
            });
        */
        //services.AddAuthorization(opt =>
        //{
        //    var defAuthPolicyBuilder = new AuthorizationPolicyBuilder(
        //          JwtBearerDefaults.AuthenticationScheme, "APPJWT").RequireAuthenticatedUser();

        //    opt.DefaultPolicy = defAuthPolicyBuilder.Build();

        //});



        services.AddControllers()
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
                options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
                options.JsonSerializerOptions.Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
            });
        //updating swagger on the JWT requirements.
        services.AddSwaggerGen(c =>
        {
            var securityScheme = new OpenApiSecurityScheme
            {
                Name = "JWT Authentication",
                Description = "Enter JWT Bearer Token **_only_**",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.Http,
                Scheme = "bearer",
                Reference = new OpenApiReference
                {
                    Id = JwtBearerDefaults.AuthenticationScheme,
                    Type = ReferenceType.SecurityScheme
                }
            };

            c.AddSecurityDefinition(securityScheme.Reference.Id, securityScheme);
            c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {securityScheme,new string[] { } }
            });

            //c.AddServer(new OpenApiServer { Url = "https://apiuat.healthteams.com.au/fe/v1", Description = "Healthteams API gateway" });
            //This line below causes the swagger page to prefix all the requests with the APIM address - handy for testing.
            var str = "Healthteams API gateway";
            if (Apim_Uri?.ToLower()?.Contains("uat") == true)
                str = "Healthteams UAT API gateway";
            else if (Apim_Uri?.ToLower()?.Contains("uat") == true)
                str = "Healthteams DEMO API gateway";

            c.AddServer(new OpenApiServer { Url = Apim_Uri, Description = str });
            c.SwaggerDoc("v1", new OpenApiInfo { Title = "ht.bff.apis", Version = "v1" });

            var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            c.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename));

            c.ExampleFilters();
            c.OperationFilter<AddResponseHeadersFilter>();

            // Enable Swagger examples
            //c.OperationFilter<ExamplesOperationFilter>();

            // Enable swagger descriptions
            //c.OperationFilter<DescriptionOperationFilter>();

            // Enable swagger response headers
            //c.OperationFilter<AddResponseHeadersFilter>();

        });
        services.AddSwaggerExamplesFromAssemblies(Assembly.GetEntryAssembly());

        services.AddScoped<HTBEApiService>();
        services.AddScoped<HTBEBillsessionApiService>();
        services.AddScoped<HTBEDoctorApiService>();
        services.AddScoped<HTBEFacilityApiService>();
        services.AddScoped<HTBEFamilymemberApiService>();
        services.AddScoped<HTBEMonitoringdataApiService>();
        services.AddScoped<HTBENotesApiService>();
        services.AddScoped<HTBENurseApiService>();
        services.AddScoped<HTBEReferralApiService>();
        services.AddScoped<HTBEResidentApiService>();
        services.AddScoped<HTBEScriptsApiService>();
        services.AddScoped<HTBETaskApiService>();
        services.AddScoped<HTBETeleHealthApiService>();
        services.AddScoped<SignalRService>();
        services.AddHostedService<HostBETokenService>();
        services.AddScoped<EmailVerificationService>();
        services.AddScoped<HTBESurveyApiService>();
        services.AddScoped<Hl7Service>();
        services.AddScoped<BlobHelper>();
        services.AddScoped<HTBEMhrApiService>();

        services.AddApplicationInsightsTelemetry(Settings.Properties["appinsights-key"]);


        services.ConfigureTelemetryModule<DependencyTrackingTelemetryModule>((module, o) =>
        {
            module.EnableSqlCommandTextInstrumentation = true;
        });


        services.AddTransient<ht.bff.apis.Extensions.RequestBodyLoggingMiddleware>();
        services.AddTransient<ht.bff.apis.Extensions.ResponseBodyLoggingMiddleware>();

        services.AddHttpClient("ht-bff-client").AddPolicyHandler(GetRetryPolicy());

    }
    private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
    {
        return HttpPolicyExtensions
          // Handle HttpRequestExceptions, 408 and 5xx status codes
          .HandleTransientHttpError()
          // Handle 404 not found
          .OrResult(msg => msg.StatusCode == System.Net.HttpStatusCode.NotFound)
          // Handle 401 Unauthorized
          .OrResult(msg => msg.StatusCode == System.Net.HttpStatusCode.Unauthorized)
          // What to do if any of the above erros occur:
          // Retry 3 times, each time wait 1,2 and 4 seconds before retrying.
          .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
    }
    // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {

        JwtSecurityTokenHandler.DefaultInboundClaimTypeMap.Clear();


        app.UseDeveloperExceptionPage();
        app.UseSwagger();
        app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "ht.bff.apis v1"));


        //app.UseHttpsRedirection();

        app.UseRouting();

        app.UseCors(builder => builder
                .AllowAnyHeader()
                .AllowAnyOrigin()
                .AllowAnyMethod());

        app.UseAuthentication();
        app.UseAuthorization();

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
        });
    }
}
