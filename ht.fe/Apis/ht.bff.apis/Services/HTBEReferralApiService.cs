﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.data.common.Dashboards;
using ht.data.common.Telehealth;
using ht.data.common.Users;

using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace ht.bff.apis.Services
{
    public class HTBEReferralApiService
    {
        
    
        public async Task<GetReferralResponse> GetReferrals(HttpClient client, GetReferralRequest req)
        {
            //var rr = new RestRequest("/api/Referral/GetReferrals", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetReferralResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Referral/GetReferrals", req);
            var data = await resp.Content.ReadFromJsonAsync<GetReferralResponse>();
            return (data);
        }

        public async Task<GetReferralResponse> GetReferralDetails(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Referral/GetReferralDetails/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<GetReferralResponse>(rr);
            var resp = await client.GetFromJsonAsync<GetReferralResponse>($"/api/Referral/GetReferralDetails/{id}");
            return (resp);
        }

        public async Task<UpsertReferralsResponse> UpsertReferrals(HttpClient client, UpsertReferralsRequest req)
        {
            //var rr = new RestRequest("/api/Referral/UpsertReferrals", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<UpsertReferralsResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Referral/UpsertReferrals", req);
            var data = await resp.Content.ReadFromJsonAsync<UpsertReferralsResponse>();
            return (data);
        }

        public async Task<SendtoSpecialistResponse> SendtoSpecialist(HttpClient client, SendtoSpecialistRequest req)
        {
            //var rr = new RestRequest("/api/Referral/SendtoSpecialist", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<SendtoSpecialistResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Referral/SendtoSpecialist", req);
            var data = await resp.Content.ReadFromJsonAsync<SendtoSpecialistResponse>();
            return (data);
        }
        

    }
}
