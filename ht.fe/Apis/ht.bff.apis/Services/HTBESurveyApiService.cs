﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.data.common.Dashboards;
using ht.data.common.Telehealth;
using ht.data.common.Surveys;

using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Net.Http;
using ht.bff.apis.Examples;
using System.Net.Http.Json;

namespace ht.bff.apis.Services
{
    public class HTBESurveyApiService
    {
        
        public async Task<GetSurveyListResponse> GetSurveyListForFacility(HttpClient client, GetSurveyListRequest req)
        {
            //var rr = new RestRequest("/api/Survey/GetSurveyListForFacility", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetSurveyListResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Survey/GetSurveyListForFacility", req);
            var data = await resp.Content.ReadFromJsonAsync<GetSurveyListResponse>();
            return (data);
        }

         public async Task<GetSurveyResponse> GetSurvey(HttpClient client, GetSurveyRequest req)
        {
            //var rr = new RestRequest("/api/Survey/GetSurvey", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetSurveyResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Survey/GetSurvey", req);
            var data = await resp.Content.ReadFromJsonAsync<GetSurveyResponse>();
            return (data);
        }

        public async Task<UpsertSurveyResponse> UpsertSurveyForResident(HttpClient client, UpsertSurveyRequest req)
        {
            //var rr = new RestRequest("/api/Survey/UpsertSurveyForResident", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<UpsertSurveyResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Survey/UpsertSurveyForResident", req);
            var data = await resp.Content.ReadFromJsonAsync<UpsertSurveyResponse>();
            return (data);
        }
        public async Task<UpsertSurveyResponse> UpsertSurveyForFacility(HttpClient client, UpsertSurveyRequest req)
        {
            //var rr = new RestRequest("/api/Survey/UpsertSurveyForFacility", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<UpsertSurveyResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Survey/UpsertSurveyForFacility", req);
            var data = await resp.Content.ReadFromJsonAsync<UpsertSurveyResponse>();
            return (data);
        }
        public async Task<GetSurveyResultsListResponse> GetSurveyResultsListForResident(HttpClient client, GetSurveyListRequest req)
        {
            //var rr = new RestRequest("/api/Survey/GetSurveyResultsListForResident", Method.Post)
            //   .AddHeader("Content-Type", "application/json")
            //   .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetSurveyResultsListResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Survey/GetSurveyResultsListForResident", req);
            var data = await resp.Content.ReadFromJsonAsync<GetSurveyResultsListResponse>();
            return (data);
        }
        public async Task<GetSurveyResultsListResponse> GetSurveyResultsListForFacility(HttpClient client, GetSurveyListRequest req)
        {
            //var rr = new RestRequest("/api/Survey/GetSurveyResultsListForFacility", Method.Post)
            //   .AddHeader("Content-Type", "application/json")
            //   .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetSurveyResultsListResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Survey/GetSurveyResultsListForFacility", req);
            var data = await resp.Content.ReadFromJsonAsync<GetSurveyResultsListResponse>();
            return (data);
        }

        public async Task<GetSurveyResponse> GetSurveyResult(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Survey/GetSurveyResult/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<GetSurveyResponse>(rr);
            var resp = await client.GetFromJsonAsync<GetSurveyResponse>($"/api/Survey/GetSurveyResult/{id}");
            return (resp);
        }

        public async Task<GetQuestionListResponse> GetQuestionList(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Survey/GetQuestionList/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<GetQuestionListResponse>(rr);

            var resp = await client.GetFromJsonAsync<GetQuestionListResponse>($"/api/Survey/GetQuestionList/{id}");
            return (resp);
        }

        public async Task<GetAnswerListResponse> GetAnswerList(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Survey/GetAnswerList/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<GetAnswerListResponse>(rr);

            var resp = await client.GetFromJsonAsync<GetAnswerListResponse>($"/api/Survey/GetAnswerList/{id}");
            return (resp);
        }
        public async Task<UpsertQuestionResponse> UpsertQuestionForFacility(HttpClient client, UpsertQuestionRequest req)
        {
            //var rr = new RestRequest("/api/Survey/UpsertQuestionForFacility", Method.Post)
            //   .AddHeader("Content-Type", "application/json")
            //   .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<UpsertQuestionResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Survey/UpsertQuestionForFacility", req);
            var data = await resp.Content.ReadFromJsonAsync<UpsertQuestionResponse>();
            return (data);
        }
        public async Task<UpdateSurveyStatusResponse> UpdateSurveyStatus(HttpClient client, UpdateSurveyStatusRequest req)
        {
            //var rr = new RestRequest("/api/Survey/UpdateSurveyStatus", Method.Post)
            //   .AddHeader("Content-Type", "application/json")
            //   .AddJsonBody(req, "application/json");
            //var resp = await client.ExecuteAsync<UpdateSurveyStatusResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Survey/UpdateSurveyStatus", req);
            var data = await resp.Content.ReadFromJsonAsync<UpdateSurveyStatusResponse>();
            return (data);
        }

    }
}
