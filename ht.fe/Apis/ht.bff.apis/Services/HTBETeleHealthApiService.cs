﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.data.common.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Users;
using ht.data.common.Wounds;

using Microsoft.AspNetCore.Http;

using RestSharp;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace ht.bff.apis.Services
{
    public class HTBETeleHealthApiService
    {

        public async Task<GetTeleHealthDetailsResponse> GetTeleHealthDetails(HttpClient client, GetTeleHealthDetailsRequest req)
        {
            //var rr = new RestRequest("/api/TeleHealth/GetTeleHealthDetails", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetTeleHealthDetailsResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/TeleHealth/GetTeleHealthDetails", req);
            var data = await resp.Content.ReadFromJsonAsync<GetTeleHealthDetailsResponse>();
            return (data);
        }

        public async Task<ConvertTaskToMeetingResponse> ConvertTaskToMeeting(HttpClient client, ConvertTaskToMeetingRequest req)
        {
            //var rr = new RestRequest("/api/TeleHealth/ConvertTaskToMeeting", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<ConvertTaskToMeetingResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/TeleHealth/ConvertTaskToMeeting", req);
            var data = await resp.Content.ReadFromJsonAsync<ConvertTaskToMeetingResponse>();
            return (data);
        }

        public async Task<LaunchMeetingResponse> LaunchMeeting(HttpClient client, LaunchMeetingRequest req)
        {
            //var rr = new RestRequest("/api/TeleHealth/LaunchMeeting", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<LaunchMeetingResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/TeleHealth/LaunchMeeting", req);
            var data = await resp.Content.ReadFromJsonAsync<LaunchMeetingResponse>();
            return (data);
        }

        public async Task<GetMeetingIdResponse> GetMeetingId(HttpClient client, GetMeetingIdRequest req)
        {
            //var rr = new RestRequest("/api/TeleHealth/GetMeetingId", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetMeetingIdResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/TeleHealth/GetMeetingId", req);
            var data = await resp.Content.ReadFromJsonAsync<GetMeetingIdResponse>();
            return (data);
        }

        public async Task<GetResidentVitalGraphDataResponse> GetResidentVitalGraphData(HttpClient client, GetResidentVitalGraphDataRequest req)
        {
            //var rr = new RestRequest("/api/TeleHealth/GetResidentVitalGraphData", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetResidentVitalGraphDataResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/TeleHealth/GetResidentVitalGraphData", req);
            var data = await resp.Content.ReadFromJsonAsync<GetResidentVitalGraphDataResponse>();
            return (data);
        }

        public async Task<baseResponse> StartRecording(HttpClient client, LaunchMeetingRequest req)
        {
            //var rr = new RestRequest("/api/TeleHealth/StartRecording", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<baseResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/TeleHealth/StartRecording", req);
            var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
            return (data);
        }

        public async Task<baseResponse> PauseRecording(HttpClient client, LaunchMeetingRequest req)
        {
            //var rr = new RestRequest("/api/TeleHealth/PauseRecording", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<baseResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/TeleHealth/PauseRecording", req);
            var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
            return (data);
        }

        public async Task<baseResponse> ResumeRecording(HttpClient client, LaunchMeetingRequest req)
        {
            //var rr = new RestRequest("/api/TeleHealth/ResumeRecording", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<baseResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/TeleHealth/ResumeRecording", req);
            var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
            return (data);
        }

        public async Task<baseResponse> StopRecording(HttpClient client, LaunchMeetingRequest req)
        {
            //var rr = new RestRequest("/api/TeleHealth/StopRecording", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<baseResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/TeleHealth/StopRecording", req);
            var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
            return (data);
        }

        public async Task<baseResponse> GetRecordingState(HttpClient client, LaunchMeetingRequest req)
        {
            //var rr = new RestRequest("/api/TeleHealth/GetRecordingState", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<baseResponse>(rr, Method.Post);
            //return (resp?.Data);
            var resp = await client.PostAsJsonAsync("/api/TeleHealth/GetRecordingState", req);
            var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
            return (data);
        }

        public async Task<GetSelectionsResponse> GetSelectionsByType(HttpClient client, string id)
        {
            var resp = await client.GetFromJsonAsync<GetSelectionsResponse>($"/api/TeleHealth/GetSelectionsByType/{id}");

            return (resp);
        }

        public async Task<baseResponse> DeleteVitals(HttpClient client, DeleteVitalsRequest req)
        {
            //var rr = new RestRequest("/api/TeleHealth/DeleteVitals", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<baseResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/TeleHealth/DeleteVitals", req);
            var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
            return (data);
        }

        public async Task<GetWoundTreatmentPlanResponse> GetWoundTreatmentPlan(HttpClient client, GetWoundTreatmentPlanRequest req)
        {
            var resp = await client.PostAsJsonAsync("/api/TeleHealth/GetWoundTreatmentPlan", req);
            var data = await resp.Content.ReadFromJsonAsync<GetWoundTreatmentPlanResponse>();
            return (data);
        }

        public async Task<GetWoundTreatmentPlanResponse> GetFacilityWoundTreatmentPlan(HttpClient client, GetFacilityWoundTreatmentPlanRequest req)
        {
            var resp = await client.PostAsJsonAsync("/api/TeleHealth/GetFacilityWoundTreatmentPlan", req);
            var data = await resp.Content.ReadFromJsonAsync<GetWoundTreatmentPlanResponse>();
            return (data);
        }

        public async Task<GetWoundTreatmentPlanResponse> UpsertFacilityWoundTreatmentPlan(HttpClient client, UpsertFacilityWoundTreatmentPlanRequest req)
        {
            var resp = await client.PostAsJsonAsync("/api/TeleHealth/UpsertFacilityWoundTreatmentPlan", req);
            var data = await resp.Content.ReadFromJsonAsync<GetWoundTreatmentPlanResponse>();
            return (data);
        }

        public async Task<baseResponse> UpsertFacilityHSVRanges(HttpClient client, UpsertFacilityHSVRangesRequest req)
        {
            var resp = await client.PostAsJsonAsync("/api/TeleHealth/UpsertFacilityHSVRanges", req);
            var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
            return (data);
        }
        public async Task<InviteUserOnCallResponse> GetMeetingLink(HttpClient client, string id)
        {
            var resp = await client.GetFromJsonAsync<InviteUserOnCallResponse>($"/api/TeleHealth/GetMeetingLink/{id}");
            return (resp);
        }

        public async Task<List<WoundImage>> UploadMultipleFiles(HttpClient client, List<IFormFile> files)
        {
            var resp = await client.PostAsJsonAsync("/api/TeleHealth/UploadMultipleFiles", files);
            var data = await resp.Content.ReadFromJsonAsync<List<WoundImage>>();
            return (data);
        }

        public async Task<baseResponse> UpsertWoundAreaDetails(HttpClient client, UpsertWoundAreaDetailsRequest req)
        {

            var resp = await client.PostAsJsonAsync("/api/TeleHealth/UpsertWoundAreaDetails", req);
            var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
            return (data);
        }

        public async Task<baseResponse> DeleteWound(HttpClient client, string id)
        {
            var resp = await client.GetFromJsonAsync<baseResponse>($"/api/TeleHealth/DeleteWound/{id}");
            return (resp);
        }

        public async Task<GetFacilityHSVRangeResponse> GetFacilityHSVRanges(HttpClient client, string id)
        {
            var resp = await client.GetFromJsonAsync<GetFacilityHSVRangeResponse>($"/api/TeleHealth/GetFacilityHSVRanges/{id}");
            return (resp);
        }

    }
}
