﻿using bt.bff.common;
using bt.bff.common.Models;
using ht.bff.apis.Examples;
using ht.data.common.Dashboards;
using ht.data.common.FacilitySetup;
using ht.data.common.Telehealth;
using ht.data.common.Users;

using RestSharp;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace ht.bff.apis.Services
{
    public class HTBEFacilityApiService
    {
        public async Task<GetFacilityDashboardResponse> GetDashboard(HttpClient client, GetFacilityDashboardRequest req)
        {
            //var rr = new RestRequest("/api/Facility/GetDashboard", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetFacilityDashboardResponse>(rr, Method.Post);
            //return (resp?.Data);
            var resp = await client.PostAsJsonAsync("/api/Facility/GetDashboard", req);
            var data = await resp.Content.ReadFromJsonAsync<GetFacilityDashboardResponse>();
            return (data);
        }

        public async Task<UpsertFacilityResponse> UpsertFacility(HttpClient client, UpsertFacilityRequest req)
        {
            //var rr = new RestRequest("/api/Facility/UpsertFacility", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<UpsertFacilityResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Facility/UpsertFacility", req);
            var data = await resp.Content.ReadFromJsonAsync<UpsertFacilityResponse>();
            return (data);
        }

        public async Task<GetFacilitiesResponse> GetFacilities(HttpClient client, GetFacilitiesRequest req)
        {
            //var rr = new RestRequest("/api/Facility/GetFacilities", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetFacilitiesResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Facility/GetFacilities", req);
            var data = await resp.Content.ReadFromJsonAsync<GetFacilitiesResponse>();
            return (data);
        }

        public async Task<GetFacilityResponse> GetFacility(HttpClient client, GetFacilityRequest req)
        {
            //var rr = new RestRequest("/api/Facility/GetFacility", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetFacilityResponse>(rr, Method.Post);

            var resp = await client.PostAsJsonAsync("/api/Facility/GetFacility", req);
            var data = await resp.Content.ReadFromJsonAsync<GetFacilityResponse>();
            return (data);
        }

        public async Task<GetFacilityDevicesResponse> GetAvailableFacilityDevices(HttpClient client, GetFacilityDevicesRequest req)
        {
            //var rr = new RestRequest("/api/Facility/GetAvailableFacilityDevices", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetFacilityDevicesResponse>(rr, Method.Post);
            //return (resp?.Data);
            var resp = await client.PostAsJsonAsync("/api/Facility/GetAvailableFacilityDevices", req);
            var data = await resp.Content.ReadFromJsonAsync<GetFacilityDevicesResponse>();
            return (data);
        }

        public async Task<GetFacilityDevicesResponse> GetSelectedFacilityDevices(HttpClient client, GetFacilityDevicesRequest req)
        {
            //var rr = new RestRequest("/api/Facility/GetSelectedFacilityDevices", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetFacilityDevicesResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Facility/GetSelectedFacilityDevices", req);
            var data = await resp.Content.ReadFromJsonAsync<GetFacilityDevicesResponse>();
            return (data);
        }
        public async Task<baseResponse> UpsertFacilityDevices(HttpClient client, UpsertFacilityDevicesRequest req)
        {
            //var rr = new RestRequest("/api/Facility/UpsertFacilityDevices", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<baseResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Facility/UpsertFacilityDevices", req);
            var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
            return (data);
        }

        public async Task<GetFacilitiesResponse> GetParentFacilities(HttpClient client, GetFacilitiesRequest req)
        {
            //var rr = new RestRequest("/api/Facility/GetParentFacilities", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetFacilitiesResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Facility/GetParentFacilities", req);
            var data = await resp.Content.ReadFromJsonAsync<GetFacilitiesResponse>();
            return (data);
        }

        public async Task<GetPharmaciesResponse> GetPharmacies(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Facility/GetPharmacies/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<GetPharmaciesResponse>(rr);
            //return (resp);

            var resp = await client.GetFromJsonAsync<GetPharmaciesResponse>($"/api/Facility/GetPharmacies/{id}");
            return (resp);
        }
        public async Task<UpsertPharmacyResponse> UpsertPharmacy(HttpClient client, UpsertPharmacyRequest req)
        {
            //var rr = new RestRequest("/api/Facility/UpsertPharmacy", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<UpsertPharmacyResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Facility/UpsertPharmacy", req);
            var data = await resp.Content.ReadFromJsonAsync<UpsertPharmacyResponse>();
            return (data);
        }

        public async Task<GetUsersSearchBriefResponse> GetFacilityAdmins(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Facility/GetFacilityAdmins/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<GetUsersSearchBriefResponse>(rr);
            //return (resp);

            var resp = await client.GetFromJsonAsync<GetUsersSearchBriefResponse>($"/api/Facility/GetFacilityAdmins/{id}");
            return (resp);
        }

        public async Task<GetHTReportsResponse> GetReports(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Facility/GetReports/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<GetHTReportsResponse>(rr);
            //return (resp);

            var resp = await client.GetFromJsonAsync<GetHTReportsResponse>($"/api/Facility/GetReports/{id}");
            return (resp);
        }

        public async Task<baseResponse> UpsertAssignableFacilityDevice(HttpClient client, UpsertFacilityDevicesRequest req)
        {
            //var rr = new RestRequest("/api/Facility/UpsertAssignableFacilityDevice", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<baseResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Facility/UpsertAssignableFacilityDevice", req);
            var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
            return (data);
        }

        public async Task<UpsertCustomStylesResponse> UpsertFacilityCustomStyles(HttpClient client, UpsertCustomStylesRequest req)
        {
            //var rr = new RestRequest("/api/Facility/UpsertFacilityCustomStyles", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<UpsertCustomStylesResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Facility/UpsertFacilityCustomStyles", req);
            var data = await resp.Content.ReadFromJsonAsync<UpsertCustomStylesResponse>();
            return (data);
        }

        public async Task<GetFacilityCustomStyleResponse> GetFacilityCustomStyles(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Facility/GetFacilityCustomStyles/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<GetFacilityCustomStyleResponse>(rr);
            //
            var resp = await client.GetFromJsonAsync<GetFacilityCustomStyleResponse>($"/api/Facility/GetFacilityCustomStyles/{id}");
            return (resp);
        }

        public async Task<DiscoveryResponse> GetClientDiscoverForFacility(HttpClient client, string id)
        {
            var resp = await client.GetFromJsonAsync<DiscoveryResponse>($"/api/Facility/GetClientDiscoverForFacility/{id}");
            return (resp);
        }

        public async Task<GetFacilityDetailsResponse> GetLiveFacilitiesDetails(HttpClient client, string id)
        {
            var resp = await client.GetFromJsonAsync<GetFacilityDetailsResponse>($"/api/Facility/GetLiveFacilitiesDetails/{id}");
            return (resp);
        }
    }
}
