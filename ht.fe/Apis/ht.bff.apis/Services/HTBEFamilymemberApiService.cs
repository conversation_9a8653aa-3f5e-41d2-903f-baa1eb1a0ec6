﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.data.common.Dashboards;
using ht.data.common.Telehealth;
using ht.data.common.Users;

using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace ht.bff.apis.Services
{
    public class HTBEFamilymemberApiService
    {
        public async Task<GetFamilymemberDashboardResponse> GetDashboard(HttpClient client, GetFamilymemberDashboardRequest req)
        {
            //var rr = new RestRequest("/api/Familymember/GetDashboard", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetFamilymemberDashboardResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Familymember/GetDashboard", req);
            var data = await resp.Content.ReadFromJsonAsync<GetFamilymemberDashboardResponse>();
            return (data);
        }

        public async Task<GetUsersSearchBriefResponse> GetResidentsforFamily(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Familymember/GetResidentsforFamily/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.ExecuteAsync<GetUsersSearchBriefResponse>(rr, Method.Get);
            //
            var resp = await client.GetFromJsonAsync<GetUsersSearchBriefResponse>($"/api/Familymember/GetResidentsforFamily/{id}");
            return (resp);
        }

    }
}
