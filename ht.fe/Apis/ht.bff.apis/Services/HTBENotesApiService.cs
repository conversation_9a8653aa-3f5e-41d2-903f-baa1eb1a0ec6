﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.data.common.Dashboards;
using ht.data.common.Shared;
using ht.data.common.Telehealth;
using ht.data.common.Users;

using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace ht.bff.apis.Services
{


    public class HTBENotesApiService
    {

        public async Task<GetNotesResponse> GetNotes(HttpClient client, GetNotesRequest req)
        {

            //var rr = new RestRequest("/api/Notes/GetNotes", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");
            //var resp = await client.ExecuteAsync<GetNotesResponse>(rr, Method.Post);

            var resp = await client.PostAsJsonAsync("/api/Notes/GetNotes", req);
            var data = await resp.Content.ReadFromJsonAsync<GetNotesResponse>();
            return (data);
        }

        public async Task<GetNotesResponse> GetNotesDetails(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Notes/GetNotesDetails/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            var resp = await client.GetFromJsonAsync<GetNotesResponse>($"/api/Notes/GetNotesDetails/{id}");
            return (resp);
        }

        public async Task<UpsertNotesResponse> UpsertNotes(HttpClient client, UpsertNotesRequest req)
        {
            //var rr = new RestRequest("/api/Notes/UpsertNotes", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<UpsertNotesResponse>(rr, Method.Post);

            var resp = await client.PostAsJsonAsync("/api/Notes/UpsertNotes", req);
            var data = await resp.Content.ReadFromJsonAsync<UpsertNotesResponse>();
            return (data);
        }

        public async Task<byte[]> DownloadNote(HttpClient client, GetNotePdfRequest req)
        {
            var resp = await client.PostAsJsonAsync("/api/Notes/GetNotePdf", req);
            var content = await resp.Content.ReadAsByteArrayAsync();
            return (content);

            //var rr = new RestRequest($"/api/Notes/GetNotesDetails/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");


        }
    }
}
