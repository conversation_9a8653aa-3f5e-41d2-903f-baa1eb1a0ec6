﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.data.common.Telehealth;
using ht.data.common.Users;
using ht.data.common.Wounds;

using RestSharp;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace ht.bff.apis.Services
{
    public class HTBEMonitoringdataApiService
    {

        public async Task<UpsertMonitoringDataResponse> UpsertMonitoringData(HttpClient client, UpsertMonitoringDataRequest req)
        {
            //var rr = new RestRequest("/api/Monitoringdata/UpsertMonitoringData", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<UpsertMonitoringDataResponse>(rr, Method.Post);

            var resp = await client.PostAsJsonAsync("/api/Monitoringdata/UpsertMonitoringData", req);
            var data = await resp.Content.ReadFromJsonAsync<UpsertMonitoringDataResponse>();
            return (data);
        }

        public async Task<GetMonitoringDataListResponse> GetMonitoringDataList(HttpClient client, GetMonitoringDataListRequest req)
        {
            //var rr = new RestRequest($"/api/Monitoringdata/GetMonitoringDataList", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json"); ;

            //var resp = await client.ExecuteAsync<GetMonitoringDataListResponse>(rr, Method.Post);
            var resp = await client.PostAsJsonAsync("/api/Monitoringdata/GetMonitoringDataList", req);
            var data = await resp.Content.ReadFromJsonAsync<GetMonitoringDataListResponse>();
            return (data);
        }

        public async Task<GetMonitoringDataResponse> GetMonitoringData(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Monitoringdata/GetMonitoringData/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<GetMonitoringDataResponse>(rr);

            var resp = await client.GetFromJsonAsync<GetMonitoringDataResponse>($"/api/Monitoringdata/GetMonitoringData/{id}");

            return (resp);
        }

        public async Task<TelehealthMonitorData> GetUserLastVitals(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Monitoringdata/GetUserLastVitals/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<TelehealthMonitorData>(rr);

            var resp = await client.GetFromJsonAsync<TelehealthMonitorData>($"/api/Monitoringdata/GetUserLastVitals/{id}");
            return (resp);
        }
        /*
        public async Task<GetMonitoringDataListResponse> GetMonitoringDataList(RestClient client, GetMonitoringDataListRequest req)
        {
            var rr = new RestRequest($"/api/Monitoringdata/GetMonitoringDataList", Method.Post)
                .AddHeader("Content-Type", "application/json")
                .AddJsonBody(req, "application/json"); ;

            var resp = await client.ExecuteAsync<GetMonitoringDataListResponse>(rr, Method.Post);
            return (resp?.Data);
        }
        */

        public async Task<GetResidentThresholdsResponse> GetResidentThresholds(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Monitoringdata/GetResidentThresholds/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<GetResidentThresholdsResponse>(rr);

            var resp = await client.GetFromJsonAsync<GetResidentThresholdsResponse>($"/api/Monitoringdata/GetResidentThresholds/{id}");
            return (resp);
        }

        public async Task<GetWoundsResponse> GetResidentWounds(HttpClient client, GetWoundRequest req)
        {
            //var rr = new RestRequest("/api/Monitoringdata/GetResidentWounds", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetWoundsResponse>(rr, Method.Post);

            var resp = await client.PostAsJsonAsync("/api/Monitoringdata/GetResidentWounds", req);
            var data = await resp.Content.ReadFromJsonAsync<GetWoundsResponse>();
            return (data);
        }

        public async Task<GetWoundsResponse> GetWoundDetails(HttpClient client, GetWoundRequest req)
        {
            //var rr = new RestRequest("/api/Monitoringdata/GetWoundDetails", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetWoundsResponse>(rr, Method.Post);
            var resp = await client.PostAsJsonAsync("/api/Monitoringdata/GetWoundDetails", req);
            var data = await resp.Content.ReadFromJsonAsync<GetWoundsResponse>();
            return (data);
        }

        public async Task<UpsertWoundDetailsResponse> UpsertWoundDetails(HttpClient client, UpsertWoundDetailsRequest req)
        {
            //var rr = new RestRequest("/api/Monitoringdata/UpsertWoundDetails", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<UpsertWoundDetailsResponse>(rr, Method.Post);

            var resp = await client.PostAsJsonAsync("/api/Monitoringdata/UpsertWoundDetails", req);
            var data = await resp.Content.ReadFromJsonAsync<UpsertWoundDetailsResponse>();
            return (data);
        }

    }
}
