﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.data.common.Dashboards;
using ht.data.common.Tasks;
using ht.data.common.Telehealth;

using RestSharp;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace ht.bff.apis.Services
{
    public class HTBETaskApiService
    {
        public async Task<GetTaskSearchResponse> GetTasksList(HttpClient client, GetTaskListRequest req)
        {
            //var rr = new RestRequest("/api/Task/GetTasksList", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetTaskSearchResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Task/GetTasksList", req);
            var data = await resp.Content.ReadFromJsonAsync<GetTaskSearchResponse>();
            return (data);
        }


        public async Task<GetTaskSearchResponse> GetTask(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Task/GetTask/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<GetTaskSearchResponse>(rr);
            //return (resp);

            var resp = await client.GetFromJsonAsync<GetTaskSearchResponse>($"/api/Task/GetTask/{id}");
            return (resp);
        }

        public async Task<UpsertTaskResponse> UpsertTask(HttpClient client, UpsertTaskRequest req)
        {
            var resp = await client.PostAsJsonAsync("/api/Task/UpsertTask", req);
            var data = await resp.Content.ReadFromJsonAsync<UpsertTaskResponse>();
            return (data);
        }
        public async Task<GetAppointmentRequestSearchResponse> UpdateTasksStatusRequest(HttpClient client, UpsertAppointmentRequest req)
        {
            var resp = await client.PostAsJsonAsync("/api/Task/UpsertTask", req);
            var data = await resp.Content.ReadFromJsonAsync<GetAppointmentRequestSearchResponse>();
            return (data);
        }
        public async Task<GetTaskSearchResponse> GetTasksSearch(HttpClient client, GetTasksSearchRequest req)
        {
            //var rr = new RestRequest("/api/Task/GetTasksSearch", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetTaskSearchResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Task/GetTasksSearch", req);
            var data = await resp.Content.ReadFromJsonAsync<GetTaskSearchResponse>();
            return (data);
        }


        public async Task<GetVitalsListResponse> GetVitalsList(HttpClient client, GetVitalsListRequest req)
        {
            //var rr = new RestRequest("/api/Task/GetVitalsList", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetVitalsListResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Task/GetVitalsList", req);
            var data = await resp.Content.ReadFromJsonAsync<GetVitalsListResponse>();
            return (data);
        }

        public async Task<UpsertTaskResponse> DeleteTask(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Task/DeleteTask/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<UpsertTaskResponse>(rr);
            //return (resp);

            var resp = await client.GetFromJsonAsync<UpsertTaskResponse>($"/api/Task/DeleteTask/{id}");
            return (resp);
        }

        public async Task<GetTaskSearchResponse> GetUnassignedTasksList(HttpClient client, GetUnAssignedTaskListRequest req)
        {
            //var rr = new RestRequest("/api/Task/GetUnassignedTasksList", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetTaskSearchResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Task/GetUnassignedTasksList", req);
            var data = await resp.Content.ReadFromJsonAsync<GetTaskSearchResponse>();
            return (data);
        }

        public async Task<baseResponse> AssignTasks(HttpClient client, AssignTasksRequest req)
        {
            //var rr = new RestRequest("/api/Task/AssignTasks", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<baseResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Task/AssignTasks", req);
            var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
            return (data);
        }

        public async Task<baseResponse> UpdateTasksStatus(HttpClient client, UpdateTasksStatusRequest req)
        {
            var resp = await client.PostAsJsonAsync("/api/Task/UpdateTasksStatus", req);
            var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
            return (data);
        }
        public async Task<GetAppointmentRequestSearchResponse> UpsertAppointmentRequest(HttpClient client, UpsertAppointmentRequest req)
            {
                var resp = await client.PostAsJsonAsync("/api/Task/UpsertAppointmentRequest", req);
                var data = await resp.Content.ReadFromJsonAsync<GetAppointmentRequestSearchResponse>();
                return (data);
            }
        public async Task<GetAppointmentRequestSearchResponse> GetAppointmentRequestList(HttpClient client, GetAppointmentRequestSearchRequest req)
        {
            var resp = await client.PostAsJsonAsync("/api/Task/GetAppointmentRequestList", req);
            var data = await resp.Content.ReadFromJsonAsync<GetAppointmentRequestSearchResponse>();
            return (data);
        }


        public async Task<baseResponse> AcknowledgeAppointmentRequests(HttpClient client, AckAppointmentRequest req)
        {
            var resp = await client.PostAsJsonAsync("/api/Task/AcknowledgeAppointmentRequests", req);
            var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
            return (data);
        }
    }
}
