﻿using bt.bff.common;
using bt.bff.common.Models;
using ht.data.common.Telehealth;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace ht.bff.apis.Services
{
    public class Hl7Service
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="client"></param>
        /// <param name="req"></param>
        /// <returns>baseResponse</returns>
        public async Task<data.common.baseResponse> CreateHl7(HttpClient client, Hl7Request req)
        {

            var resp = await client.PostAsJsonAsync("/api/Hl7/CreateMessage", req);
            var content = await resp.Content.ReadFromJsonAsync<data.common.baseResponse>();
            return (content);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="client"></param>
        /// <param name="exFacilityId"></param>
        /// <returns>Hl7Data</returns>
        public async Task<List<Hl7Data>> GetHl7Messages(HttpClient client, string exFacilityId)
        {
            var resp = await client.GetFromJsonAsync<List<Hl7Data>>($"/api/Hl7/GetHl7Messages/{exFacilityId}");
            return (resp);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="client"></param>
        /// <param name="messageId"></param>
        /// <returns></returns>
        public async Task<HL7Acknowledgment> GetHl7Acknowledgment(HttpClient client, string messageId)
        {
           
            var resp = await client.GetFromJsonAsync<Hl7Response>($"/api/Hl7/GetHl7Acknowledgment/{messageId}");
            return (resp.HL7Acknowledgment);
        }
    }
}
