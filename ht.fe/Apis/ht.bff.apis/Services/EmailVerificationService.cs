﻿using ht.bff.apis.Utils;
using ht.data.common.EmailVerification;
using RestSharp;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using System.Linq;
using System.Net.Http;
using ht.data.common.Wounds;
using System.Net.Http.Json;

namespace ht.bff.apis.Services
{
    public class EmailVerificationService
    {
        private readonly IHttpClientFactory _clientFactory;
        private  HttpClient _client;
        public EmailVerificationService(IHttpClientFactory clientFactory )
        {
            this.clientFactory = clientFactory;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");
        }
        public static string ApiKey;

        public static string BaseUrl = "https://api.zerobounce.net";

        public static List<string> TestEmails = new List<string>
        {
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"

        };
        private readonly IHttpClientFactory clientFactory;

        public EmailVerificationService()
        {
            
        }

        public async Task<EmailVerificationResponse> VerifyEmail(string email)
        {
            EmailVerificationResponse br = new();

            /* - MICK: Removed 2022-06-27
            if (!email.StartsWith("689") && !TestEmails.Contains(email))
                throw new System.Exception("ERROR: Please use a test email of the following: " + string.Join(",", TestEmails));
            */
            var url = "/v2/validate?api_key=[key]&email=[email]".Replace("[key]", ApiKey).Replace("[email]", WebUtility.UrlEncode(email));
            //using (var clnt = new RestClient(BaseUrl))
            //{
            //    var rr = new RestRequest(url, Method.Get) {  Timeout = 200000};
            //    var res = await clnt.GetAsync(rr);
            //    br = HTJsonSerialiser.Deserialise<EmailVerificationResponse>(res.Content);
            //    br.Message = await this.GetCredits();

            //}
            _client.BaseAddress= new System.Uri(BaseUrl);
             br= await _client.GetFromJsonAsync<EmailVerificationResponse>(url);
             br.Message = await this.GetCredits();
             return (br);
        }


        public async Task<string> GetCredits()
        {
            var url = "/v2/getcredits?api_key=[key]";
            //using (var clnt = new RestClient(BaseUrl))
            //{
            //    var rr = new RestRequest(url, Method.Get) { Timeout = 200000 };
            //    var res = await clnt.GetAsync(rr);
            //    return (res?.Content);
            //}
            _client= _clientFactory.CreateClient("ht-bff-client");
            _client.BaseAddress = new System.Uri(BaseUrl);
            var resp = await _client.GetAsync(url);
            return await resp.Content.ReadAsStringAsync();
        }

    }
}
