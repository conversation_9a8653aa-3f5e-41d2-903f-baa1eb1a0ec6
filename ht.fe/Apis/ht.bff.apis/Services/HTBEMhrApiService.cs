﻿using bt.bff.common;
using bt.bff.common.Models;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace ht.bff.apis.Services
{
    public class HTBEMhrApiService
    {
        public async Task<MhrPortalResponse> GetMhrPortal(HttpClient client, MhrPortalRequest req)
        {
            var resp = await client.PostAsJsonAsync("/api/Mhr/GetMhrPortal", req);
            var data = await resp.Content.ReadFromJsonAsync<MhrPortalResponse>();
            return (data);
        }

        public async Task<baseResponse> UpsertHPIOCertificate(HttpClient client, UpsertHPIOCertificateRequest req)
        {
            var resp = await client.PostAsJsonAsync("/api/Mhr/UpsertHPIOCertificate", req);
            var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
            return (data);
        }

        public async Task<MhrSetupResponse> GetFacilityMHRSetup(HttpClient client, string ExFacilityId)
        {
            var resp = await client.GetFromJsonAsync<MhrSetupResponse>($"/api/Mhr/GetFacilityMHRSetup/{ExFacilityId}");
            return resp;
        }
    }

}
