﻿
using Microsoft.AspNetCore.Authentication;
using System.Security.Claims;
using System.Threading.Tasks;

namespace ht.bff.apis.Services;
public class ClaimsExtender : IClaimsTransformation
{
    //DI here to pass a link to DB helper.
    public ClaimsExtender()
    {

    }
    public Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal p)
    {
        
        var ident = (ClaimsIdentity)p?.Identity;
        ident.AddClaim(new Claim(ident.RoleClaimType, "Doctor"));
        
        return Task.FromResult(p);
    }
}
