﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.data.common.Dashboards;
using ht.data.common.Telehealth;
using ht.data.common.Users;

using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace ht.bff.apis.Services
{
    public class HTBENurseApiService
    {
        public async Task<GetNurseDashboardResponse> GetDashboard(HttpClient client, GetNurseDashboardRequest req)
        {
            //var rr = new RestRequest("/api/Nurse/GetDashboard", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetNurseDashboardResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Nurse/GetDashboard", req);
            var data = await resp.Content.ReadFromJsonAsync<GetNurseDashboardResponse>();
            return (data);
        }

    }
}
