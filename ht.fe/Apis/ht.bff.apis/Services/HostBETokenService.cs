﻿using bt.bff.common;
using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace ht.bff.apis.Services
{
    public class HostBETokenService : IHostedService, IDisposable
    {
        private Timer _timer;
        private const int _workFrequency = 45 * 60 * 1000; //45 mins
        private static object _lockObject = new ();


        public Task StartAsync(CancellationToken cancellationToken)
        {
            _timer = new Timer(DoWork, null, 0, _workFrequency);
            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _timer?.Change(Timeout.Infinite, 0);
            return Task.CompletedTask;
        }


        public void DoWork(object state)
        {
            //refresh the BE Token
            lock(_lockObject)
            {
                Settings.BE_API_AuthClient?.AcquireAccessToken();
            }
        }




        public void Dispose()
        {
            try { _timer?.Dispose(); } catch { }
        }
    }
}
