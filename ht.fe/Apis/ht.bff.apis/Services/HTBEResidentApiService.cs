﻿using bt.bff.common;
using bt.bff.common.Models;
using ht.bff.apis.Examples;
using ht.data.common.Dashboards;
using ht.data.common.partner;
using ht.data.common.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Users;
using ht.data.common.Vitals;

using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace ht.bff.apis.Services
{
    public class HTBEResidentApiService
    {
        public async Task<GetResidentDashboardResponse> GetDashboard(HttpClient client, GetResidentDashboardRequest req)
        {
            //var rr = new RestRequest("/api/Resident/GetDashboard", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetResidentDashboardResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Resident/GetDashboard", req);
            var data = await resp.Content.ReadFromJsonAsync<GetResidentDashboardResponse>();
            return (data);
        }
        public async Task<GetVitalsListResponse> GetVitalsListForResidentGraphs(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Vitals/GetVitalsListForResidentGraphs/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<GetVitalsListResponse>(rr);

            var resp = await client.GetFromJsonAsync<GetVitalsListResponse>($"/api/Vitals/GetVitalsListForResidentGraphs/{id}");

            return (resp);
        }

        public async Task<baseResponse> SendErrorLogDetails(HttpClient client, VitalsErrorLogDetailsRequest req)
        {
            //var rr = new RestRequest("/api/Vitals/SendErrorLogDetails", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<baseResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Vitals/SendErrorLogDetails", req);
            var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
            return (data);
        }

        public async Task<GetResidentPartnerEventDetailsResponse> GetResidentPartnerEventDetails(HttpClient client, GetResidentPartnerEventDetailsRequest req)
        {
            //var rr = new RestRequest("/api/Resident/GetResidentPartnerEventDetails", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetResidentPartnerEventDetailsResponse>(rr, Method.Post);
            //return (resp?.Data);
            var resp = await client.PostAsJsonAsync("/api/Resident/GetResidentPartnerEventDetails", req);
            var data = await resp.Content.ReadFromJsonAsync<GetResidentPartnerEventDetailsResponse>();
            return (data);
        }
        public async Task<GetPartnerIntegrationMappingDetailsResponse> GetResidentPartnerMappingDetails(HttpClient client, GetPartnerIntegrationMappingDetailsRequest req)
        {
            //var rr = new RestRequest("/api/Resident/GetResidentPartnerMappingDetails", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetPartnerIntegrationMappingDetailsResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Resident/GetResidentPartnerMappingDetails", req);
            var data = await resp.Content.ReadFromJsonAsync<GetPartnerIntegrationMappingDetailsResponse>();
            return (data);
        }
        public async Task<GetUsersSearchResponse> GetResidentByFacilityId(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Vitals/GetVitalsListForResidentGraphs/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<GetVitalsListResponse>(rr);
            //client.BaseAddress = new Uri("http://localhost:5000");
            var resp = await client.GetFromJsonAsync<GetUsersSearchResponse>($"/api/Resident/GetResidentOffline/{id}");

            return (resp);
        }

        public async Task<DiscoveryResponse> GetClientDiscoverForResident(HttpClient client, string id)
        {

            var resp = await client.GetFromJsonAsync<DiscoveryResponse>($"/api/Resident/GetClientDiscoverForResident/{id}");
            return (resp);
        }

        public async Task<InviteUserOnCallResponse> GetResidentDirectLink(HttpClient client, string id)
        {

            var resp = await client.GetFromJsonAsync<InviteUserOnCallResponse>($"/api/Resident/GetResidentDirectLink/{id}");
            return (resp);
        }

    }

}
