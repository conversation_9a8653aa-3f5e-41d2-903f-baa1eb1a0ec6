﻿using RestSharp;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace ht.bff.apis.Services
{
    public class SignalRService
    {
        public static string _baseUrl;
        private readonly IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;
        public SignalRService(IHttpClientFactory clientFactory)
        {
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");
        }

        public async Task<string> GetAccessToken(string userExId)
        {
            using (var clnt = await GetClient())
            {
                var rr = new RestRequest("/negotiate")
                    .AddHeader("x-ht-userid", userExId)
                    .AddJsonBody(string.Empty);

                var resp = await clnt.PostAsync(rr);
                return resp.Content;
            }

            //try
            //{
            //    var client = await GetHttpClient();
            //    client.DefaultRequestHeaders.Add("x-ht-userid", userExId);
            //    var content = new StringContent("", Encoding.UTF8, "application/json");
            //    var response = await client.PostAsJsonAsync("/negotiate", content);
            //    return await response.Content.ReadAsStringAsync();
            //}
            //catch (System.Exception ex)
            //{

            //    throw;
            //}


        }


        public async Task<RestClient> GetClient()
        {
            return new RestClient(_baseUrl);
        }

        public async Task<HttpClient> GetHttpClient()
        {
            _client.BaseAddress = new System.Uri(_baseUrl);
            return _client;
        }
    }
}
