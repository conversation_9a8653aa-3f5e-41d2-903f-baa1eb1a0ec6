﻿using bt.bff.common;
using bt.bff.common.Models;
using ht.data.common.Telehealth;
using ht.data.common.Billing;

using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Net.Http;
using System.Net.Http.Json;
using ht.bff.apis.Examples;

namespace ht.bff.apis.Services
{
    public class HTBEBillsessionApiService
    {  
        public async Task<UpsertBillSessionResponse> UpsertBillSession(HttpClient client, UpsertBillSessionRequest req)
        {
            //var rr = new RestRequest("/api/BillSession/UpsertBillSession", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<UpsertBillSessionResponse>(rr, Method.Post);

            var resp = await client.PostAsJsonAsync("/api/BillSession/UpsertBillSession", req);
            var data = await resp.Content.ReadFromJsonAsync<UpsertBillSessionResponse>();
            return (data);
        }

        public async Task<GetBillSessionResponse> GetBillsession(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/BillSession/GetBillSession/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");
            //var resp = await client.GetAsync<GetBillSessionResponse>(rr);

            var resp = await client.GetFromJsonAsync<GetBillSessionResponse>($"/api/BillSession/GetBillSession/{id}");

            return (resp);
        }

        public async Task<GetBillSessionResponse> GetBillsessionDetails(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/BillSession/GetBillsessionDetails/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<GetBillSessionResponse>(rr);

            var resp = await client.GetFromJsonAsync<GetBillSessionResponse>($"/api/BillSession/GetBillsessionDetails/{id}");

            return (resp);
        }

        public async Task<MedicareVerificationResponse> VerifyMedicare(HttpClient client, MedicareVerificationRequest req)
        {
            //var rr = new RestRequest("/api/BillSession/VerifyMedicare", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<MedicareVerificationResponse>(rr, Method.Post);

            var resp = await client.PostAsJsonAsync("/api/BillSession/VerifyMedicare", req);
            var data = await resp.Content.ReadFromJsonAsync<MedicareVerificationResponse>();
            return (data);
        }


    }
}
