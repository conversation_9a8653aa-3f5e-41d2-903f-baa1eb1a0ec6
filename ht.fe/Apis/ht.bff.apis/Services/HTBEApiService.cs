﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.data.common.Users;
using ht.data.common.Telehealth;
using ht.data.common.Vitals;

using RestSharp;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ht.bff.apis.Examples;
using ht.data.common.Reports;
using ht.data.common.Shared;
using System.Net.Http;
using System.Text.Json;
using System.Net.Http.Json;
using ht.data.common.EmailVerification;

namespace ht.bff.apis.Services;

public class HTBEApiService
{

    public async Task<ht.data.common.baseResponse> UploadUserProfilePic(HttpClient client, byte[] bytes, JwtUserAndRoles usr, string ftype, string fname)
    {
        ht.data.common.baseResponse br = new();
        try
        {
            var url = $"/api/User/UpdateProfilePic";

            var formData = new MultipartFormDataContent();
            formData.Add(new ByteArrayContent(bytes), "File", "profilepic.png");
            formData.Add(new StringContent(ftype), "ftype");
            formData.Add(new StringContent(fname), "fname");
            formData.Add(new StringContent(usr.UserExId), "userid");

            var resp = await client.PostAsync(url, formData);
            var respContent = await resp.Content.ReadFromJsonAsync<ht.data.common.baseResponse>();
            br = respContent;// JsonSerializer.Deserialize<ht.data.common.baseResponse>(respContent);
            br.Status = "Success";
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (br);
    }

    public async Task<baseResponse> MakeSecureCall(HttpClient client)
    {

        //var rr = new RestRequest("/api/Client/SecureCall", Method.Get);

        //var resp = await client.GetAsync<baseResponse>(rr);

        var resp = await client.GetFromJsonAsync<baseResponse>($"/api/Client/SecureCall");

        return (resp);

    }

    public async Task<DiscoveryResponse> GetClientDiscover(HttpClient client, DiscoveryRequest req)
    {

        //var rr = new RestRequest("/api/Client/Discovery", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddBody(req);
        //var resp = await clnt.ExecuteAsync<DiscoveryResponse>(rr, Method.Post);
        var resp = await client.PostAsJsonAsync("/api/Client/Discovery", req);
        var data = await resp.Content.ReadFromJsonAsync<DiscoveryResponse>();
        return (data);
    }

    public async Task<Tuple<GetVitalsInformationResponse, string>> GetClientInformation(HttpClient client)
    {

        var rr = new RestRequest("/api/Vitals/GetVitalsInformation", Method.Get);
        //var resp = await client.GetAsync<GetVitalsInformationResponse>(rr);
        //        rr = new RestRequest("/api/Telehealth/GetAcsId");

        //var resp2 = await client.GetAsync<baseResponse>(rr);

        var resp = await client.GetFromJsonAsync<GetVitalsInformationResponse>($"/api/Vitals/GetVitalsInformation");
        var resp2 = await client.GetFromJsonAsync<baseResponse>($"/api/Telehealth/GetAcsId");

        var acsId = resp2?.Message;

        var t = new Tuple<GetVitalsInformationResponse, string>(resp, acsId);

        return (t);

    }

    public async Task<GetVitalsInformationResponse> GetVitalsInformation(RestClient client)
    {

        var rr = new RestRequest("/api/Vitals/GetVitalsInformation", Method.Get);

        var resp = await client.GetAsync<GetVitalsInformationResponse>(rr);
        return (resp);

    }

    public async Task<GetReportResponse> GetReport(RestClient client, GetReportRequest req)
    {
        var rr = new RestRequest("/api/Report/GetReport", Method.Post)
            .AddHeader("Content-Type", "application/json")
            .AddJsonBody(req, "application/json");

        var resp = await client.ExecuteAsync<GetReportResponse>(rr, Method.Post);
        return (resp?.Data);
    }
    public async Task<SharedListResponse> GetReportList(HttpClient client, data.common.baseRequest req)
    {
        //var rr = new RestRequest("/api/Report/GetReportList", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<SharedListResponse>(rr, Method.Post);

        var resp = await client.PostAsJsonAsync("/api/Report/GetReportList", req);
        var data = await resp.Content.ReadFromJsonAsync<SharedListResponse>();
        return (data);
    }

    public async Task<AskForAccessResponse> AskForAccessRequest(HttpClient client, AskForAccessRequest req)
    {
        //var rr = new RestRequest("/api/User/AskForAccess", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<AskForAccessResponse>(rr, Method.Post);
        var resp = await client.PostAsJsonAsync("/api/User/AskForAccess", req);
        var data = await resp.Content.ReadFromJsonAsync<AskForAccessResponse>();
        return (data);
    }

    public async Task<ChangeUserPasswordResponse> ChangePassword(HttpClient client, ChangeUserPasswordRequest req)
    {
        var resp = await client.PostAsJsonAsync("/api/User/ChangePassword", req);
        var data = await resp.Content.ReadFromJsonAsync<ChangeUserPasswordResponse>();
        return (data);

    }

    public async Task<InviteUserResponse> ResetUserPassword(HttpClient client, ChangeUserPasswordRequest req)
    {
        var resp = await client.PostAsJsonAsync("/api/User/ResetUserPassword", req);
        var data = await resp.Content.ReadFromJsonAsync<InviteUserResponse>();
        return (data);

    }
    public async Task<ResetUserPasswordResponse> ResetPassword(HttpClient client, ResetUserPasswordRequest req)
    {
        var resp = await client.PostAsJsonAsync("/api/User/ResetPassword", req);
        var data = await resp.Content.ReadFromJsonAsync<ResetUserPasswordResponse>();
        return (data);

    }

    public async Task<UpsertUsersResponseBackend> UpsertUsersRequest(HttpClient client, UpsertUsersRequestBackend req)
    {
        //var rr = new RestRequest("/api/Client/UpsertUsers", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<UpsertUsersResponseBackend>(rr, Method.Post);

        var resp = await client.PostAsJsonAsync("/api/Client/UpsertUsers", req);
        var data = await resp.Content.ReadFromJsonAsync<UpsertUsersResponseBackend>();
        return (data);
    }

    public async Task<GetMeetingDetailsResponseBackend> GetMeetingDetails(HttpClient client, string id)
    {
        //var rr = new RestRequest($"/api/Client/GetMeetingDetails/{id}", Method.Get)
        //    .AddHeader("Content-Type", "application/json");

        //var resp = await client.GetAsync<GetMeetingDetailsResponseBackend>(rr);

        var resp = await client.GetFromJsonAsync<GetMeetingDetailsResponseBackend>($"/api/Client/GetMeetingDetails/{id}");
        return (resp);
    }

    public async Task<GetUserDetailsResponse> GetUserDetails(HttpClient client, string id)
    {
        //var rr = new RestRequest($"/api/User/GetUserDetails/{id}", Method.Get)
        //    .AddHeader("Content-Type", "application/json");

        //var resp = await client.ExecuteAsync<GetUserDetailsResponse>(rr, Method.Get);

        var resp = await client.GetFromJsonAsync<GetUserDetailsResponse>($"/api/User/GetUserDetails/{id}");
        return (resp);
    }

    public async Task<GetUsersSearchBriefResponse> GetUsersSearchBrief(HttpClient client, GetUsersSearchRequest req)
    {
        //var rr = new RestRequest("/api/User/GetUsersSearchBrief", Method.Post)
        //   .AddHeader("Content-Type", "application/json")
        //   .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<GetUsersSearchBriefResponse>(rr, Method.Post);
        var resp = await client.PostAsJsonAsync("/api/User/GetUsersSearchBrief", req);
        var data = await resp.Content.ReadFromJsonAsync<GetUsersSearchBriefResponse>();
        return (data);
    }

    public async Task<GetUsersSearchResponse> GetUsersSearch(HttpClient client, GetUsersSearchRequest req)
    {
        //var rr = new RestRequest("/api/User/GetUsersSearch", Method.Post)
        //   .AddHeader("Content-Type", "application/json")
        //   .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<GetUsersSearchResponse>(rr, Method.Post);
        var resp = await client.PostAsJsonAsync("/api/User/GetUsersSearch", req);
        var data = await resp.Content.ReadFromJsonAsync<GetUsersSearchResponse>();
        return (data);
    }

    public async Task<GetResidentResponse> GetResident(HttpClient client, GetResidentRequest req)
    {
        //var rr = new RestRequest("/api/User/GetResident", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<GetResidentResponse>(rr, Method.Post);
        var resp = await client.PostAsJsonAsync("/api/User/GetResident", req);
        var data = await resp.Content.ReadFromJsonAsync<GetResidentResponse>();
        return (data);
    }

    public async Task<GetFacilitiesResponse> GetFacilities(HttpClient client, GetFacilitiesRequest req)
    {
        //var rr = new RestRequest("/api/User/GetFacilities", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<GetFacilitiesResponse>(rr, Method.Post);
        var resp = await client.PostAsJsonAsync("/api/User/GetFacilities", req);
        var data = await resp.Content.ReadFromJsonAsync<GetFacilitiesResponse>();
        return (data);
    }

    public async Task<InvitedUsersResponse> GetInvitedUsers(HttpClient client, string id)
    {
        //var rr = new RestRequest($"/api/Client/GetInvitedUsers/{id}", Method.Get)
        //    .AddHeader("Content-Type", "application/json");

        //var resp = await client.GetAsync<InvitedUsersResponse>(rr);

        var resp = await client.GetFromJsonAsync<InvitedUsersResponse>($"/api/Client/GetInvitedUsers/{id}");

        return (resp);
    }

    public async Task<UpsertUserProfileResponse> UpsertUserProfile(HttpClient client, HealthTeamsUser req)
    {
        //var rr = new RestRequest("/api/Client/UpsertUserProfile", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<UpsertUserProfileResponse>(rr, Method.Post);


        var resp = await client.PostAsJsonAsync("/api/Client/UpsertUserProfile", req);
        var data = await resp.Content.ReadFromJsonAsync<UpsertUserProfileResponse>();
        return (data);
    }

    public async Task<GetUserProfileResponse> GetUserProfileByExternalId(HttpClient client, string id)
    {
        //var rr = new RestRequest($"/api/Client/GetUserProfileByExternalId/{id}", Method.Get)
        //    .AddHeader("Content-Type", "application/json");

        //var resp = await client.GetAsync<GetUserProfileResponse>(rr);

        var resp = await client.GetFromJsonAsync<GetUserProfileResponse>($"/api/Client/GetUserProfileByExternalId/{id}");

        return (resp);
    }

    public async Task<GetResidentsSearchResponse> GetResidentsSearch(HttpClient client, GetResidentsSearchRequest req)
    {
        //var rr = new RestRequest("/api/User/GetResidentsSearch", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<GetResidentsSearchResponse>(rr, Method.Post);
        var resp = await client.PostAsJsonAsync("/api/User/GetResidentsSearch", req);
        var data = await resp.Content.ReadFromJsonAsync<GetResidentsSearchResponse>();
        return (data);
    }

    public async Task<GetCountriesResponse> GetCountries(HttpClient client)
    {
        //var rr = new RestRequest($"/api/User/GetCountries", Method.Get)
        //    .AddHeader("Content-Type", "application/json");

        //var resp = await client.GetAsync<GetCountriesResponse>(rr);

        var resp = await client.GetFromJsonAsync<GetCountriesResponse>($"/api/User/GetCountries");

        return (resp);
    }

    public async Task<DisconnectFacilityResponse> DisconnectFacility(HttpClient client, DisconnectFacilityRequest req)
    {
        //var rr = new RestRequest("/api/User/DisconnectFacility", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<DisconnectFacilityResponse>(rr, Method.Post);
        var resp = await client.PostAsJsonAsync("/api/User/DisconnectFacility", req);
        var data = await resp.Content.ReadFromJsonAsync<DisconnectFacilityResponse>();
        return (data);
    }

    public async Task<ConnectedFacilitiesResponse> GetUserConnectedFacilities(HttpClient client, string id)
    {
        //var rr = new RestRequest($"/api/User/GetUserConnectedFacilities/{id}", Method.Get)
        //    .AddHeader("Content-Type", "application/json");

        //var resp = await client.ExecuteAsync<ConnectedFacilitiesResponse>(rr, Method.Get);
        var resp = await client.GetFromJsonAsync<ConnectedFacilitiesResponse>($"/api/User/GetUserConnectedFacilities/{id}");

        return (resp);
    }

    public async Task<GetNewFacilityRequestsResponse> GetNewFacilityRequests(HttpClient client, string id)
    {
        //var rr = new RestRequest($"/api/User/GetNewFacilityRequests/{id}", Method.Get)
        //    .AddHeader("Content-Type", "application/json");

        //var resp = await client.ExecuteAsync<GetNewFacilityRequestsResponse>(rr, Method.Get);

        var resp = await client.GetFromJsonAsync<GetNewFacilityRequestsResponse>($"/api/User/GetNewFacilityRequests/{id}");

        return (resp);
    }

    public async Task<GetFacilityConfirmResponse> ConfirmFacilityRequest(HttpClient client, GetFacilityConnectRequests req)
    {
        //var rr = new RestRequest("/api/User/ConfirmFacilityRequest", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<GetFacilityConfirmResponse>(rr, Method.Post);
        //return (resp?.Data);

        var resp = await client.PostAsJsonAsync("/api/User/ConfirmFacilityRequest", req);
        var data = await resp.Content.ReadFromJsonAsync<GetFacilityConfirmResponse>();
        return (data);
    }

    public async Task<GetFacilityConfirmResponse> DeleteFacilityRequest(HttpClient client, GetFacilityConnectRequests req)
    {
        var resp = await client.PostAsJsonAsync("/api/User/DeleteFacilityRequest", req);
        var data = await resp.Content.ReadFromJsonAsync<GetFacilityConfirmResponse>();
        return (data);
    }

    public async Task<GetFacilitiesResponse> GetFacilitybyInvitecode(HttpClient client, GetFacilityConnectRequests req)
    {
        //var rr = new RestRequest("/api/User/GetFacilitybyInvitecode", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<GetFacilitiesResponse>(rr, Method.Post);
        //return (resp?.Data);

        var resp = await client.PostAsJsonAsync("/api/User/GetFacilitybyInvitecode", req);
        var data = await resp.Content.ReadFromJsonAsync<GetFacilitiesResponse>();
        return (data);
    }

    public async Task<GetFacilityConfirmResponse> UserConnectFacility(HttpClient client, GetFacilityConnectRequests req)
    {
        //var rr = new RestRequest("/api/User/UserConnectFacility", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<GetFacilityConfirmResponse>(rr, Method.Post);
        //return (resp?.Data);

        var resp = await client.PostAsJsonAsync("/api/User/UserConnectFacility", req);
        var data = await resp.Content.ReadFromJsonAsync<GetFacilityConfirmResponse>();
        return (data);
    }

    public async Task<InviteUserResponse> InviteUser(HttpClient client, InviteUserRequest req)
    {
        //var rr = new RestRequest("/api/Client/InviteUser", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<InviteUserResponse>(rr, Method.Post);
        //return (resp?.Data);

        var resp = await client.PostAsJsonAsync("/api/Client/InviteUser", req);
        var data = await resp.Content.ReadFromJsonAsync<InviteUserResponse>();
        return (data);
    }

    public async Task<UpsertResidentResponse> UpsertResident(HttpClient client, UpsertResidentRequest req)
    {
        //var rr = new RestRequest("/api/User/UpsertResident", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<UpsertResidentResponse>(rr, Method.Post);
        //return (resp?.Data);

        var resp = await client.PostAsJsonAsync("/api/User/UpsertResident", req);
        var data = await resp.Content.ReadFromJsonAsync<UpsertResidentResponse>();
        return (data);
    }

    public async Task<GetResidentMonitoringTimesResponse> GetResidentMonitoringTimes(HttpClient client, GetResidentMonitoringTimesRequest req)
    {
        //var rr = new RestRequest("/api/User/GetResidentMonitoringTimes", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<GetResidentMonitoringTimesResponse>(rr, Method.Post);
        //return (resp?.Data);

        var resp = await client.PostAsJsonAsync("/api/User/GetResidentMonitoringTimes", req);
        var data = await resp.Content.ReadFromJsonAsync<GetResidentMonitoringTimesResponse>();
        return (data);
    }

    public async Task<baseResponse> UpsertResidentMonitoringTimes(HttpClient client, UpsertResidentMonitoringTimesRequest req)
    {
        //var rr = new RestRequest("/api/User/UpsertResidentMonitoringTimes", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<baseResponse>(rr, Method.Post);
        //return (resp?.Data);

        var resp = await client.PostAsJsonAsync("/api/User/UpsertResidentMonitoringTimes", req);
        var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
        return (data);
    }

    public async Task<GetResidentMonitoringLimitsNotificationsResponse> GetResidentLimitsNotifications(HttpClient client, GetResidentMonitoringLimitsNotificationsRequest req)
    {
        //var rr = new RestRequest("/api/User/GetResidentLimitsNotifications", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<GetResidentMonitoringLimitsNotificationsResponse>(rr, Method.Post);
        //return (resp?.Data);

        var resp = await client.PostAsJsonAsync("/api/User/GetResidentLimitsNotifications", req);
        var data = await resp.Content.ReadFromJsonAsync<GetResidentMonitoringLimitsNotificationsResponse>();
        return (data);
    }

    public async Task<baseResponse> UpsertResidentLimitsNotifications(HttpClient client, UpsertResidentMonitoringLimitsNotificationsRequest req)
    {
        //var rr = new RestRequest("/api/User/UpsertResidentLimitsNotifications", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<baseResponse>(rr, Method.Post);
        //return (resp?.Data);

        var resp = await client.PostAsJsonAsync("/api/User/UpsertResidentLimitsNotifications", req);
        var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
        return (data);
    }

    public async Task<UpsertResidentThresholdsResponse> UpsertResidentMonitoringSetup(HttpClient client, UpsertResidentThresholdsRequest req)
    {
        //var rr = new RestRequest("/api/User/UpsertResidentMonitoringSetup", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<UpsertResidentThresholdsResponse>(rr, Method.Post);
        //return (resp?.Data);
        var resp = await client.PostAsJsonAsync("/api/User/UpsertResidentMonitoringSetup", req);
        var data = await resp.Content.ReadFromJsonAsync<UpsertResidentThresholdsResponse>();
        return (data);
    }

    public async Task<GetAlertContactListResponse> GetResidentAlertContactList(HttpClient client, GetAlertContactListRequest req)
    {
        //var rr = new RestRequest("/api/User/GetResidentAlertContactList", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<GetAlertContactListResponse>(rr, Method.Post);
        //return (resp?.Data);

        var resp = await client.PostAsJsonAsync("/api/User/GetResidentAlertContactList", req);
        var data = await resp.Content.ReadFromJsonAsync<GetAlertContactListResponse>();
        return (data);
    }

    public async Task<GetAlertResidentListResponse> GetAlertResidentList(HttpClient client, GetAlertResidentListRequest req)
    {
        //var rr = new RestRequest("/api/User/GetAlertResidentList", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<GetAlertResidentListResponse>(rr, Method.Post);
        var resp = await client.PostAsJsonAsync("/api/User/GetAlertResidentList", req);
        var data = await resp.Content.ReadFromJsonAsync<GetAlertResidentListResponse>();
        return (data);
    }

    public async Task<baseResponse> UpdateResidentAlertStatus(HttpClient client, UpdateResidentAlertStatusRequest req)
    {
        //var rr = new RestRequest("/api/User/UpdateResidentAlertStatus", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<baseResponse>(rr, Method.Post);
        var resp = await client.PostAsJsonAsync("/api/User/UpdateResidentAlertStatus", req);
        var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
        return (data);
    }

    public async Task<ht.data.common.baseResponse> DeActivateUser(HttpClient client, string id)
    {
        //var rr = new RestRequest($"/api/User/DeActivateUser/{id}", Method.Get)
        //    .AddHeader("Content-Type", "application/json");

        //var resp = await client.ExecuteAsync<ht.data.common.baseResponse>(rr, Method.Get);
        var resp = await client.GetFromJsonAsync<data.common.baseResponse>($"/api/User/DeActivateUser/{id}");
        return (resp);
    }

    public async Task<ht.data.common.baseResponse> ActivateUser(HttpClient client, string id)
    {
        //var rr = new RestRequest($"/api/User/ActivateUser/{id}", Method.Get)
        //    .AddHeader("Content-Type", "application/json");

        //var resp = await client.ExecuteAsync<ht.data.common.baseResponse>(rr, Method.Get);

        var resp = await client.GetFromJsonAsync<data.common.baseResponse>($"/api/User/ActivateUser/{id}");
        return (resp);
    }
    public async Task<ht.data.common.baseResponse> DeleteUser(HttpClient client, string id)
    {
        //var rr = new RestRequest($"/api/User/DeleteUser/{id}", Method.Get)
        //    .AddHeader("Content-Type", "application/json");

        //var resp = await client.ExecuteAsync<ht.data.common.baseResponse>(rr, Method.Get);

        var resp = await client.GetFromJsonAsync<data.common.baseResponse>($"/api/User/DeleteUser/{id}");

        return (resp);
    }

    public async Task<GetOnetoOneChatThreadIdResponse> GetOnetoOneChatThreadId(HttpClient client, GetOnetoOneChatThreadIdRequest req)
    {
        //var rr = new RestRequest("/api/User/GetOnetoOneChatThreadId", Method.Post)
        //   .AddHeader("Content-Type", "application/json")
        //   .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<GetOnetoOneChatThreadIdResponse>(rr, Method.Post);
        var resp = await client.PostAsJsonAsync("/api/User/GetOnetoOneChatThreadId", req);
        var data = await resp.Content.ReadFromJsonAsync<GetOnetoOneChatThreadIdResponse>();
        return (data);
    }


    public async Task<ht.data.common.baseResponse> GetLoggedInUserACSToken(HttpClient client, string id)
    {
        //var rr = new RestRequest($"/api/User/GetLoggedInUserACSToken/{id}", Method.Get)
        //    .AddHeader("Content-Type", "application/json");

        //var resp = await client.ExecuteAsync<ht.data.common.baseResponse>(rr, Method.Get);
        var resp = await client.GetFromJsonAsync<data.common.baseResponse>($"/api/User/GetLoggedInUserACSToken/{id}");

        return (resp);
    }

    public async Task<data.common.baseResponse> CheckDuplicateEmail(HttpClient client, EmailVerificationRequest req)
    {
        //var rr = new RestRequest($"/api/User/CheckDuplicateEmail/{id}", Method.Get)
        //    .AddHeader("Content-Type", "application/json");

        //var resp = await client.ExecuteAsync<data.common.baseResponse>(rr, Method.Get);

        var resp = await client.PostAsJsonAsync($"/api/User/CheckDuplicateEmail",req);
        var data = await resp.Content.ReadFromJsonAsync<data.common.baseResponse>();
        return (data);
    }

    public async Task<InviteUserOnCallResponse> InviteUserOnCall(HttpClient client, InviteUserOnCallRequest req)
    {
        //var rr = new RestRequest("/api/Client/InviteUserOnCall", Method.Post)
        //    .AddHeader("Content-Type", "application/json")
        //    .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<InviteUserOnCallResponse>(rr, Method.Post);
        var resp = await client.PostAsJsonAsync("/api/Client/InviteUserOnCall", req);
        var data = await resp.Content.ReadFromJsonAsync<InviteUserOnCallResponse>();
        return (data);
    }
    public async Task<InviteUserOnCallResponse> SendVirtualConsultLinkToUser(HttpClient client, InviteUserOnCallRequest req)
    {

        var resp = await client.PostAsJsonAsync("/api/Client/SendVirtualConsultLinkToUser", req);
        var data = await resp.Content.ReadFromJsonAsync<InviteUserOnCallResponse>();
        return (data);
    }

    public async Task<ht.data.common.baseResponse> GetResidentsLastVitalsUpdateReport(HttpClient client, ht.data.common.baseRequest req)
    {
        //var rr = new RestRequest("/api/User/GetResidentsLastVitalsUpdateReport", Method.Post)
        //   .AddHeader("Content-Type", "application/json")
        //   .AddJsonBody(req, "application/json");

        //var resp = await client.ExecuteAsync<ht.data.common.baseResponse>(rr, Method.Post);
        var resp = await client.PostAsJsonAsync("/api/User/GetResidentsLastVitalsUpdateReport", req);
        var data = await resp.Content.ReadFromJsonAsync<ht.data.common.baseResponse>();
        return (data);
    }


    public async Task<GetHTuserFeedbackDetailsResponse> GetFeedBackDetailsList(HttpClient client, GetHTuserFeedbackDetailsRequest req)
    {
        var resp = await client.PostAsJsonAsync("/api/User/GetFeedBackDetailsList", req);
        var data = await resp.Content.ReadFromJsonAsync<GetHTuserFeedbackDetailsResponse>();
        return (data);
    }

    public async Task<baseResponse> UpsertFeedbackDetails(HttpClient client, UpsertHTuserFeedbackRequest req)
    {
        var resp = await client.PostAsJsonAsync("/api/User/UpsertFeedbackDetails", req);
        var data = await resp.Content.ReadFromJsonAsync<baseResponse>();
        return (data);
    }
    public async Task<AuthUserResponse> Authentication(HttpClient client, AuthUserRequest req)
    {
        var resp = await client.PostAsJsonAsync("/api/Client/Authentication", req);
        var data = await resp.Content.ReadFromJsonAsync<AuthUserResponse>();
        return (data);
    }

    public async Task<InviteUserOnCallResponse> SendResidentDirectLink(HttpClient client, InviteUserOnCallRequest req)
    {
        var resp = await client.PostAsJsonAsync("/api/Client/SendResidentDirectLink", req);
        var data = await resp.Content.ReadFromJsonAsync<InviteUserOnCallResponse>();
        return (data);
    }
}

