﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.data.common.Dashboards;
using ht.data.common.Telehealth;
using ht.data.common.Users;

using RestSharp;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace ht.bff.apis.Services
{
    public class HTBEScriptsApiService
    {

        public async Task<GetScriptsResponse> GetScripts(HttpClient client, GetScriptsRequest req)
        {
            //var rr = new RestRequest("/api/Scripts/GetScripts", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<GetScriptsResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Scripts/GetScripts", req);
            var data = await resp.Content.ReadFromJsonAsync<GetScriptsResponse>();
            return (data);
        }

        public async Task<GetScriptsResponse> GetScriptDetails(HttpClient client, string id)
        {
            //var rr = new RestRequest($"/api/Scripts/GetScriptDetails/{id}", Method.Get)
            //    .AddHeader("Content-Type", "application/json");

            //var resp = await client.GetAsync<GetScriptsResponse>(rr);
            //return (resp);

            var resp = await client.GetFromJsonAsync<GetScriptsResponse>($"/api/Scripts/GetScriptDetails/{id}");
            return (resp);
        }

        public async Task<UpsertScriptsResponse> UpsertScripts(HttpClient client, UpsertScriptsRequest req)
        {
            //var rr = new RestRequest("/api/Scripts/UpsertScripts", Method.Post)
            //    .AddHeader("Content-Type", "application/json")
            //    .AddJsonBody(req, "application/json");

            //var resp = await client.ExecuteAsync<UpsertScriptsResponse>(rr, Method.Post);
            //return (resp?.Data);

            var resp = await client.PostAsJsonAsync("/api/Scripts/UpsertScripts", req);
            var data = await resp.Content.ReadFromJsonAsync<UpsertScriptsResponse>();
            return (data);
        }


    }
}
