﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
	  <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
    <UserSecretsId>084c3c05-6d8e-4620-99b6-ed14402f72e5</UserSecretsId>
    <DockerDefaultTargetOS>Windows</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.10.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.20.0" />
    <PackageReference Include="DeviceDetector.NET.NetCore" Version="*******" />
    <PackageReference Include="ht.data.common" Version="1.0.2-250617135657" />
    <PackageReference Include="HttpMultipartParser" Version="8.2.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.21.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.DependencyCollector" Version="2.21.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.2" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="7.0.5" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="6.31.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.5" />
    <PackageReference Include="OpenCvSharp4" Version="4.9.0.20240103" />
    <PackageReference Include="OpenCvSharp4.Extensions" Version="4.9.0.20240103" />
    <PackageReference Include="OpenCvSharp4.runtime.win" Version="4.9.0.20240103" />
    <PackageReference Include="Polly" Version="7.2.4" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="7.0.8" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.31.0" />
    <PackageReference Include="TimeZoneConverter" Version="6.1.0" />
    <PackageReference Include="WindowsAzure.Storage" Version="9.3.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Common\bt.bff.common\bt.bff.common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.local.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

</Project>
