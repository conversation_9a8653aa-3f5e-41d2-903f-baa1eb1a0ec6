using Azure.Identity;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Azure.Services.AppAuthentication;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using System;
using System.Net;
using bu = bt.bff.common.Utils;

namespace ht.bff.apis
{
    public class Program
    {
        public static void Main(string[] args)
        {
            ServicePointManager.ReusePort = true;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;
            ServicePointManager.DefaultConnectionLimit = 1000;
            

            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.ConfigureAppConfiguration((hostingContext, config) =>
                    {
                        var settings = config
                                       //.SetBasePath(Directory.GetCurrentDirectory())
                                       .AddEnvironmentVariables()
                                       .AddEnvironmentVariables(prefix: "HTBFF:")
                                       .AddJsonFile("appsettings.local.json", optional: true, reloadOnChange: true)
                                       .Build();

                        var azTokProvider = new AzureServiceTokenProvider();

                        // Load creds from app settings for non-azure environments
                        var kv_uri = settings["HT_KV_URI"];
                        var clientId = settings.GetValue<string>("HT_KV_ClientId", defaultValue: "");
                        var secret =  settings.GetValue<string>("HT_KV_Secret", defaultValue: "");

                        // Creds for Azure environments
                        var creds = new DefaultAzureCredential();

                        if (bu.Environment.IsInAzure)
                            config.AddAzureKeyVault(new Uri(kv_uri), creds, new bu.PrefixKeyVaultSecretManager("htbff"));
                        else
                            config.AddAzureKeyVault(kv_uri, clientId, secret, new bu.PrefixKeyVaultSecretManager("htbff"));

                    }).UseStartup<Startup>();
                });
    }
}
