﻿using ht.data.common;

using OpenCvSharp;
using OpenCvSharp.Aruco;
using OpenCvSharp.Extensions;

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;

using AR = OpenCvSharp.Aruco;

namespace WoundDetection
{
    public class ImageUtils
    {
        public static int MarkerLengthInMM = 50; //5 CM
        public static double MarkerAreaRatio = 0; //This is the Area * the Area in MM = e.g. 14000 * 2500. This number is divided by the actual area to get the area in MM
        public static double MarkerLineRatio = 0;
        // F = (P x D)/W   - W known width, P - width in pixels, D istance from marker.

        public static Bitmap ReturnEdgedImage(Bitmap img, double threshold1 = 30, double threshold2 = 100, int apertureSize = 3, bool L2gradient = false)
        {
            // convert the image to grayscale
            Bitmap imgOut = ConvertToGrayScale(img);
            var dst = BitmapConverter.ToMat(imgOut);
            Mat edged = new Mat();

            // Apply <PERSON><PERSON><PERSON>lur to reduce noise and avoid false edges
            Cv2.GaussianBlur(dst, dst, new OpenCvSharp.Size(5, 5), 0);

            // Apply Canny edge detection to detect wound boundaries
            Cv2.Canny(dst, edged, threshold1, threshold2, apertureSize, L2gradient);

            return edged.ToBitmap();
        }

        // convert a bitmap image to grayscale
        public static Bitmap ConvertToGrayScale(Bitmap img)
        {
            var rect = new RectangleF(0, 0, img.Width, img.Height);
            Bitmap imgOut = img.Clone(rect, img.PixelFormat);

            var src = BitmapConverter.ToMat(img);
            var dst = BitmapConverter.ToMat(imgOut);

            Cv2.CvtColor(src, dst, ColorConversionCodes.BGR2GRAY);

            return dst.ToBitmap();
        }

        public static IOrderedEnumerable<ContourInfo> FindContours(Bitmap img)
        {
            var src = BitmapConverter.ToMat(img);
            var cnts = Cv2.FindContoursAsArray(src, RetrievalModes.List, ContourApproximationModes.ApproxSimple);


            var sorted = GetSortedList(cnts);
            //var rr = MaxContour(sorted);

            return sorted;
        }

        public static Bitmap DrawContours(IOrderedEnumerable<ContourInfo> cnts, Bitmap img, MarkerInfo marker = null)
        {
            var rect = new RectangleF(0, 0, img.Width, img.Height);
            Bitmap imgOut = img.Clone(rect, img.PixelFormat);
            var src = BitmapConverter.ToMat(imgOut);
            var colour = Scalar.Red;

            int i = 0;
            foreach (var c in cnts)
            {
                i++;
                // For now, we're only interested in the first 10 contours.
                if (i > 10)
                    break;

                var arry = new[] { c.Contour }.ToList();

                //Cv2.DrawContours(src, arry, 0, colour,-1);

                var approx = Cv2.ApproxPolyDP(c.Contour, 0.03 * Cv2.ArcLength(c.Contour, true), true);
                if (approx?.Length == 3)
                {
                    Cv2.DrawContours(src, arry, 0, Scalar.Green);
                }
                else if (approx?.Length > 7)
                {
                    Cv2.DrawContours(src, arry, 0, Scalar.Red, 4);
                    //var k = Cv2.IsContourConvex(approx);
                    //Cv2.DrawContours(src, arry, 0, Scalar.Red, 4);


                    Cv2.Polylines(src, c.Lines, true, Scalar.Blue, 4);
                    //Cv2.Circle(src, c.Box.Center.ToPoint(), 5, Scalar.Red, -1);

                    if (c.AreaInMM.GetValueOrDefault(0) > 0)
                    {
                        //Positioning and Drawing the Text
                        var cp = c.Box.GetValueOrDefault().Center.ToPoint();
                        cp.X -= 80;
                        cp.Y -= 15;
                        DrawText(src, $"Width: {(int)c.WidthMM}", cp);
                        cp.Y += 30;
                        DrawText(src, $"Height: {(int)c.HeightMM}", cp);
                    }

                }
            }
            var img2 = BitmapConverter.ToBitmap(src);
            return img2;


        }

        public static void DrawText(Mat src, string msg, OpenCvSharp.Point cp)
        {
            Cv2.PutText(src, msg, cp, HersheyFonts.HersheyPlain, 2, Scalar.Green, 2);
        }

        public static Tuple<IEnumerable<ContourInfo>, Bitmap> GetCircles(IOrderedEnumerable<ContourInfo> cnts, Bitmap img)
        {
            var kr = new List<ContourInfo>();
            var src = BitmapConverter.ToMat(img);

            //Cv2.CvtColor(src, src, ColorConversionCodes.GRAY2BGR);
            //src = new Mat(src.Size(), src.Type());
            foreach (var c in cnts)
            {
                var arry = new[] { c.Contour }.ToList();
                var colour = Scalar.Red;
                //Cv2.DrawContours(src, arry, 0, colour,-1);
                var approx = Cv2.ApproxPolyDP(c.Contour, 0.03 * Cv2.ArcLength(c.Contour, true), true);
                if (approx?.Length == 3)
                {
                    //lst.Items.Add($"triangle");
                    //Cv2.DrawContours(src,arry , 0, Scalar.Red);
                }
                else if (approx?.Length == 4)
                {
                    //lst.Items.Add($"square");
                    //Cv2.DrawContours(src, arry, 0, Scalar.Yellow);
                }
                else if (approx?.Length == 8)
                {
                    //lst.Items.Add($"8 sides");
                    var k = Cv2.IsContourConvex(approx);
                    if (k)
                    {
                        //lst.Items.Add("   CIRCLE");
                        kr.Add(c);
                    }
                    Cv2.DrawContours(src, arry, 0, Scalar.Red, 4);


                    Cv2.Polylines(src, c.Lines, true, Scalar.Blue, 4);
                    var pt = c.Box.GetValueOrDefault().Center.ToPoint();
                    Cv2.Circle(src, pt, 5, Scalar.Red, -1);
                    var cp = c.Box.GetValueOrDefault().Center.ToPoint();
                    cp.X -= 80;
                    cp.Y -= 15;
                    Cv2.PutText(src, $"Width: {(int)c.Box?.Size.Width}", cp, HersheyFonts.HersheyPlain, 2, Scalar.Green, 2);
                    cp.Y += 30;
                    Cv2.PutText(src, $"Height: {(int)c.Box?.Size.Height}", cp, HersheyFonts.HersheyPlain, 2, Scalar.Green, 2);


                }
            }
            var img2 = BitmapConverter.ToBitmap(src);

            return Tuple.Create<IEnumerable<ContourInfo>, Bitmap>(kr, img2);
        }

        public static RotatedRect MaxContour(IOrderedEnumerable<ContourInfo> orderedList)
        {

            var one = orderedList?.FirstOrDefault();
            return (Cv2.MinAreaRect(one.Contour));
        }

        public static IOrderedEnumerable<ContourInfo> GetSortedList(OpenCvSharp.Point[][] contours)
        {
            var dict = new Dictionary<int, ContourInfo>();
            foreach (var cnt in contours)
            {
                var ci = new ContourInfo(cnt)
                {
                    Rank = dict.Count
                };

                if (ci?.Area > 0)
                {
                    ci.AreaInMM = ImageUtils.MarkerAreaRatio * ci.Area;
                    ci.HeightMM = ImageUtils.MarkerLineRatio * (ci.Box?.Size.Height);
                    ci.WidthMM = ImageUtils.MarkerLineRatio * (ci.Box?.Size.Width);
                    dict[dict.Count] = ci;
                }
            }

            var sorted = dict.Values.OrderByDescending(s => s.Area);
            return (sorted);
        }

        public static ContourInfo GetContourFromPoint(Point2f pt, IOrderedEnumerable<ContourInfo> cnts)
        {
            if (cnts?.Count() > 0)
                foreach (var c in cnts?.Where(x => x.Area > 0))
                {
                    IEnumerable<OpenCvSharp.Point> points = c?.Contour?.ToList();
                    if (Cv2.PointPolygonTest(c.Contour.ToList(), pt, false) > -1)
                    {
                        return (c);
                    }
                }
            return null;
        }

        public static double GetMMPerPixel(double markerArea, double pixelArea)
        {
            var knownObjectArea = markerArea; // 282857.14;
            var result = Math.Sqrt(knownObjectArea / pixelArea);
            return result;

        }

        public static MarkerInfo MarkerDetection(Bitmap img)
        {
            MarkerInfo mi = null;

            var src = BitmapConverter.ToMat(img);
            DetectorParameters parms = new DetectorParameters();
            AR.Dictionary dict = AR.CvAruco.GetPredefinedDictionary(PredefinedDictionaryName.Dict6X6_250);

            //This will detect several markers in the picture - currently we're only using one.
            AR.CvAruco.DetectMarkers(src, dict, out Point2f[][] corners, out int[] ids, parms, out Point2f[][] rejectedPoints);

            if (corners.Length > 0) //found one or more markers.
            {
                mi = new MarkerInfo(corners, $"{ids?.First()}", MarkerLengthInMM);
                ImageUtils.MarkerAreaRatio = mi.MarkerAreaRatio.GetValueOrDefault(0);
                ImageUtils.MarkerLineRatio = mi.MarkerLineRatio.GetValueOrDefault(0);
            }

            return (mi);
        }

        public static Bitmap DrawMarker(MarkerInfo mi, Bitmap img)
        {
            Bitmap imgOut = null;
            if (mi != null)
            {
                var rect = new RectangleF(0, 0, img.Width, img.Height);
                imgOut = img.Clone(rect, img.PixelFormat);
                var src = BitmapConverter.ToMat(imgOut);

                Cv2.Polylines(src, mi.Lines, true, Scalar.LimeGreen, 10);
                imgOut = BitmapConverter.ToBitmap(src);

            }
            return imgOut ?? img;
        }

        public static Mat PrintMarker(Mat img, string charNum)
        {
            if (!int.TryParse(charNum, out var c))
                c = 23;
            var dict = AR.CvAruco.GetPredefinedDictionary(PredefinedDictionaryName.Dict6X6_250);

            // OpenCV 4.7.0 changes. Use generateImageMarker() instead of drawMarker()
            //AR.CvAruco.DrawMarker(dict, c, 200, img, 1);
            dict.GenerateImageMarker(c, 200, img, 1);

            return (img);
        }


    }

    public class ContourInfo : baseResponse
    {
        public string WoundImageUrl { get; set; }
        public List<List<OpenCvSharp.Point>> Lines { get; set; } = new();
        public List<OpenCvSharp.Point> Points { get; set; } = new();
        public OpenCvSharp.Point[] Contour { get; set; }
        public double Area { get; set; }
        public double? AreaInMM { get; set; }
        public double? AreaInCM { get; set; }
        public double? HeightMM { get; set; }
        public double? WidthMM { get; set; }
        public double? HeightCM { get; set; }
        public double? WidthCM { get; set; }
        public double? perimeter { get; set; }
        public double? Red { get; set; }
        public double? Yellow { get; set; }
        public double? Other { get; set; }
        public double? Black { get; set; }
        public int Rank { get; set; }
        public RotatedRect? Box { get; set; }

        public ContourInfo()
        {

        }
        public ContourInfo(OpenCvSharp.Point[] cnt)
        {
            Contour = cnt;
            PopulateContour();
        }

        public ContourInfo(Point2f[][] corners)
        {
            Contour = ConvertPoints(corners).ToArray();
            PopulateContour();
        }

        public void PopulateContour()
        {
            Area = Cv2.ContourArea(Contour);
            Box = Cv2.MinAreaRect((IEnumerable<OpenCvSharp.Point>)Contour);

            Points.AddRange(Contour.ToList());
            Lines.Add(Points);
        }

        public static List<OpenCvSharp.Point> ConvertPoints(Point2f[][] points)
        {
            var pts = new List<OpenCvSharp.Point>();
            foreach (var s in points)
                foreach (var pt in s)
                    pts.Add(new OpenCvSharp.Point(pt.X, pt.Y));
            return (pts);
        }
    }
    public class imageCoordinates
    {
        public float x { get; set; }
        public float y { get; set; }
    }
    public class MarkerInfo : ContourInfo
    {
        public string MarkerId { get; set; }
        //public int? AreaInMM { get; set; }

        /// <summary>
        /// Area ratio of mm2 (square mm) to pixels 
        /// </summary>
        public double? MarkerAreaRatio { get; set; }

        /// <summary>
        /// Line ratio of mm to pixels 
        /// </summary>
        public double? MarkerLineRatio { get; set; }

        public MarkerInfo(string id) : base()
        {
            MarkerId = id;
        }

        public MarkerInfo(Point2f[][] corners, string id, int? lenMM) : base(corners)
        {
            MarkerId = id;
            AreaInMM = lenMM * lenMM; // we are using a square marker

            MarkerAreaRatio = AreaInMM / this.Area; // mm2 (sqr mm) per pixel

            // Length of the side of the marker in pixels
            double lenPixels = Math.Sqrt(this.Area);

            MarkerLineRatio = lenMM / lenPixels; // mm per pixel
        }
    }
}
