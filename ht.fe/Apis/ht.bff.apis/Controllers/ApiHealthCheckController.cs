﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.bff.apis.Extensions;
using ht.bff.apis.Services;
using ht.data.common.Dashboards;
using ht.data.common.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Tests;
using ht.data.common.Users;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ht.data.common.Billing;
using System.Net.Http;

namespace ht.bff.apis.Controllers
{
    [Route("/")]
    [ApiController]
    public class ApiHealthCheckController : ControllerBase
    {
        public ApiHealthCheckController()
        {
        }

        [HttpGet]
        [Route("/")]
        public IActionResult Get()
        {
            return new OkResult();
        }

       


    }
}
