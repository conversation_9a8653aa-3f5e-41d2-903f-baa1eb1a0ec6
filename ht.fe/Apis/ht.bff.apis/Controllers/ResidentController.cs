﻿using bt.bff.common;
using bt.bff.common.Models;
using ht.bff.apis.Services;
using ht.data.common.Dashboards;
using ht.data.common.Tasks;
using ht.data.common.Tests;
using ht.data.common.Users;
using ht.bff.apis.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ht.data.common;
using ht.data.common.Vitals;
using baseResponse = ht.data.common.baseResponse;
using ht.data.common.Telehealth;
using ht.data.common.partner;
using System.Net.Http;
using Microsoft.AspNetCore.Authorization;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ResidentController : ControllerBase
    {
        HTBEResidentApiService _svc;
        private readonly IHttpClientFactory _clientFactory;
        private HttpClient _client;
        public ResidentController(HTBEResidentApiService svc, IHttpClientFactory clientFactory)
        {
            _svc = svc;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");
        }
        [Authorize]
        [HttpPost("GetDashboard")]
        public async Task<IActionResult> GetDashboard([FromBody] GetResidentDashboardRequest req)
        {
            GetResidentDashboardResponse br = new();


            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetDashboard(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetVitalsListForResidentGraphs/{exResidentId}")]
        public async Task<IActionResult> GetVitalsListForResidentGraphs(string exResidentId)

        {
            GetVitalsListResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetVitalsListForResidentGraphs(clnt, exResidentId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("SendErrorLogDetails")]
        public async Task<IActionResult> SendErrorLogDetails([FromBody] VitalsErrorLogDetailsRequest req)
        {
            baseResponse br = new();


            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.SendErrorLogDetails(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetResidentPartnerEventDetails")]
        public async Task<IActionResult> GetResidentPartnerEventDetails([FromBody] GetResidentPartnerEventDetailsRequest req)
        {
            GetResidentPartnerEventDetailsResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetResidentPartnerEventDetails(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetResidentPartnerMappingDetails")]
        public async Task<IActionResult> GetResidentPartnerMappingDetails([FromBody] GetPartnerIntegrationMappingDetailsRequest req)
        {
            GetPartnerIntegrationMappingDetailsResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetResidentPartnerMappingDetails(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetResidentOffline/{ExFacilityId}")]
        public async Task<IActionResult> GetResidentOffline(string ExFacilityId)
        {
            baseResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);

                var props = new Dictionary<string, string>();
                var res = await _svc.GetResidentByFacilityId(clnt, ExFacilityId);
                clnt = null;

                var rest = res.Results.Select(p => new
                {
                    p.FullName,p.UserExId
                });
                return (new JsonResult(rest));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [AllowAnonymous]
        [HttpGet("GetResidentDirectLink/{meetingCode}")]
        public async Task<IActionResult> GetResidentDirectLink(string meetingCode)
        {
            InviteUserOnCallResponse br = new();

            try
            {
                //var tok = HttpContext.Request.GetFacilityAndToken(new ht.data.common.baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientNoAuthAsync();
                var res = await _svc.GetResidentDirectLink(clnt, meetingCode);
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }
    }



}
