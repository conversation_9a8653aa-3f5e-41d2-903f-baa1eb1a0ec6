﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.bff.apis.Services;
using ht.data.common.Dashboards;
using ht.data.common.Tasks;
using ht.data.common.Tests;
using ht.data.common.Users;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ht.bff.apis.Extensions;
using System.Net.Http;
using Microsoft.AspNetCore.Authorization;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class NurseController : ControllerBase
    {
        HTBENurseApiService _svc;
        private readonly IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;
        public NurseController(HTBENurseApiService svc, IHttpClientFactory clientFactory)
        {
            _svc = svc;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");
        }

        //[HttpGet("GetMeetingDetails/{meetingId}")]
        //public async Task<IActionResult> GetMeetingDetails(string meetingId)
        //{
        //    GetMeetingDetailsResponse br = new();
        //    try
        //    {
        //        var clnt = await Settings.BE_API_AuthClient.GetHttpClientAsync();
        //        var res = await _svc.GetMeetingDetails(clnt, meetingId);
        //        clnt = null;
        //        return (new JsonResult(res));
        //    }
        //    catch (Exception ex)
        //    {
        //        br.FromException(ex);
        //        return (new JsonResult(br));

        //    }
        //}
        [Authorize]
        [HttpPost("GetDashboard")]
        public async Task<IActionResult> GetDashboard([FromBody] GetNurseDashboardRequest req)
        {
            GetNurseDashboardResponse br = new();
            if (ModelState.IsValid)
            {

                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.GetDashboard(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);

                }
            }
            return (new JsonResult(br));
        }



    }
}
