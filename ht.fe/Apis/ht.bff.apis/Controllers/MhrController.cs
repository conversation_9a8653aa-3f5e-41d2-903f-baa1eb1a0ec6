﻿using bt.bff.common;
using ht.bff.apis.Extensions;
using ht.bff.apis.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Net.Http;
using System.Threading.Tasks;
using System;
using bt.bff.common.Models;
using Microsoft.AspNetCore.Authorization;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MhrController : ControllerBase
    {
        HTBEMhrApiService _svc;
        private readonly IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;

        public MhrController(HTBEMhrApiService svc, IHttpClientFactory clientFactory)
        {
            _svc = svc;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost("GetMhrPortal")]
        public async Task<IActionResult> GetMhrPortal([FromBody] MhrPortalRequest req)
        {
            MhrPortalResponse response = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new data.common.baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);

                //clnt.BaseAddress = new Uri("https://localhost:5000");

                req.ExFacilityId = tok.Item2;
                response = await _svc.GetMhrPortal(clnt, req);
                clnt = null;
            }
            catch (Exception ex)
            {
                response.FromException(ex);
            }

            return (new JsonResult(response));
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost("UpsertHPIOCertificate")]
        public async Task<IActionResult> UpsertHPIOCertificate([FromBody] UpsertHPIOCertificateRequest req)
        {
            baseResponse response = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new data.common.baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);

                //clnt.BaseAddress = new Uri("https://localhost:5000");

                req.ExFacilityId = tok.Item2;
                response = await _svc.UpsertHPIOCertificate(clnt, req);
                clnt = null;
            }
            catch (Exception ex)
            {
                response.FromException(ex);
            }

            return (new JsonResult(response));
        }

        [Authorize]
        [HttpGet("GetFacilityMHRSetup/{ExFacilityId}")]
        public async Task<IActionResult> GetFacilityMHRSetup(string ExFacilityId)
        {
            if (string.IsNullOrEmpty(ExFacilityId))
            {
                return BadRequest();
            }

            MhrSetupResponse response = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new data.common.baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);

                //req.ExFacilityId = tok.Item2;
                response = await _svc.GetFacilityMHRSetup(clnt, ExFacilityId);
                clnt = null;
            }
            catch (Exception ex)
            {
                response.FromException(ex);
            }

            return (new JsonResult(response));
        }
    }
}
