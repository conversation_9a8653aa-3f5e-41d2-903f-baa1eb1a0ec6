﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.bff.apis.Extensions;
using ht.bff.apis.Services;
using ht.data.common;
using ht.data.common.Dashboards;
using ht.data.common.Tasks;
using ht.data.common.Tests;
using ht.data.common.Users;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FamilymemberController : ControllerBase
    {
        HTBEFamilymemberApiService _svc;
        private readonly IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;
        public FamilymemberController(HTBEFamilymemberApiService svc, IHttpClientFactory clientFactory)
        {
            _svc = svc;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");
        }

        [Authorize]
        [HttpPost("GetDashboard")]
        public async Task<IActionResult> GetDashboard([FromBody] GetFamilymemberDashboardRequest req)
        {
            GetFamilymemberDashboardResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok,_client);
                var res = await _svc.GetDashboard(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetResidentsforFamily/{exFamilyId}")]
        public async Task<IActionResult> GetResidentsforFamily(string exFamilyId)
        {
          GetUsersSearchBriefResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetResidentsforFamily(clnt, exFamilyId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

    }
}
