﻿using bt.bff.common;
using bt.bff.common.Models;
using bt.bff.common.Models.Security;
using ht.bff.apis.Helpers;
using ht.bff.apis.Services;
using ht.data.common.Tests;
using ht.data.common.Users;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ht.bff.apis.Extensions;
using ht.data.common;
using ht.data.common.EmailVerification;
using ht.bff.apis.Utils;
using ht.bff.apis.Examples;
using System.Net.Http;
using static Azure.Core.HttpHeader;
using Microsoft.Extensions.Configuration;
using System.Text;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using System.IdentityModel.Tokens.Jwt;
using Newtonsoft.Json;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //
    //[Authorize(AuthenticationSchemes = $"{JwtBearerDefaults.AuthenticationScheme},{CustomAuthenticationDefaults.AuthenticationScheme}")]
    public class ClientController : ControllerBase
    {
        private HTBEApiService _beSvc = null;
        private SignalRService _rSvc = null;
        private EmailVerificationService _emailSvc = null;
        private readonly HTBEResidentApiService _residentSvc;
        private readonly HTBEFacilityApiService _facSvc;
        private IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;
        private readonly IConfiguration _configuration;
        public ClientController(HTBEApiService svc, SignalRService sigRSvc,
            EmailVerificationService emailSvc,
            HTBEResidentApiService residentSvc,
            IHttpClientFactory clientFactory, HTBEFacilityApiService facSvc, IConfiguration configuration)
        {
            _configuration = configuration;
            _beSvc = svc;
            _rSvc = sigRSvc;
            _emailSvc = emailSvc;
            this._residentSvc = residentSvc;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");
            _facSvc = facSvc;
        }


        [AllowAnonymous]
        [HttpPost("Authentication")]
        public async Task<IActionResult> Authentication([FromBody] AuthUserRequest user)
        {
            AuthUserResponse tokenresponse = new();

            if (user != null)
            {
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientNoAuthFactoryAsync(_client);
                tokenresponse = await _beSvc.Authentication(clnt, user);
                if (tokenresponse.Status != "error")
                {
                    dynamic results = JsonConvert.DeserializeObject<dynamic>(tokenresponse.Token);

                    string windowsTz = Convert.ToString(results?.userLocalZone);
                    if (!string.IsNullOrEmpty(windowsTz))
                    {
                        if (TimeZoneConverter.TZConvert.TryWindowsToIana(windowsTz, out var tz))
                        {
                            results.userLocalZone = tz;
                        }
                    }

                    var issuer = _configuration["JwtIssuer"];
                    var audience = _configuration["JwtAudience"];
                    var key = Encoding.UTF8.GetBytes(_configuration["JwtKey"]);
                    var signingCredentials = new SigningCredentials(
                        new SymmetricSecurityKey(key),
                        SecurityAlgorithms.HmacSha512Signature
                    );

                    var subject = new ClaimsIdentity(new[]
                    {

                        new Claim("ht_roles", Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(Convert.ToString(results)))),
                        //new Claim("name", tokenresponse.FullName),

                    });


                    var expires = DateTime.UtcNow.AddDays(30);

                    var tokenDescriptor = new SecurityTokenDescriptor
                    {
                        Subject = subject,
                        Expires = expires,
                        Issuer = issuer,
                        Audience = audience,
                        SigningCredentials = signingCredentials
                    };

                    var tokenHandler = new JwtSecurityTokenHandler();
                    var token = tokenHandler.CreateToken(tokenDescriptor);
                    var jwtToken = tokenHandler.WriteToken(token);
                    tokenresponse.Token = jwtToken;
                    return Ok(tokenresponse);

                }
            }

            return new JsonResult(tokenresponse);
        }

        [AllowAnonymous]
        [HttpGet("Discover")]
        public async Task<IActionResult> Discover() //
        {
            var br = new bt.bff.common.baseResponse { Status = "Success" };
            try
            {

                var tok = HttpContext.Request.GetToken();
                var userRoles = HttpContext.Request.ExtractClientUserRoles(tok, true);
                //Detecting if its a mobile device
                var dd = new DeviceDetectorNET.DeviceDetector(HttpContext.Request.Headers.UserAgent);
                dd.Parse();


                //var tskSigR = _rSvc.GetAccessToken(userRoles.UserExId);
                //if (tskSigR.Status == TaskStatus.WaitingToRun)
                //    tskSigR.Start();


                var props = new Dictionary<string, string>();
                //earlier there was no token passed to the GetHttpClientAsync but it was throwing error while fetching the userAcsId and hence I passed this.
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(_client, tok);
                var getInfo = await _beSvc.GetClientDiscover(clnt, new DiscoveryRequest { AppName = "HT" });

                //get hash for resident
                var http = _clientFactory.CreateClient("ht-bff-client");
                var httpClnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(http, tok);
                var getFacilityInfo = await _facSvc.GetClientDiscoverForFacility(httpClnt, userRoles.UserExId);

                if (getInfo?.Properties?.Count > 0)
                {
                    foreach (var p in getInfo.Properties)
                    {
                        props[p.Key] = p.Value;
                    }
                }

                if (getFacilityInfo?.Properties?.Count > 0)
                {
                    foreach (var p in getFacilityInfo.Properties)
                    {
                        props[p.Key] = p.Value;
                    }
                }

                props["feBaseUrl"] = "https://apiuat.healthteams.com.au/fe/v1";
                props["htEventsBaseUrl"] = "https://apiuat.healthteams.com.au/events/v1";
                props["signupsigninUrl"] = "https://htclients.b2clogin.com/htclients.onmicrosoft.com/oauth2/v2.0/authorize?p=B2C_1A_SIGNUP_SIGNIN_WITH_LINK_UAT&client_id=021466d9-3809-4032-87c9-f14c803d486e&nonce=defaultNonce&redirect_uri=https%3A%2F%2Fjwt.ms&scope=openid&response_type=id_token&prompt=login";
                props["signupsigninflow"] = "B2C_1A_SIGNUP_SIGNIN_WITH_LINK_UAT";

                props["appInsightId"] = "3f157b1e-3d27-43d6-ac00-637dfab0b420";
                props["secAppToken"] = "App";

                props["serverTimeUtc"] = DateTime.UtcNow.ToString();

                if (dd.IsMobile())
                {
                    #region we need to consilodate these calls

                    var r = await _beSvc.GetClientInformation(clnt);
                    props["vitalsInformation"] = HTJsonSerialiser.Serialise(r?.Item1?.VitalsInformation);
                    props["acsId"] = r?.Item2;
                    #endregion

                    props["nsHubName"] = "ht-notify-uat";
                    props["nsConnString"] = "Endpoint=sb://ht-uat.servicebus.windows.net/;SharedAccessKeyName=DefaultListenSharedAccessSignature;SharedAccessKey=fx3wWH42V5FMzYpHKws2fNdc6+1XSTSq+fJpVtzG558=";
                    props["iotConnStr"] = Settings.Properties["iot-admin-constr"];// "HostName=read-hub.azure-devices.net;DeviceId=testDevice;SharedAccessKey=QqDHy68jFrJcxhEiQC/IF3OIGKTmjFFvPS2YpkL46Gc=";
                    props["appDroidRedirect"] = "msauth://com.companyname.HealthTeams/EMH6qW7gSI3MIzeLtE%2BhRwl3A3c%3D";
                    props["resAppKey"] = "abbcd82c-c216-4d25-bea7-aa1e53be5e7f"; //staging key
                }

                //tskSigR.Wait();
                //props["signalRResponse"] = tskSigR.Result;

                br.Properties = props;
                //props[""]
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return new JsonResult(br);
        }

        [HttpGet("Login")]

        public async Task<IActionResult> Login([FromHeader(Name = "Authorization")] string auth)
        {

            string tok = auth?.Replace("Bearer ", "").Replace("bearer ", "");

            var th = new TokenHelper().ReturnNewSecurityToken(tok);

            return new JsonResult(new bt.bff.common.baseResponse { Message = th });
        }

        [HttpPost("UpsertUser")]

        public async Task<IActionResult> UpsertUser([FromBody] UpsertUserRequest req)
        {
            UpsertUserResponse br = new();
            /*
            var clnt = await Settings.BE_API_AuthClient.GetHttpClientAsync();
            var r = await _beSvc.UpsertUsersRequest(clnt, new UpsertUsersRequestBackend { UpsertUserRequests = new List<UpsertUserRequest> { req }});
            if (r?.UpsertUserResponses?.Count>0)
                br = r.UpsertUserResponses[0];
            clnt = null;
            */
            return (new JsonResult(br));
        }

        [HttpGet]
        [Route("SecureCall")]
        public async Task<IActionResult> SecureCall()
        {
            return new JsonResult(new bt.bff.common.baseResponse { Message = "Congrats your call made it to the bff layer" });
        }

        [HttpGet]
        [Route("SecureCallToBackend")]

        public async Task<IActionResult> SecureCallToBackend()
        {
            var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(_client);
            var r = await _beSvc.MakeSecureCall(clnt);
            r.Message += "\r\nFrom the Client too";
            clnt = null;
            return (new JsonResult(r));
        }
       
        [Authorize]
        [HttpGet("GetInvitedUsers/{exfacilityId}")]
        public async Task<IActionResult> GetInvitedUsers(string exfacilityId)
        {
            InvitedUsersResponse br = new();
            baseRequest req = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _beSvc.GetInvitedUsers(clnt, exfacilityId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }
        
        [Authorize]
        [HttpPost("UpsertUserProfile")]
        public async Task<IActionResult> UpsertUserProfile([FromBody] HealthTeamsUser req)
        {
            UpsertUserProfileResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                //let's validate the provider numbers and medicare numbers.
                if (!string.IsNullOrEmpty(req?.ProviderNumber))
                {
                    var du = new data.common.Utils.DoctorValidation();
                    if (!du.CheckProvider(req.ProviderNumber))
                        throw new OverflowException("ERROR: Provider number is not valid");
                }

                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);

                //convert the timezone if required from the UI.
                if (!string.IsNullOrEmpty(req?.UserLocaleZone))
                {
                    if (TimeZoneConverter.TZConvert.TryIanaToWindows(req.UserLocaleZone, out var tz))
                    {
                        req.UserLocaleZone = tz;
                    }
                }

                var res = await _beSvc.UpsertUserProfile(clnt, req);

                if (res.user != null && !string.IsNullOrEmpty(res.user?.UserLocaleZone) && TimeZoneConverter.TZConvert.TryWindowsToIana(res.user.UserLocaleZone, out var tzs))
                {
                    res.user.UserLocaleZone = tzs;
                }

                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetUserProfileByExternalId/{externalId}")]
        public async Task<IActionResult> GetUserProfileByExternalId(string externalId)
        {
            GetUserProfileResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _beSvc.GetUserProfileByExternalId(clnt, externalId);

                if (!string.IsNullOrEmpty(res.User?.UserLocaleZone))
                {
                    if (res.User != null && !string.IsNullOrEmpty(res.User.UserLocaleZone) && TimeZoneConverter.TZConvert.TryWindowsToIana(res.User.UserLocaleZone, out var tz))
                    {
                        res.User.UserLocaleZone = tz;

                    }
                }
                clnt = null;



                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("InviteUser")]
        public async Task<IActionResult> InviteUser([FromBody] InviteUserRequest req)
        {
            InviteUserResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _beSvc.InviteUser(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }


        [AllowAnonymous]
        [ProducesResponseType(200, Type = typeof(EmailVerificationResponse))]
        [HttpPost("ValidateEmail")]
        public async Task<EmailVerificationResponse> ValidateEmail(EmailVerificationRequest req)
        {
            var res = await CheckDuplicateEmail(req);
            EmailVerificationResponse br = new();
            //MICK: 2022-06-28 work around for jon having issues with email validation.
            //br.Status = "valid";
            //br.Message = "Email Address Verified Successfully.";
            //return (br);
            if (res != null && res?.Status == "valid")
            {
                try
                {
                    br.Status = "valid";
                    // br = await _emailSvc.VerifyEmail(req?.Email);
                    if (br.Status != "valid")
                    {
                        br.Message = "Invalid Email Address.";
                    }
                    else
                    {
                        br.Message = "Email Address Verified Successfully.";
                    }
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }
            }
            else
            {
                br.Status = "invalid";
                br.Message = res.Message;
            }
            return (br);
        }

        private async Task<ht.data.common.baseResponse> CheckDuplicateEmail(EmailVerificationRequest req)
        {
            data.common.baseResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _beSvc.CheckDuplicateEmail(clnt, req);
                clnt = null;
                return (res);
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (br);
        }

        [HttpGet("GetLoggedInUserACSToken/{UserExId}")]
        public async Task<IActionResult> GetLoggedInUserACSToken(string UserExId)
        {
            data.common.baseResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _beSvc.GetLoggedInUserACSToken(clnt, UserExId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }
        
        [Authorize]
        [HttpPost("InviteUserOnCall")]
        public async Task<IActionResult> InviteUserOnCall([FromBody] InviteUserOnCallRequest req)
        {
            InviteUserOnCallResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _beSvc.InviteUserOnCall(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }
        //[HttpPost("InviteUserOnCallSMS")]
        //public async Task<IActionResult> InviteUserOnCallSMS([FromBody] InviteUserOnCallRequest req)
        //{
        //    InviteUserOnCallResponse br = new();

        //    try
        //    {
        //        var tok = HttpContext.Request.GetFacilityAndToken(req, true);
        //        var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
        //        var res = await _beSvc.InviteUserOnCallSMS(clnt, req);
        //        clnt = null;
        //        return (new JsonResult(res));
        //    }
        //    catch (Exception ex)
        //    {
        //        br.FromException(ex);
        //    }

        //    return (new JsonResult(br));
        //}

        [Authorize]
        [HttpPost("SendVirtualConsultLinkToUser")]
        public async Task<IActionResult> SendVirtualConsultLinkToUser([FromBody] InviteUserOnCallRequest req)
        {
            InviteUserOnCallResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _beSvc.SendVirtualConsultLinkToUser(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("SendResidentDirectLink")]
        public async Task<IActionResult> SendResidentDirectLink([FromBody] InviteUserOnCallRequest req)
        {
            InviteUserOnCallResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _beSvc.SendResidentDirectLink(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

    }
}
