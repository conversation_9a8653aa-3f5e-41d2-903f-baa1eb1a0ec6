﻿using bt.bff.common;
using bt.bff.common.Models;
using ht.bff.apis.Services;
using ht.data.common.Tests;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.bff.apis.Extensions;
using ht.data.common;
using System.Net.Http;
using Microsoft.AspNetCore.Authorization;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ScriptsController : ControllerBase
    {
        HTBEScriptsApiService _svc;
        private readonly IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;
        public ScriptsController(HTBEScriptsApiService svc, IHttpClientFactory clientFactory)
        {
            _svc = svc;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");
        }

        /// <summary>
        /// To get the list of presecriptions for the resident
        /// </summary>
        /// <remarks>
        /// More elaborate description
        /// </remarks>
        /// <param name="UserExternalId">This is typically the Resident Id</param>
        [Authorize]
        [HttpPost("GetScripts")]
        public async Task<IActionResult> GetScripts([FromBody] GetScriptsRequest req)
        {
            GetScriptsResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok,_client);
                var res = await _svc.GetScripts(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetScriptDetails/{exscriptId}")]
        public async Task<IActionResult> GetScriptDetails(string exscriptId)
        {
            GetScriptsResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetScriptDetails(clnt, exscriptId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }


        [Authorize]
        [HttpPost("UpsertScripts")]
        public async Task<IActionResult> UpsertScripts([FromBody] UpsertScriptsRequest req)
        {
            UpsertScriptsResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UpsertScripts(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

    }
}
