﻿using bt.bff.common;
using bt.bff.common.Models;
using ht.bff.apis.Services;
using ht.data.common.Tests;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.bff.apis.Extensions;
using ht.data.common;
using System.Net.Http;
using Microsoft.AspNetCore.Authorization;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class NotesController : ControllerBase
    {
        HTBENotesApiService _svc;
        private readonly IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;

        public NotesController(HTBENotesApiService svc, IHttpClientFactory clientFactory)
        {
            _svc = svc;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");

        }


        [Authorize]
        [HttpPost("GetNotes")]
        public async Task<IActionResult> GetNotes([FromBody] GetNotesRequest req)
        {
            GetNotesResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetNotes(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetNoteDetails/{exnoteId}")]
        public async Task<IActionResult> GetNoteDetails(string exnoteId)
        {
            GetNotesResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetNotesDetails(clnt, exnoteId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }


        [Authorize]
        [HttpPost("UpsertNote")]
        public async Task<IActionResult> UpsertNotes([FromBody] UpsertNotesRequest req)
        {
            UpsertNotesResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UpsertNotes(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetNotePdf")]
        public async Task<IActionResult> DownloadNotePdf([FromBody] GetNotePdfRequest req)
        {
            try
            {
                if (string.IsNullOrEmpty(req.ExResidentId))
                {
                    throw new Exception("ExResidentId not found");
                }
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.DownloadNote(clnt, req);
                clnt = null;
                var dt = System.DateTime.UtcNow.ToString("yyyyMMdd-hhmm");
                string fileName = $"HealthTeamsReport-{req.ExResidentId}-{dt}.pdf";


                return new FileContentResult(res, "application/pdf")
                {

                    FileDownloadName = fileName,
                    
                };
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

    }
}
