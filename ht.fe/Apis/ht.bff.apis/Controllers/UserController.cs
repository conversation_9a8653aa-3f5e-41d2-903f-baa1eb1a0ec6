﻿using bt.bff.common;

using ht.bff.apis.Extensions;
using ht.bff.apis.Services;
using ht.data.common;
using ht.data.common.EmailVerification;
using ht.data.common.Telehealth;
using ht.data.common.Users;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Mail;
using System.Runtime.InteropServices;
using System.Threading.Tasks;

using TimeZoneConverter;

using baseResponse = ht.data.common.baseResponse;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UserController : ControllerBase
    {
        private HTBEApiService _svc;
        private readonly IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;

        public UserController(HTBEApiService svc, IHttpClientFactory httpClientFactory)
        {
            _svc = svc;
            this._clientFactory = httpClientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");

        }

        [Microsoft.AspNetCore.Authorization.AllowAnonymous]
        [HttpGet("GetAvailableTimeZones")]
        public async Task<IActionResult> GetAvailableTimeZones()
        {
            GetTimezonesResponse br = new();
            bool isWindows = System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(OSPlatform.Windows);
            if (isWindows)
            {
                br.TimeZones = TimeZoneInfo.GetSystemTimeZones()
                    .Select(x => new HTTimeZone { DisplayName = x.DisplayName, Id = x.Id, IanaDisplayName = TZConvert.WindowsToIana(x.Id) })
                    .ToList();
            }
            else
            {
                br.TimeZones = TimeZoneInfo.GetSystemTimeZones()
                   .Select(x => new HTTimeZone { DisplayName = x.DisplayName, Id = x.Id, IanaDisplayName = x.Id })
                   .ToList();

            }



            return new JsonResult(br);

        }

        [HttpPost("ChangePassword")]
        [ProducesResponseType(200, Type = typeof(ChangeUserPasswordResponse))]
        public async Task<IActionResult> ChangePassword([FromBody] ChangeUserPasswordRequest req)
        {
            ChangeUserPasswordResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                br = await _svc.ChangePassword(clnt, req);

            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return new JsonResult(br);
        }

        [HttpPost("AskForAccess")]
        [ProducesResponseType(200, Type = typeof(AskForAccessResponse))]
        public async Task<IActionResult> AskForAccess([FromBody] AskForAccessRequest req)
        {
            AskForAccessResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetToken();

                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(_client, tok);
                br = await _svc.AskForAccessRequest(clnt, req);
                br.Status = "Success";
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return new JsonResult(br);
        }

        [Authorize]
        [HttpPost("UploadProfilePic")]
        public async Task<IActionResult> UploadProfilePic()
        {
            ht.data.common.baseResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetToken();
                var usr = HttpContext.Request.ExtractClientUserRoles(tok, true);
                var ftype = "profile";
                var fname = "profilepic.png";
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(_client, tok);
                IList<string> AllowedFileExtensions = new List<string> { ".jpg", ".gif", ".png" };
                var req = HttpContext.Request;

                var parser = await HttpMultipartParser.MultipartFormDataParser.ParseAsync(req.Body).ConfigureAwait(false);

                if (parser.Files?.Count == 0)
                    throw new FileNotFoundException("No file found in upload.");

                //passing userExId for which profile pic need to update.
                foreach (var p in parser.Parameters)
                {
                    if (p.Name == "userExId")
                    {
                        usr.UserExId = p.Data;
                    }
                    if (p.Name == "ftype")
                    {
                        ftype = p.Data;
                        if (ftype == "logo")
                        {
                            fname = "logo.png";
                        }
                    }
                }

                var file = parser.Files.First();
                var ext = Path.GetExtension(file.FileName);
                if (!AllowedFileExtensions.Contains(ext))
                    return BadRequest("Invalid File extension");


                //upload the file to the backend.
                using (var ms = new MemoryStream())
                {
                    await file.Data.CopyToAsync(ms);
                    var bytes = ms.ToArray();
                    var res = await _svc.UploadUserProfilePic(clnt, bytes, usr, ftype, fname);
                    br = res;
                }

            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return new JsonResult(br);
        }

        [Authorize]
        [HttpPost("GetUsersSearchBrief")]
        public async Task<IActionResult> GetUsersSearchBrief([FromBody] GetUsersSearchRequest req)
        {
            GetUsersSearchBriefResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetUsersSearchBrief(clnt, req);
                clnt = null;
                br = res;

            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetUsersSearch")]
        public async Task<IActionResult> GetUsersSearch([FromBody] GetUsersSearchRequest req)
        {
            GetUsersSearchResponse br = new();
            if (ModelState.IsValid)
            {
                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.GetUsersSearch(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetUserDetails/{userexId}")]
        public async Task<IActionResult> GetUserDetails(string userexId)
        {
            GetUserDetailsResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetUserDetails(clnt, userexId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        //[HttpGet("GetMonitoringData/{userexId}")]
        //public async Task<IActionResult> GetMonitoringData(string userexId)
        //{
        //    GetMonitoringDataResponse br = new();
        //    if (Settings.Is_Test)
        //        br = (GetMonitoringDataResponse)TestMaster.CreateMockData(br);
        //    else
        //    {
        //        try
        //        {
        //            var clnt = await Settings.BE_API_AuthClient.GetHttpClientAsync();
        //            var res = await _svc.GetMonitoringData(clnt, userexId);
        //            clnt = null;

        //        }
        //        catch (Exception ex)
        //        {
        //            br.FromException(ex);
        //        }
        //    }
        //    return (new JsonResult(br));
        //}

        /// <summary>
        /// This method is for getting the residents details with health summary.
        /// </summary>
        /// <remarks>
        /// This method is for getting the residents details with health summary.
        /// </remarks>

        [Authorize]
        [HttpPost("GetResident")]
        public async Task<IActionResult> GetResident([FromBody] GetResidentRequest req)
        {
            GetResidentResponse br = new();

            if (ModelState.IsValid)
            {

                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.GetResident(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }

            }
            return (new JsonResult(br));
        }

        /// <summary>
        /// To get the list if facilities associated to the logged in user
        /// </summary>
        /// <remarks>
        /// This method is for getting the list of facilities associated to logged in Nurse/Doctor
        /// </remarks>

        [Authorize]
        [HttpPost("GetFacilities")]
        public async Task<IActionResult> GetFacilities([FromBody] GetFacilitiesRequest req)
        {
            GetFacilitiesResponse br = new();
            if (ModelState.IsValid)
            {
                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.GetFacilities(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetResidentsSearch")]
        public async Task<IActionResult> GetResidentsSearch([FromBody] GetResidentsSearchRequest req)
        {
            GetResidentsSearchResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetResidentsSearch(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }


        [HttpGet("GetContactMethodList")]
        public async Task<IActionResult> GetContactMethodList()
        {
            var br = new string[] { "Mobile", "Email", "Other" };
            return (new JsonResult(br));
        }
        [Authorize]
        [HttpGet("GetCountries")]
        public async Task<IActionResult> GetCountries()
        {
            GetFacilitiesResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetCountries(clnt);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("DisconnectFacility")]
        public async Task<IActionResult> DisconnectFacility([FromBody] DisconnectFacilityRequest req)
        {
            DisconnectFacilityResponse br = new();

            if (ModelState.IsValid)
            {
                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.DisconnectFacility(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetUserConnectedFacilities/{userExId}")]
        [ProducesResponseType(typeof(ConnectedFacilitiesResponse), 200, "application/json")]
        public async Task<IActionResult> GetUserConnectedFacilities([FromRoute] string userExId)
        {
            ConnectedFacilitiesResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetUserConnectedFacilities(clnt, userExId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetNewFacilityRequests/{userExId}")]
        public async Task<IActionResult> GetNewFacilityRequests(string userExId)
        {
            GetNewFacilityRequestsResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetNewFacilityRequests(clnt, userExId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        /// <summary>
        /// This method is to confirm the new facility request. User need to pass userExId and userInviteId which will be available while showing the requests
        /// </summary>
        /// <remarks>
        /// This method is to confirm the new facility request. User need  user to pass userExId and userInviteId which will be available while showing the requests
        /// </remarks>
        [Authorize]
        [HttpPost("ConfirmFacilityRequest")]
        public async Task<IActionResult> ConfirmFacilityRequest([FromBody] GetFacilityConnectRequests req)
        {
            GetFacilityConfirmResponse br = new();
            if (ModelState.IsValid)
            {
                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.ConfirmFacilityRequest(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("DeleteFacilityRequest")]
        public async Task<IActionResult> DeleteFacilityRequest([FromBody] GetFacilityConnectRequests req)
        {
            GetFacilityConfirmResponse br = new();
            if (ModelState.IsValid)
            {
                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.DeleteFacilityRequest(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }
            }
            return (new JsonResult(br));
        }

        /// <summary>
        /// This method is to get the facility name by passing the inviteCode and userExId. User need to pass userExId and inviteCode to this API request.
        /// </summary>
        /// <remarks>
        /// This method is to get the facility name by passing the inviteCode and userExId. User need to pass userExId and inviteCode to this API request.
        /// </remarks>
        [Authorize]
        [HttpPost("GetFacilitybyInvitecode")]
        public async Task<IActionResult> GetFacilitybyInvitecode([FromBody] GetFacilityConnectRequests req)
        {
            GetFacilitiesResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetFacilitybyInvitecode(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        /// <summary>
        /// This method is to associate user with facility. User need to pass userExId and inviteCode to this API request.
        /// </summary>
        /// <remarks>
        /// This method is to associate user with facility. User need to pass userExId and inviteCode to this API request.
        /// </remarks>
        [Authorize]
        [HttpPost("UserConnectFacility")]
        public async Task<IActionResult> UserConnectFacility([FromBody] GetFacilityConnectRequests req)
        {
            GetFacilityConfirmResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UserConnectFacility(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertResident")]
        public async Task<IActionResult> UpsertResident([FromBody] UpsertResidentRequest req)
        {
            UpsertResidentResponse br = new();
            baseResponse brs = new();
            brs.Status = "valid";
            if (ModelState.IsValid)
            {

                try
                {
                    if (!string.IsNullOrEmpty(req.resident?.MedicareNumber) && !req.resident.IsMedicareFormatValid())
                        throw new InvalidDataException("ERROR: Medicare number not valid. Please fix");

                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    if (string.IsNullOrEmpty(req.resident.ExResidentId))
                    {
                        EmailVerificationRequest emailreq = new();
                        emailreq.Email = req.resident.Email;
                        emailreq.Role = "resident";

                        //Check duplicate email for resident.
                        brs = await _svc.CheckDuplicateEmail(clnt, emailreq);

                    }
                    if (brs != null && brs?.Status == "valid")
                    {
                        var res = await _svc.UpsertResident(clnt, req);
                        clnt = null;
                        return (new JsonResult(res));
                    }
                    else
                    {
                        br.Status = "Error";
                        br.Message = "ERROR: Email address already exists. Please use different email address.";
                    }
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }

            }
            return (new JsonResult(br));
        }

        #region Resident monitoring setup
        [Authorize]
        [HttpPost("GetResidentMonitoringTimes")]
        public async Task<IActionResult> GetResidentMonitoringTimes([FromBody] GetResidentMonitoringTimesRequest req)
        {
            GetResidentMonitoringTimesResponse br = new();

            if (ModelState.IsValid)
            {

                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.GetResidentMonitoringTimes(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }

            }
            return (new JsonResult(br));
        }
        
        [Authorize]
        [HttpPost("GetResidentLimitsNotifications")]
        public async Task<IActionResult> GetResidentLimitsNotifications([FromBody] GetResidentMonitoringLimitsNotificationsRequest req)
        {
            GetResidentMonitoringLimitsNotificationsResponse br = new();

            if (ModelState.IsValid)
            {

                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.GetResidentLimitsNotifications(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }

            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertResidentMonitoringTimes")]
        public async Task<IActionResult> UpsertResidentMonitoringTimes([FromBody] UpsertResidentMonitoringTimesRequest req)
        {
            ht.data.common.baseResponse br = new();

            if (ModelState.IsValid)
            {

                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.UpsertResidentMonitoringTimes(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }

            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertResidentLimitsNotifications")]
        public async Task<IActionResult> UpsertResidentLimitsNotifications([FromBody] UpsertResidentMonitoringLimitsNotificationsRequest req)
        {
            ht.data.common.baseResponse br = new();

            if (ModelState.IsValid)
            {

                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.UpsertResidentLimitsNotifications(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }

            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertResidentMonitoringSetup")]
        public async Task<IActionResult> UpsertResidentMonitoringSetup([FromBody] UpsertResidentThresholdsRequest req)
        {
            UpsertResidentThresholdsResponse br = new();

            if (ModelState.IsValid)
            {

                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.UpsertResidentMonitoringSetup(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }

            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetResidentAlertContactList")]
        public async Task<IActionResult> GetResidentAlertContactList([FromBody] GetAlertContactListRequest req)
        {
            GetAlertContactListResponse br = new();

            if (ModelState.IsValid)
            {

                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.GetResidentAlertContactList(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }

            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetAlertResidentList")]
        public async Task<IActionResult> GetAlertResidentList([FromBody] GetAlertResidentListRequest req)
        {
            GetAlertResidentListResponse br = new();

            if (ModelState.IsValid)
            {

                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.GetAlertResidentList(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }

            }
            return (new JsonResult(br));
        }

        #endregion

        [Authorize]
        [HttpPost("UpdateResidentAlertStatus")]
        public async Task<IActionResult> UpdateResidentAlertStatus([FromBody] UpdateResidentAlertStatusRequest req)
        {
            ht.data.common.baseResponse br = new();
            if (ModelState.IsValid)
            {
                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.UpdateResidentAlertStatus(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("DeActivateUser/{userExId}")]
        public async Task<IActionResult> DeActivateUser(string userExId)
        {
            ht.data.common.baseResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.DeActivateUser(clnt, userExId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("ActivateUser/{userExId}")]
        public async Task<IActionResult> ActivateUser(string userExId)
        {
            ht.data.common.baseResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.ActivateUser(clnt, userExId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("DeleteUser/{userExId}")]
        public async Task<IActionResult> DeleteUser(string userExId)
        {
            ht.data.common.baseResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.DeleteUser(clnt, userExId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetOnetoOneChatThreadId")]
        public async Task<IActionResult> GetOnetoOneChatThreadId([FromBody] GetOnetoOneChatThreadIdRequest req)
        {
            GetOnetoOneChatThreadIdResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetOnetoOneChatThreadId(clnt, req);
                clnt = null;
                br = res;

            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetResidentsLastVitalsUpdateReport")]
        public async Task<IActionResult> GetResidentsLastVitalsUpdateReport([FromBody] GetResidentsLastVitalCaptureReportRequest req)
        {
            baseResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetResidentsLastVitalsUpdateReport(clnt, req);
                clnt = null;
                br = res;

            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetFeedBackDetailsList")]
        public async Task<IActionResult> GetFeedBackDetailsList([FromBody] GetHTuserFeedbackDetailsRequest req)
        {
            GetHTuserFeedbackDetailsResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetFeedBackDetailsList(clnt, req);
                clnt = null;
                br = res;

            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertFeedbackDetails")]
        public async Task<IActionResult> UpsertFeedbackDetails([FromBody] UpsertHTuserFeedbackRequest req)
        {
            ht.data.common.baseResponse br = new();
            if (ModelState.IsValid)
            {
                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.UpsertFeedbackDetails(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }
            }
            return (new JsonResult(br));
        }


        [HttpPost("ResetUserPassword")]
        [ProducesResponseType(200, Type = typeof(InvitedUsersResponse))]
        public async Task<IActionResult> ResetUserPassword([FromBody] ChangeUserPasswordRequest req)
        {
            InviteUserResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                br = await _svc.ResetUserPassword(clnt, req);

            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return new JsonResult(br);
        }


        [HttpPost("ResetPassword")]
        [ProducesResponseType(200, Type = typeof(ResetUserPasswordResponse))]
        public async Task<IActionResult> ResetPassword([FromBody] ResetUserPasswordRequest req)
        {
            ResetUserPasswordResponse br = new();
            try
            {
                // var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientNoAuthFactoryAsync(_client);
                br = await _svc.ResetPassword(clnt, req);

            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return new JsonResult(br);
        }

    }
}
