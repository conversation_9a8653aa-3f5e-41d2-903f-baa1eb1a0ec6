﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.bff.apis.Services;
using ht.data.common.Dashboards;
using ht.data.common.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Tests;
using ht.data.common.Users;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using ht.bff.apis.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ht.data.common;
using ht.data.common.Wounds;
using System.Net.Http;
using OpenCvSharp.Flann;
using Microsoft.AspNetCore.Authorization;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MonitoringdataController : ControllerBase
    {
        HTBEMonitoringdataApiService _svc;
        private readonly IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;
        public MonitoringdataController(HTBEMonitoringdataApiService svc, IHttpClientFactory clientFactory)
        {
            _svc = svc;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");
        }

        [Authorize]
        [HttpPost("UpsertMonitoringData")]
        public async Task<IActionResult> UpsertMonitoringData([FromBody] UpsertMonitoringDataRequest req)
        {
            UpsertMonitoringDataResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UpsertMonitoringData(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }


        /// <summary>
        /// Short, descriptive title of the operation
        /// </summary>
        /// <remarks>
        /// More elaborate description
        /// </remarks>
        /// <param name="userExternalId">This is typically the Resident Id</param>
        /// 
        [Authorize]
        [HttpPost("GetMonitoringDataList")]
        public async Task<IActionResult> GetMonitoringDataList([FromBody] GetMonitoringDataListRequest req)
        {
            //TODO: Incorporate dateFromUtc

            GetMonitoringDataListResponse br = new();
            if (ModelState.IsValid)
            {
                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.GetMonitoringDataList(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }

            }
            return (new JsonResult(br));
        }


        [Authorize]
        [HttpGet("GetMonitoringData/{ExTransactionId}")]
        public async Task<IActionResult> GetMonitoringData(string ExTransactionId)
        {
            GetMonitoringDataResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetMonitoringData(clnt, ExTransactionId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetUserLastVitals/{userexId}")]
        public async Task<IActionResult> GetUserLastVitals(string userexId)
        {
            TelehealthMonitorData br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetUserLastVitals(clnt, userexId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                //br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetResidentThresholds/{exResidnetId}")]
        public async Task<IActionResult> GetResidentThresholds(string exResidnetId)
        {
            GetResidentThresholdsResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetResidentThresholds(clnt, exResidnetId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetMonitoringTypes")]
        public async Task<IActionResult> GetMonitoringTypes()
        {
            var br = new string[] { "Self", "Nurse" };
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetResidentWounds")]
        public async Task<IActionResult> GetResidentWounds([FromBody] GetWoundRequest req)
        {
            GetWoundsResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetResidentWounds(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetWoundDetails")]
        public async Task<IActionResult> GetWoundDetails([FromBody] GetWoundRequest req)
        {
            GetWoundsResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetWoundDetails(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertWoundDetails")]
        public async Task<IActionResult> UpsertWoundDetails([FromBody] UpsertWoundDetailsRequest req)
        {
            GetWoundsResponse br = new();

            try
            {
                // Updating the file urls to remove the auth token
                if (req?.Wound?.WoundDetails.Count > 0)
                {
                    for (int i = 0; i < req?.Wound?.WoundDetails?.Count; i++)
                    {
                        if (req?.Wound?.WoundDetails[i]?.WoundImages?.Count > 0)
                        {
                            for (int j = 0; j < req?.Wound?.WoundDetails[i]?.WoundImages?.Count; j++)
                            {
                                string input = req?.Wound?.WoundDetails[i]?.WoundImages[j]?.FileUrl;
                                if (!string.IsNullOrEmpty(input))
                                {
                                    int index = input.IndexOf('?');
                                    req.Wound.WoundDetails[i].WoundImages[j].FileUrl = (index >= 0) ? input.Substring(0, index) : input;
                                }
                            }

                        }
                    }
                }
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UpsertWoundDetails(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }
    }
}
