﻿using bt.bff.common;
using bt.bff.common.Models;
using ht.bff.apis.Services;
using ht.data.common.Tests;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.bff.apis.Extensions;
using ht.data.common;
using ht.data.common.Reports;
using System.Net.Http;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ReportController : ControllerBase
    {
        HTBEApiService _svc;
        private readonly IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;
        public ReportController(HTBEApiService svc, IHttpClientFactory clientFactory)
        {
            _svc = svc;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");
        }

       
        [HttpPost("GetReport")]
        public async Task<IActionResult> GetReport([FromBody] GetReportRequest req)
        {
            GetReportResponse br = new();

            try
            {

                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientAsync(tok);
                req.ExFacilityId = tok.Item2;
                var res = await _svc.GetReport(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        
        [HttpPost("GetReportList")]
        public async Task<IActionResult> GetReportList([FromBody] baseRequest req)
        {
            GetReportResponse br = new();

            try
            {

                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                req.ExFacilityId = tok.Item2;
                var res = await _svc.GetReportList(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }


    }
}
