﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.bff.apis.Extensions;
using ht.bff.apis.Services;
using ht.data.common.Dashboards;
using ht.data.common.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Tests;
using ht.data.common.Users;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ht.data.common.Billing;
using System.Net.Http;
using Microsoft.AspNetCore.Authorization;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BillsessionController : ControllerBase
    {
        HTBEBillsessionApiService _svc;
        private readonly IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;
        public BillsessionController(HTBEBillsessionApiService svc, IHttpClientFactory clientFactory)
        {
            _svc = svc;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");
        }
        [Authorize]
        [HttpGet("GetBillsession/{userExId}")]
        public async Task<IActionResult> GetBillsession(string userExId)
        {
            GetBillSessionResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new data.common.baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok,_client);
                var res = await _svc.GetBillsession(clnt, userExId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetBillsessionDetails/{exbillId}")]
        public async Task<IActionResult> GetScriptDetails(string exbillId)
        {
            try
            {
                UpsertBillSessionResponse br = new();
                if (Settings.Is_Test)
                    br = (UpsertBillSessionResponse)TestMaster.CreateMockDataBillSession(br);
                else
                {
                    try
                    {
                        var tok = HttpContext.Request.GetFacilityAndToken(new data.common.baseRequest(), true);
                        var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                        var res = await _svc.GetBillsessionDetails(clnt, exbillId);
                        clnt = null;
                        return (new JsonResult(res));
                    }
                    catch (Exception ex)
                    {
                        br.FromException(ex);
                    }
                    

                }
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {

                throw;
            }
           
            
        }

        [Authorize]
        [HttpPost("UpsertBillSession")]
        public async Task<IActionResult> UpsertBillSession([FromBody] UpsertBillSessionRequest req)
        {
            UpsertBillSessionResponse br = new();
            if (Settings.Is_Test)
                br = (UpsertBillSessionResponse)TestMaster.CreateMockDataBillSession(br);
            else
            {
                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.UpsertBillSession(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("VerifyMedicare")]
        public async Task<IActionResult> VerifyMedicare([FromBody] MedicareVerificationRequest req)
        {
            MedicareVerificationResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.VerifyMedicare(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

    }
}
