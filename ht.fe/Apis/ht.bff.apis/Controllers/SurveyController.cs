﻿using bt.bff.common;
using bt.bff.common.Models;
using ht.bff.apis.Services;
using ht.data.common.Tests;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.bff.apis.Extensions;
using ht.data.common;
using ht.data.common.Surveys;
using System.Net.Http;
using Microsoft.AspNetCore.Authorization;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SurveyController : ControllerBase
    {
        HTBESurveyApiService _svc;
        private readonly IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;
        public SurveyController(HTBESurveyApiService svc, IHttpClientFactory clientFactory)
        {
            _svc = svc;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");
        }


        [Authorize]
        [HttpPost("GetSurveyListForFacility")]
        public async Task<IActionResult> GetSurveyListForFacility([FromBody] GetSurveyListRequest req)
        {
            GetSurveyListResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok,_client);
                var res = await _svc.GetSurveyListForFacility(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetSurvey")]
        public async Task<IActionResult> GetSurvey([FromBody] GetSurveyRequest req)
        {
            GetSurveyResponse br = new();
           
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok,_client);
                var res = await _svc.GetSurvey(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertSurveyForResident")]
        public async Task<IActionResult> UpsertSurveyForResident([FromBody] UpsertSurveyRequest req)
        {
            UpsertSurveyResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok,_client);
                var res = await _svc.UpsertSurveyForResident(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertSurveyForFacility")]
        public async Task<IActionResult> UpsertSurveyForFacility([FromBody] UpsertSurveyRequest req)
        {
            UpsertSurveyResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UpsertSurveyForFacility(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetSurveyResultsListForResident")]
        public async Task<IActionResult> GetSurveyResultsListForResident([FromBody] GetSurveyListRequest req)
        {
            GetSurveyResultsListResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetSurveyResultsListForResident(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetSurveyResultsListForFacility")]
        public async Task<IActionResult> GetSurveyResultsListForFacility([FromBody] GetSurveyListRequest req)
        {
            GetSurveyResultsListResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetSurveyResultsListForFacility(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetSurveyResult/{exSurveyResponseId}")]
        public async Task<IActionResult> GetSurveyResult(string exSurveyResponseId)
        {
            GetSurveyResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetSurveyResult(clnt, exSurveyResponseId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetQuestionList/{exFacilityId}")]
        public async Task<IActionResult> GetQuestionList(string exFacilityId)
        {
            GetQuestionListResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetQuestionList(clnt, exFacilityId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetAnswerList/{exFacilityId}")]
        public async Task<IActionResult> GetAnswerList(string exFacilityId)
        {
            GetAnswerListResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetAnswerList(clnt, exFacilityId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertQuestionForFacility")]
        public async Task<IActionResult> UpsertQuestionForFacility([FromBody] UpsertQuestionRequest req)
        {
            UpsertQuestionResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UpsertQuestionForFacility(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpdateSurveyStatus")]
        public async Task<IActionResult> UpdateSurveyStatus([FromBody] UpdateSurveyStatusRequest req)
        {
            UpdateSurveyStatusResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UpdateSurveyStatus(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

    }
}
