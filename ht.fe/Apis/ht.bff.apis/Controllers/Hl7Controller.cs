﻿using bt.bff.common;
using ht.bff.apis.Extensions;
using ht.bff.apis.Services;
using ht.data.common.Telehealth;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Net.Http;
using System.Threading.Tasks;
using System;
using bt.bff.common.Models;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class Hl7Controller : ControllerBase
    {
        Hl7Service _svc;
        private readonly IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;

        public Hl7Controller(Hl7Service svc, IHttpClientFactory clientFactory)
        {
            _svc = svc;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");

        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("CreateMessage")]
        public async Task<IActionResult> CreateHl7Message([FromBody] Hl7Request req)
        {
            data.common.baseResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new data.common.baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                req.ExFacilityId = tok.Item2;
                br = await _svc.CreateHl7(clnt, req);
                clnt = null;
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="exFacilityId"></param>
        /// <returns></returns>
        [HttpGet("GetHl7Messages/{exFacilityId}")]
        public async Task<IActionResult> GetHl7Messages(string exFacilityId)
        {
            if(string.IsNullOrEmpty(exFacilityId))
            {
                return BadRequest();
            }


            Hl7Response res = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new data.common.baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                res.Hl7Data = await _svc.GetHl7Messages(clnt, exFacilityId);
                clnt = null;
            }
            catch (Exception ex)
            {
                res.FromException(ex);
            }

            return (new JsonResult(res));

        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="messageId"></param>
        /// <returns></returns>
        [HttpGet("GetHl7Acknowledgment/{messageId}")]
        public async Task<IActionResult> GetHl7Acknowledgment(string messageId)
        {
            if (string.IsNullOrEmpty(messageId))
            {
                return BadRequest();
            }


            Hl7Response response = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new data.common.baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                response.HL7Acknowledgment = await _svc.GetHl7Acknowledgment(clnt, messageId);
                clnt = null;
              
            }
            catch (Exception ex)
            {
                response.FromException(ex);
            }

            return (new JsonResult(response));

        }
    }
}
