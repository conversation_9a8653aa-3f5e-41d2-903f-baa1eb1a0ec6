﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.bff.apis.Examples;
using ht.bff.apis.Services;
using ht.data.common.Dashboards;
using ht.data.common.Tasks;
using ht.data.common.Tests;
using ht.data.common.Users;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

using Swashbuckle.AspNetCore.Filters;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ht.bff.apis.Extensions;
using ht.data.common;
using System.Net.Http;
using Microsoft.AspNetCore.Authorization;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TaskController : ControllerBase
    {
        HTBETaskApiService _svc;
        private readonly IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;
        public TaskController(HTBETaskApiService svc, IHttpClientFactory clientFactory)
        {
            _svc = svc;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");

        }

        [Authorize]
        [HttpPost("GetTasksList")]
        public async Task<IActionResult> GetTasksList([FromBody] GetTaskListRequest req)
        {
            GetTaskSearchResponse br = new() { Status = "Success" };
            if (ModelState.IsValid)
            {
                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.GetTasksList(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetTask/{extaskId}")]
        public async Task<IActionResult> GetTask(string extaskId)
        {
            GetTaskSearchResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetTask(clnt, extaskId);

                if (res.Tasks.Count > 0 && TimeZoneConverter.TZConvert.TryWindowsToIana(res.Tasks[0].TaskLocalLocale, out var tz))
                {
                    res.Tasks[0].TaskLocalLocale = tz;
                    if (!string.IsNullOrEmpty(res.Tasks[0]?.TaskRepeats?.LocaleZone))
                    {
                        res.Tasks[0].TaskRepeats.LocaleZone = tz;
                    }
                }
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertTask")]
        [SwaggerResponseHeader(200, "UpsertTaskResponse", "UpsertTaskResponse", null)]
        public async Task<IActionResult> UpsertTask([FromBody] UpsertTaskRequest req)
        {
            UpsertTaskResponse br = new() { Status = "Success" };
            if (ModelState.IsValid)
            {
                try
                {
                    var guid = new data.common.Utils.GUIDValidator();
                    if (string.IsNullOrEmpty(req.Task?.ExResidentId))
                        throw new Exception("ERROR: unassigned resident. Please supply one");
                    else
                    {
                        bool isvalid = guid.ValidateGuid(req.Task?.ExResidentId, "ExResidentId");
                    }
                    if (string.IsNullOrEmpty(req.Task?.ExFacilityId))
                    {
                        throw new Exception("ERROR: Please supply valid exFacilityId");
                    }
                    else
                    {
                        bool isvalid = guid.ValidateGuid(req.Task?.ExFacilityId, "ExFacilityId");
                    }
                    if (string.IsNullOrEmpty(req.Task?.AssignedToId))
                    {
                        req.Task.AssignedToId = null;
                    }
                    else
                    {
                        bool isvalid = guid.ValidateGuid(req.Task?.AssignedToId, "AssignedToId");
                    }

                    if (string.IsNullOrEmpty(req.Task?.ExDoctorId))
                    {
                        req.Task.ExDoctorId = null;
                    }
                    else
                    {
                        bool isvalid = guid.ValidateGuid(req.Task?.ExDoctorId, "ExDoctorId");
                    }
                    if (string.IsNullOrEmpty(req.Task?.ExDoctorId))
                    {
                        req.Task.ExDoctorId = null;
                    }
                    else
                    {
                        bool isvalid = guid.ValidateGuid(req.Task?.ExDoctorId, "ExDoctorId");
                    }
                    //Check date time passed against the current UTC date time and throw error if it is less than current utc datetime. This is only for one time task not for the repeat task.
                    if (req.Task?.TaskRepeats?.WeekDays.Count == null && req.Task?.TaskDateTimeUtc < DateTime.UtcNow)
                    {
                        br.Message = "Date and Time is Passed for the Selected Timezone..";
                        br.Status = "Error";
                        return (new JsonResult(br));
                        //  throw new Exception("ERROR: Date and Time is Passed for the Selected Timezone..");
                    }
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);

                    //convert the timezone if required from the UI.
                    if (TimeZoneConverter.TZConvert.TryIanaToWindows(req.Task.TaskLocalLocale, out var tz))
                    {
                        req.Task.TaskLocalLocale = tz;
                        if (!string.IsNullOrEmpty(req.Task?.TaskRepeats?.LocaleZone))
                        {
                            req.Task.TaskRepeats.LocaleZone = tz;
                        }
                    }
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.UpsertTask(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetTasksSearch")]
        public async Task<IActionResult> GetTasksSearch([FromBody] GetTasksSearchRequest req)
        {
            GetTaskSearchResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetTasksSearch(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetVitalsList")]
        public async Task<IActionResult> GetVitalsList([FromBody] GetVitalsListRequest req)
        {
            GetVitalsListResponse br = new() { };

            if (ModelState.IsValid)
            {
                try
                {
                    var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                    var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                    var res = await _svc.GetVitalsList(clnt, req);
                    clnt = null;
                    return (new JsonResult(res));
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }

            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetTaskTypeList")]
        public async Task<IActionResult> GetTaskTypeList()
        {
            var br = new string[] { "CollectVitals", "Telehealth", "Other" };
            return (new JsonResult(br));
        }

        [HttpGet("GetFrequencyList")]
        public async Task<IActionResult> GetFrequencyList()
        {
            var br = new string[] { "Daily/Weekly", "Monthly" };
            return (new JsonResult(br));
        }
        [HttpGet("GetWeekOfMonthList")]
        public async Task<IActionResult> GetWeekOfMonthList()
        {
            var br = new string[] { "First", "Second", "Third", "Fourth" };
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("DeleteTask/{extaskId}")]
        public async Task<IActionResult> DeleteTask(string extaskId)
        {
            UpsertTaskResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.DeleteTask(clnt, extaskId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetUnassignedTasksList")]
        public async Task<IActionResult> GetUnassignedTasksList([FromBody] GetUnAssignedTaskListRequest req)
        {
            GetTaskSearchResponse br = new() { Status = "Success" };

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetUnassignedTasksList(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("AssignTasks")]
        public async Task<IActionResult> AssignTasks([FromBody] AssignTasksRequest req)
        {
            ht.data.common.baseResponse br = new() { Status = "Success" };

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.AssignTasks(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpdateTasksStatus")]
        public async Task<IActionResult> UpdateTasksStatus([FromBody] UpdateTasksStatusRequest req)
        {
            ht.data.common.baseResponse br = new() { Status = "Success" };

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UpdateTasksStatus(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertAppointmentRequest")]
        public async Task<IActionResult> UpsertAppointmentRequest([FromBody] UpsertAppointmentRequest req)
        {
            GetAppointmentRequestSearchResponse br = new() { Status = "Success" };

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);

                //convert the timezone if required from the UI.
                if (TimeZoneConverter.TZConvert.TryIanaToWindows(req.Request.TaskLocalLocale, out var tz))
                {
                    req.Request.TaskLocalLocale = tz;
                }
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UpsertAppointmentRequest(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetAppointmentRequestList")]
        public async Task<IActionResult> GetAppointmentRequestList([FromBody] GetAppointmentRequestSearchRequest req)
        {
            GetAppointmentRequestSearchResponse br = new() { Status = "Success" };

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetAppointmentRequestList(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("AcknowledgeAppointmentRequests")]
        public async Task<IActionResult> AcknowledgeAppointmentRequests([FromBody] AckAppointmentRequest req)
        {
            data.common.baseResponse br = new() { Status = "Success" };

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.AcknowledgeAppointmentRequests(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }
    }
}
