﻿using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using bt.bff.common;

using ht.bff.apis.Extensions;
using ht.bff.apis.Helpers;
using ht.bff.apis.Services;
using ht.bff.apis.Utils;
using ht.data.common;
using ht.data.common.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Users;
using ht.data.common.Wounds;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;

using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

using WoundDetection;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TelehealthController : ControllerBase
    {
        HTBETeleHealthApiService _svc;
        private readonly IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;
        private readonly IConfiguration _configuration;
        BlobHelper _blobHelper;



        public TelehealthController(HTBETeleHealthApiService svc, IHttpClientFactory clientFactory, IConfiguration configuration, BlobHelper blobHelper)
        {
            _configuration = configuration;
            _svc = svc;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");
            _blobHelper = blobHelper;
        }

        [Authorize]
        [HttpPost("GetTeleHealthDetails")]
        public async Task<IActionResult> GetTeleHealthDetails([FromBody] GetTeleHealthDetailsRequest req)
        {
            GetTeleHealthDetailsResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetTeleHealthDetails(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("ConvertTaskToMeeting")]
        public async Task<IActionResult> ConvertTaskToMeeting([FromBody] ConvertTaskToMeetingRequest req)
        {
            ConvertTaskToMeetingResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetToken();
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(_client, tok);
                var res = await _svc.ConvertTaskToMeeting(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("LaunchMeeting")]
        [ProducesResponseType(200, Type = typeof(LaunchMeetingResponse))]
        public async Task<IActionResult> LaunchMeeting([FromBody] LaunchMeetingRequest req)
        {
            LaunchMeetingResponse br = new() { Status = "Success" };

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);

                br = await _svc.LaunchMeeting(clnt, req);
                clnt = null;
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

       
        [HttpPost("GetMeetingId")]
        public async Task<IActionResult> GetMeetingId([FromBody] GetMeetingIdRequest req)
        {
            GetMeetingIdResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);

                var res = await _svc.GetMeetingId(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetResidentVitalGraphData")]
        public async Task<IActionResult> GetResidentVitalGraphData([FromBody] GetResidentVitalGraphDataRequest req)
        {
            GetResidentVitalGraphDataResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);

                var res = await _svc.GetResidentVitalGraphData(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("DeleteVitals")]
        public async Task<IActionResult> DeleteVitals([FromBody] DeleteVitalsRequest req)
        {
            data.common.baseResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.DeleteVitals(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }


        #region ACS Server Services

        [HttpPost("StartRecording")]
        public async Task<IActionResult> StartRecording([FromBody] LaunchMeetingRequest req)
        {
            data.common.baseResponse br = new();

            try
            {
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientNoAuthFactoryAsync(_client);
                var res = await _svc.StartRecording(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [HttpPost("PauseRecording")]
        public async Task<IActionResult> PauseRecording([FromBody] LaunchMeetingRequest req)
        {
            data.common.baseResponse br = new();

            try
            {
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientNoAuthFactoryAsync(_client);
                var res = await _svc.PauseRecording(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [HttpPost("ResumeRecording")]
        public async Task<IActionResult> ResumeRecording([FromBody] LaunchMeetingRequest req)
        {
            data.common.baseResponse br = new();

            try
            {
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientNoAuthFactoryAsync(_client);
                var res = await _svc.ResumeRecording(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [HttpPost("StopRecording")]
        public async Task<IActionResult> StopRecording([FromBody] LaunchMeetingRequest req)
        {
            data.common.baseResponse br = new();

            try
            {
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientNoAuthFactoryAsync(_client);
                var res = await _svc.StopRecording(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [HttpPost("GetRecordingState")]
        public async Task<IActionResult> GetRecordingState([FromBody] LaunchMeetingRequest req)
        {
            data.common.baseResponse br = new();

            try
            {
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientNoAuthFactoryAsync(_client);
                var res = await _svc.GetRecordingState(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        #endregion
        [Authorize]
        [HttpGet("GetSelectionsByType/{selectionType}")]
        public async Task<IActionResult> GetSelectionsByType(string selectionType)
        {
            GetSelectionsResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new ht.data.common.baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetSelectionsByType(clnt, selectionType);
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

       
        [HttpGet("GetMeetingLink/{meetingCode}")]
        public async Task<IActionResult> GetMeetingLink(string meetingCode)
        {
            InviteUserOnCallResponse br = new();

            try
            {
                //var tok = HttpContext.Request.GetFacilityAndToken(new ht.data.common.baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientNoAuthAsync();
                var res = await _svc.GetMeetingLink(clnt, meetingCode);
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UploadMultipleFiles")]
        public async Task<IActionResult> UploadMultipleFilesAsync()
        {
            var req = HttpContext.Request;

            string folderName = string.Empty;
            string fileName = string.Empty;
            string filepath = _configuration["blobusersurl"];
            IList<string> AllowedFileExtensions = new List<string> { ".jpg", ".gif", ".png", ".jpeg", ".JPG", ".GIF", ".PNG", ".JPEG" };
            var parser = await HttpMultipartParser.MultipartFormDataParser.ParseAsync(req.Body).ConfigureAwait(false);

            if (parser.Files?.Count == 0)
                throw new FileNotFoundException("No file found in upload.");
            foreach (var p in parser.Parameters)
            {
                if (p.Name == "exResidentId")
                {
                    folderName = p.Data;
                    break;
                }
            }

            List<WoundImage> res = new List<WoundImage>();

            if (parser.Files.Count > 0)
            {
                foreach (var file in parser.Files)
                {
                    WoundImage img = new WoundImage();
                    string timestamp = Convert.ToString((long)DateTime.UtcNow.Subtract(DateTime.UnixEpoch).TotalSeconds);
                    var ext = Path.GetExtension(file.FileName);
                    if (!AllowedFileExtensions.Contains(ext))
                        return BadRequest("Invalid File extension");
                    fileName = timestamp + "_" + file.FileName.Replace(" ", "_");
                    using (var ms = new MemoryStream())
                    {
                        await file.Data.CopyToAsync(ms);
                        string storageConnectionString = _configuration["blobusersConns"];
                        BlobContainerClient containerClient = new BlobContainerClient(storageConnectionString, "users");
                        ms.Position = 0;

                        var blobClient = containerClient.GetBlobClient(folderName + "/Wounds/" + fileName);
                        var blobHttpHeaders = new BlobHttpHeaders
                        {
                            ContentType = file.ContentType
                        };

                        await blobClient.UploadAsync(ms, new BlobUploadOptions { HttpHeaders = blobHttpHeaders });
                    }
                    img.FileName = fileName;
                    var resp = await AskForAccessAsync(filepath + "/" + folderName + "/Wounds/" + fileName);
                    img.FileUrl = resp;
                    img.status = "Active";
                    img.ThumbnailUrl = string.Empty;
                    res.Add(img);
                }
            }
            return new JsonResult(res);
        }

        [Authorize]
        [HttpPost("WoundSizeDetection")]
        public async Task<IActionResult> WoundSizeDetection([FromForm] string imageCoordinates, [FromForm] string exResidentId, [FromForm] string ExDetailId)
        {
            ContourInfo br = new ContourInfo();
            var apiBResponse = string.Empty;
            string folderName = exResidentId;
            string fileName = string.Empty;
            string WoundImageURL = string.Empty;
            string filepath = _configuration["blobusersurl"]; //"https://htappdemo.blob.core.windows.net/users";// 
            IList<string> AllowedFileExtensions = new List<string> { ".jpg", ".gif", ".png", ".jpeg", ".JPG", ".GIF", ".PNG", ".JPEG" };

            if (Request.Form.Files?.Count == 0)
                throw new FileNotFoundException("No file found in upload.");
            foreach (var fileform in Request.Form.Files)
            {
                string timestamp = Convert.ToString((long)DateTime.UtcNow.Subtract(DateTime.UnixEpoch).TotalSeconds);
                var ext = Path.GetExtension(fileform.FileName);
                if (!AllowedFileExtensions.Contains(ext))
                    return BadRequest("Invalid File extension");
                fileName = timestamp + "_" + fileform.FileName.Replace(" ", "_");
                using (var ms = new MemoryStream())
                {
                    await fileform.CopyToAsync(ms);
                    //var bytes = ms.ToArray();
                    string storageConnectionString = _configuration["blobusersConns"];//"DefaultEndpointsProtocol=https;AccountName=htappdemo;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";//
                    BlobContainerClient containerClient = new BlobContainerClient(storageConnectionString, "users");
                    ms.Position = 0;
                    containerClient.UploadBlob("/" + folderName + "/Wounds/" + fileName, ms);

                }

                var resp = await AskForAccessAsync(filepath + "/" + folderName + "/Wounds/" + fileName);
                WoundImageURL = resp;
            }

            foreach (var fileform in Request.Form.Files)
            {
                using (var client = new HttpClient())
                {
                    using (var content = new MultipartFormDataContent())
                    {

                        var tokhsv = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                        var clnthsv = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tokhsv, _client);
                        var reshsv = await _svc.GetFacilityHSVRanges(clnthsv, tokhsv.Item2);
                        string reshsvJson = JsonSerializer.Serialize(reshsv.HSVRanges);

                        content.Add(new StringContent(reshsvJson, Encoding.UTF8, "application/json"), "hsvRanges");
                        content.Add(new StreamContent(fileform.OpenReadStream()), "file", fileform.FileName);
                        content.Add(new StringContent(imageCoordinates), "ImageCoordinates");
                        content.Add(new StringContent(exResidentId), "exResidentId");
                        var response = await client.PostAsync("https://woundsizedetection.azurewebsites.net/api/Wound/WoundSizeDetection", content);

                        if (response.IsSuccessStatusCode)
                        {
                            apiBResponse = await response.Content.ReadAsStringAsync();
                            br = HTJsonSerialiser.Deserialise<ContourInfo>(apiBResponse.ToString());

                            if (br.Status != "Error")
                            {
                                br.WoundImageUrl = WoundImageURL;

                                if (string.IsNullOrEmpty(ExDetailId))
                                {
                                    ExDetailId = null;
                                }
                                UpsertWoundAreaDetailsRequest areaRequest = new();
                                WoundAreaDetails woundarea = new();
                                baseRequest req = new();
                                woundarea.ExResidentId = exResidentId;
                                woundarea.ExDetailId = ExDetailId;
                                woundarea.WoundAreaInMM = br.AreaInMM;
                                woundarea.WoundAreaInCM = br.AreaInCM;
                                woundarea.FileUrl = br.WoundImageUrl;
                                woundarea.imageCoordinates = imageCoordinates;
                                woundarea.HeightCM = br.HeightCM;
                                woundarea.WidthCM = br.WidthCM;
                                woundarea.perimeter = br.perimeter;
                                woundarea.Red = br.Red;
                                woundarea.Yellow = br.Yellow;
                                woundarea.Black = br.Black;
                                woundarea.Other = br.Other;
                                areaRequest.WoundAreaDetails = woundarea;
                              //  var tok = HttpContext.Request.GetFacilityAndToken(new ht.data.common.baseRequest(), true);
                                //var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                                var res = await _svc.UpsertWoundAreaDetails(clnthsv, areaRequest);
                            }

                        }
                        else
                        {
                            br.Message = await response.Content.ReadAsStringAsync();
                            br.Status = "Error";
                        }
                    }
                }
            }

            return new JsonResult(br);
        }

        [Authorize]
        [HttpGet("DeleteWound/{exWoundId}")]
        public async Task<IActionResult> DeleteWound(string exWoundId)
        {
            data.common.baseResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.DeleteWound(clnt, exWoundId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetWoundTreatmentPlan")]
        public async Task<IActionResult> GetWoundTreatmentPlan([FromBody] GetWoundTreatmentPlanRequest req)
        {
            GetWoundTreatmentPlanResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetWoundTreatmentPlan(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetFacilityWoundTreatmentPlan")]
        public async Task<IActionResult> GetFacilityWoundTreatmentPlan([FromBody] GetFacilityWoundTreatmentPlanRequest req)
        {
            GetWoundTreatmentPlanResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetFacilityWoundTreatmentPlan(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertFacilityWoundTreatmentPlan")]
        public async Task<IActionResult> UpsertFacilityWoundTreatmentPlan([FromBody] UpsertFacilityWoundTreatmentPlanRequest req)
        {
            GetWoundTreatmentPlanResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UpsertFacilityWoundTreatmentPlan(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertFacilityHSVRanges")]
        public async Task<IActionResult> UpsertFacilityHSVRanges([FromBody] UpsertFacilityHSVRangesRequest req)
        {
            data.common.baseResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UpsertFacilityHSVRanges(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetFacilityHSVRanges/{exFacilityID}")]
        public async Task<IActionResult> GetFacilityHSVRanges(string exFacilityID)
        {
            GetFacilityHSVRangeResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetFacilityHSVRanges(clnt, exFacilityID);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }
        private async Task<string> AskForAccessAsync(string fileUrl)
        {
            data.common.baseResponse br = new();
            try
            {
                if (!string.IsNullOrEmpty(fileUrl) && fileUrl.StartsWith("https://"))
                {
                    var key = await _blobHelper.GetSasToken(fileUrl, 30);
                    if (string.IsNullOrEmpty(key))
                        throw new NullReferenceException("Token cannot be null");
                    fileUrl = fileUrl + "?" + key;
                }
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return fileUrl;
        }

    }
}