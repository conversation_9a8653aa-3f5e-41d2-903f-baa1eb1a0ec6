﻿using bt.bff.common;
using bt.bff.common.Models;

using ht.bff.apis.Services;
using ht.data.common.Dashboards;
using ht.data.common.FacilitySetup;
using ht.data.common.Tasks;
using ht.data.common.Tests;
using ht.data.common.Users;
using ht.bff.apis.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using ht.data.common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Net.Http;
using Microsoft.AspNetCore.Authorization;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FacilityController : ControllerBase
    {
        HTBEFacilityApiService _svc;
        private readonly IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;
        public FacilityController(HTBEFacilityApiService svc, IHttpClientFactory clientFactory)
        {
            _svc = svc;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");
        }

        [Authorize]
        [HttpPost("GetDashboard")]
        public async Task<IActionResult> GetDashboard([FromBody] GetFacilityDashboardRequest req)
        {
            GetFacilityDashboardResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetDashboard(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertFacility")]
        public async Task<IActionResult> UpsertFacility([FromBody] UpsertFacilityRequest req)
        {
            UpsertFacilityResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);


                //convert the timezone if required from the UI.
                if (!string.IsNullOrEmpty(req?.Facility?.FacilityLocaleZone))
                {
                    if (TimeZoneConverter.TZConvert.TryIanaToWindows(req.Facility?.FacilityLocaleZone, out var tz))
                    {
                        req.Facility.FacilityLocaleZone = tz;
                    }
                }

                var res = await _svc.UpsertFacility(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        /// <summary>
        /// To get the list if facilities associated to the logged in user
        /// </summary>
        /// <remarks>
        /// This method is for getting the list of facilities associated to logged in Nurse/Doctor
        /// </remarks>
        /// <param name="UserExternalId">This is the Logged In User Id</param>
        /// 
        [Authorize]
        [HttpPost("GetFacilities")]
        public async Task<IActionResult> GetFacilities([FromBody] GetFacilitiesRequest req)
        {
            GetFacilitiesResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetFacilities(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetFacility")]
        public async Task<IActionResult> GetFacility([FromBody] GetFacilityRequest req)
        {
            GetFacilityResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok,_client);
                var res = await _svc.GetFacility(clnt, req);
                if (!string.IsNullOrEmpty(res.Facility?.FacilityLocaleZone))
                {
                    if (res.Facility != null && !string.IsNullOrEmpty(res.Facility.FacilityLocaleZone) && TimeZoneConverter.TZConvert.TryWindowsToIana(res.Facility.FacilityLocaleZone, out var tz))
                    {
                        res.Facility.FacilityLocaleZone = tz;

                    }
                }
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetAvailableFacilityDevices")]
        public async Task<IActionResult> GetAvailableFacilityDevices([FromBody] GetFacilityDevicesRequest req)
        {
            GetFacilityDevicesResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetAvailableFacilityDevices(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetSelectedFacilityDevices")]
        public async Task<IActionResult> GetSelectedFacilityDevices([FromBody] GetFacilityDevicesRequest req)
        {
            GetFacilityDevicesResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetSelectedFacilityDevices(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertFacilityDevices")]
        public async Task<IActionResult> UpsertFacilityDevices([FromBody] UpsertFacilityDevicesRequest req)
        {
            bt.bff.common.baseResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UpsertFacilityDevices(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("GetParentFacilities")]
        public async Task<IActionResult> GetParentFacilities([FromBody] GetFacilitiesRequest req)
        {
            GetFacilitiesResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetParentFacilities(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetPharmacies/{exfacilityId}")]
        public async Task<IActionResult> GetPharmacies(string exfacilityId)
        {
            GetPharmaciesResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetPharmacies(clnt, exfacilityId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertPharmacy")]
        public async Task<IActionResult> UpsertPharmacy([FromBody] UpsertPharmacyRequest req)
        {
            UpsertPharmacyResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UpsertPharmacy(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetFacilityAdmins/{exfacilityId}")]
        public async Task<IActionResult> GetFacilityAdmins(string exfacilityId)
        {
            GetUsersSearchBriefResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetFacilityAdmins(clnt, exfacilityId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

       
        [HttpGet("GetRelationshipList")]
        public async Task<IActionResult> GetRelationshipList()
        {
            var br = new string[] { "Son", "Daughter", "Brother", "Sister", "Niece", "Nephew", "Cousin", "Friend", "Other" };
            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetReports/{exfacilityId}")]
        public async Task<IActionResult> GetReports(string exfacilityId)
        {
            GetHTReportsResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetReports(clnt, exfacilityId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertAssignableFacilityDevice")]
        public async Task<IActionResult> UpsertAssignableFacilityDevice([FromBody] UpsertFacilityDevicesRequest req)
        {
            data.common.baseResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UpsertAssignableFacilityDevice(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertFacilityCustomStyles")]
        public async Task<IActionResult> UpsertFacilityCustomStyles ([FromBody] UpsertCustomStylesRequest req)
        {
            UpsertCustomStylesResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UpsertFacilityCustomStyles(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetFacilityCustomStyles/{exfacilityId}")]
        public async Task<IActionResult> GetFacilityCustomStyles(string exfacilityId)
        {
            GetFacilityCustomStyleResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetFacilityCustomStyles(clnt, exfacilityId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetLiveFacilitiesDetails/{userExId}")]
        public async Task<IActionResult> GetLiveFacilitiesDetails(string userExId)
        {
            GetFacilityDetailsResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetLiveFacilitiesDetails(clnt, userExId);

                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }
    }
}
