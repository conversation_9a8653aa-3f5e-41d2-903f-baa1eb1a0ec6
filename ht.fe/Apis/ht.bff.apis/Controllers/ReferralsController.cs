﻿using bt.bff.common;
using bt.bff.common.Models;
using ht.bff.apis.Services;
using ht.data.common.Tests;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.bff.apis.Extensions;
using ht.data.common;
using System.Net.Http;
using Microsoft.AspNetCore.Authorization;

namespace ht.bff.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ReferralsController : ControllerBase
    {
        HTBEReferralApiService _svc;
        private readonly IHttpClientFactory _clientFactory;
        private readonly HttpClient _client;
        public ReferralsController(HTBEReferralApiService svc, IHttpClientFactory clientFactory)
        {
            _svc = svc;
            this._clientFactory = clientFactory;
            this._client = _clientFactory.CreateClient("ht-bff-client");
        }

        /// <summary>
        /// To get the list of Referrals for the resident
        /// </summary>
        /// <remarks>
        /// More elaborate description
        /// </remarks>
        [Authorize]
        [HttpPost("GetReferrals")]
        public async Task<IActionResult> GetReferrals([FromBody] GetReferralRequest req)
        {
            GetReferralResponse br = new();

            try
            {

                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetReferrals(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpGet("GetReferralDetails/{exreferralId}")]
        public async Task<IActionResult> GetReferralDetails(string exreferralId)
        {
            GetReferralResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(new baseRequest(), true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.GetReferralDetails(clnt, exreferralId);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("UpsertReferrals")]
        public async Task<IActionResult> UpsertReferrals([FromBody] UpsertReferralsRequest req)
        {
            UpsertReferralsResponse br = new();

            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.UpsertReferrals(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return (new JsonResult(br));
        }

        [Authorize]
        [HttpPost("SendtoSpecialist")]
        public async Task<IActionResult> SendtoSpecialist([FromBody] SendtoSpecialistRequest req)
        {
            SendtoSpecialistResponse br = new();
            try
            {
                var tok = HttpContext.Request.GetFacilityAndToken(req, true);
                var clnt = await Settings.BE_API_AuthClient.GetHttpClientFactoryAsync(tok, _client);
                var res = await _svc.SendtoSpecialist(clnt, req);
                clnt = null;
                return (new JsonResult(res));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

    }
}
