#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

#Depending on the operating system of the host machines(s) that will build or run the containers, the image specified in the FROM statement may need to be changed.
#For more information, please see https://aka.ms/containercompat

FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app
EXPOSE 4040
EXPOSE 443

ENV HT_KV_URI=https://ht-kv-aue-demo.vault.azure.net/
ARG CONFIGURATION
ENV CONFIGURATION=$CONFIGURATION
RUN echo "Build Configuration: ${CONFIGURATION}"

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src

COPY ["ht.fe/Apis/ht.bff.apis/ht.bff.apis.csproj", "ht.fe/Apis/ht.bff.apis/"]
COPY ["ht.fe/Common/bt.bff.common/bt.bff.common.csproj", "ht.fe/Common/bt.bff.common/"]
RUN curl -L https://raw.githubusercontent.com/Microsoft/artifacts-credprovider/master/helpers/installcredprovider.sh  | sh
ARG FEED_ACCESSTOKEN
ENV VSS_NUGET_EXTERNAL_FEED_ENDPOINTS \
    "{\"endpointCredentials\": [{\"endpoint\":\"https://pkgs.dev.azure.com/healthteams/_packaging/healthteams/nuget/v3/index.json\", \"password\":\"${FEED_ACCESSTOKEN}\"}]}"
COPY ["Nuget.config", "."]
RUN dotnet restore "ht.fe/Apis/ht.bff.apis/ht.bff.apis.csproj" --configfile "Nuget.config"
COPY . .
WORKDIR "/src/ht.fe/Apis/ht.bff.apis"


RUN dotnet build "ht.bff.apis.csproj" -c "${CONFIGURATION}" -o /app/build

FROM build AS publish
RUN dotnet publish "ht.bff.apis.csproj" -c "${CONFIGURATION}" -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ht.bff.apis.dll"]