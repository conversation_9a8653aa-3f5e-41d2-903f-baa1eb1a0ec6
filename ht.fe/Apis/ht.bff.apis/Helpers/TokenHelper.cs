﻿
using Microsoft.IdentityModel.Tokens;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Linq;
using System.Collections.Generic;

namespace ht.bff.apis.Helpers;
public class TokenHelper
{
	public string _signingKey { get; set; }
	public SymmetricSecurityKey _secretKey {  get; set;}

	public long _secsToLive = 1800;

    public TokenHelper(string key="")
    {
		if (key != null)
		{
			_signingKey = key;
			_secretKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(key));
		}
	}
	public TokenHelper(string key, long secsToLive=1800)
	{
		_signingKey = key;
		_secretKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(key));
		_secsToLive = secsToLive;	
		
	}
	public string GenerateToken(int userId)
	{
		var mySecret = "asdv234234^&%&^%&^hjsdfb2%%%";
		var mySecurityKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(mySecret));

		var myIssuer = "http://mysite.com";
		var myAudience = "http://myaudience.com";

		var tokenHandler = new JwtSecurityTokenHandler();
		var tokenDescriptor = new SecurityTokenDescriptor
		{
			Subject = new ClaimsIdentity(new Claim[]
			{
				new Claim(ClaimTypes.NameIdentifier, userId.ToString())
			}),
			Expires = DateTime.UtcNow.AddDays(7),
			Issuer = myIssuer,
			Audience = myAudience,
			SigningCredentials = new SigningCredentials(mySecurityKey,
				SecurityAlgorithms.HmacSha256Signature)
		};

		var token = tokenHandler.CreateToken(tokenDescriptor);
		return tokenHandler.WriteToken(token);
	}

	public ClaimsPrincipal ValidateToken(string tok)
    {
		var tokenHandler = new JwtSecurityTokenHandler();
		var tokParams = new TokenValidationParameters
		{
			ValidateAudience = false,
			ValidateIssuer = false,
			ValidateLifetime = false
		};

		try
		{
			var principal = tokenHandler.ValidateToken(tok, tokParams, out var securityToken);
			if (!(securityToken is JwtSecurityToken jwtSecurityToken) || !jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
			{
				var x = 5;
			}
			return principal;
		}
		catch (Exception ex)
		{
			throw;
		}
    }

	public string ReturnNewSecurityToken(string tok)
	{
		var tokenHandler = new JwtSecurityTokenHandler();

		var jwt = tokenHandler.ReadJwtToken(tok);
		var _audience = jwt?.Audiences.First();

		var tokParams = new TokenValidationParameters
		{
			ValidateAudience = false,
			ValidateIssuer = false,
			ValidateLifetime = false
		};

		try
		{
			List<Claim> claims = new List<Claim>();
			claims.AddRange(jwt.Claims);
			claims.Add(new Claim(ClaimTypes.Role, "Doctor"));
			

			var token = new JwtSecurityToken(
					issuer: "https://localhost:5001", //jwt.Issuer,
					audience: "https://apisbffuat.healthteams.com.au",//_audience,
					claims: claims,
					expires: DateTime.Now.AddHours(1)
				);


			return (tokenHandler.WriteToken(token));
		}
		catch (Exception ex)
		{
			throw;
		}
	}
}
