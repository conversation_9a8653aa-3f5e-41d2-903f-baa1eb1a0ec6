﻿using bt.bff.common.Models;

using ht.be.apis.Extensions;
using ht.data.common;
using ht.data.common.Extensions;

using Microsoft.AspNetCore.Http;
using Microsoft.IdentityModel.Tokens;

using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Text.RegularExpressions;
using System.Text;

namespace ht.bff.apis.Extensions;

public static class HttpRequestExtension
{
    public static readonly string HDR_ExFacilityId = "x-exfacilityid";
    public static string GetToken(this HttpRequest req)
    {
        try
        {
            return req?.Headers["Authorization"].FirstOrDefault()?.Replace("Bearer ", "");
        }
        catch
        {
            return null;
        }
    }

    public static Tuple<string, string> GetFacilityAndToken(this HttpRequest req, baseRequest request, bool bThrowOnMissing = false)
    {
        try
        {
            var tok = req?.Headers["Authorization"].FirstOrDefault()?.Replace("Bearer ", "");
            var facId = req?.Headers[HDR_ExFacilityId].FirstOrDefault() ?? request?.GetExFacilityId();
            if (bThrowOnMissing && string.IsNullOrEmpty(facId))
                throw new MissingFieldException("ERROR: exFacilityId is not found. Required.");

            return (new Tuple<string, string>(tok, facId));
        }
        catch (Exception ex)
        {
            if (bThrowOnMissing)
                throw;
            else //swallow the error
                return null;
        }
    }

    public static string GetClientToken(this HttpRequest req, string hdrKey = "x-client-tok")
    {
        var hdrVal = req.Headers[hdrKey].FirstOrDefault()?.Replace("Bearer ", "");
        return (hdrVal);
    }

    public static JwtSecurityToken ExtractClientToken(this HttpRequest req, string hdrKey = "x-client-tok")
    {
        var tok = GetClientToken(req, hdrKey);
        var jwt = new JwtSecurityTokenHandler();

        var token = jwt.ReadJwtToken(tok);
        return (token);

    }

    public static JwtSecurityToken ExtractClientToken(string tok)
    {
        //var jwt = new JwtSecurityTokenHandler();
        var jwt = new MyJwtSecurityTokenHandler();
        if (jwt.CanReadToken(tok))
        {
            var token = jwt.ReadJwtToken(tok);
            return (token);
        }
        else
            throw new SecurityTokenInvalidTypeException("JWT error - cannot read inbound token. Possibly malformed.");


    }

    public static string ExtractClientUserExId(this HttpRequest req, string hdrKey = "x-client-tok")
    {
        var tok = ExtractClientToken(req, hdrKey);
        return tok.Subject;

    }

    public static JwtUserAndRoles ExtractClientUserRoles(this HttpRequest req, string hdrKey = "x-client-tok")
    {
        var br = new JwtUserAndRoles();
        var tok = ExtractClientToken(req, hdrKey);
        var id = tok.Subject;

        foreach (var c in tok.Claims)
        {
            if (c.Type == "userRoles")
            {
                string val = c.Value;
                br = HTJsonSerialiser.Deserialise<JwtUserAndRoles>(val);
                break;
            }
        }



        return (br);



    }

    public static JwtUserAndRoles ExtractClientUserRoles(this HttpRequest req, string token, bool bUseToken)
    {
        var br = new JwtUserAndRoles();
        var tok = ExtractClientToken(token);
        var id = tok.Subject;

        foreach (var c in tok.Claims)
        {
            if (c.Type == "userRoles" || c.Type == "ht_roles")
            {
                string val = c.Value;
                if(Regex.IsMatch(val, @"^[a-zA-Z0-9\+/]*={0,2}$"))
                {
                    val = Encoding.UTF8.GetString(Convert.FromBase64String(val));
                }
                br = HTJsonSerialiser.Deserialise<JwtUserAndRoles>(val);
                break;
            }
        }



        return (br);



    }
}
