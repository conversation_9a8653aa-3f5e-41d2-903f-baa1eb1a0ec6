﻿using ht.data.common.Telehealth;
using ht.data.common.Users;

using Swashbuckle.AspNetCore.Filters;

using System;
using System.Collections.Generic;

namespace ht.bff.apis.Examples;

public class UpsertResidentRequestExample : IExamplesProvider<UpsertResidentRequest>
{

    public UpsertResidentRequest GetExamples()
    {
        return new UpsertResidentRequest
        {
            resident = new ResidentUser
            {


                FirstName = "Brunhild",
                LastName = "Heston",
                ProfilePicUrl = "https://htappuat.blob.core.windows.net/users/335D7BD0-4CEE-4E21-81B6-00621582ADCD/profile/profilepic.jpg?sv=2018-03-28&sr=b&sig=SpL1%2BFuKA%2Bfmu6ZgbC0UNh5y2rX9eP4Gnaw%2Fn%2F7IMFk%3D&se=6105-03-12T11%3A22%3A28Z&sp=r",
                DOB = System.DateTime.Now.AddYears(-60),
                MobileCC = "+61",
                Mobile = "**********",
                Email = "<EMAIL>",
                ResidentUnitNo = "1",
                MedicareNumber = "9996 3617 2262",
                MedicarePosNo = "10",
                MedicareExp = System.DateTime.Now.AddYears(5),
                TelehealthPreferences = new TelehealthUserPreferences
                {
                    ContactPref = "Mobile",
                    Device = "iPad",
                    Email = "<EMAIL>",
                    Mobile = "0411 222 333",
                    MobileCC="+61"
                },
                Summary = new HealthSummary
                {
                    ChronicDiseases = new List<string>
                    {
                        "Cardiovascular","Arthritis"
                    },
                    Allergies = new List<string>
                    {
                        "Pencilin"
                    },
                    FamilyHistory = new List<string>
                    {
                        "High Blood Pressure"
                    },
                    Medications = new List<string>
                    {
                         "Roximosfan",
                         "Dexoradisan"
                    }


                },

                ExCarerId = Guid.NewGuid().ToString(),
                Carer = new data.common.Users.HealthTeamsUser
                {
                    FullName = "Tristan Hunt",
                    Email = "<EMAIL>",
                    Mobile = "0433 444 555",
                    TelehealthPreferences = new TelehealthUserPreferences
                    {
                        ContactPref = "Mobile",
                        Email = "<EMAIL>",
                        Mobile = "0433 444 555",
                        Device = "iPad",
                        MobileCC = "+61"
                    }
                },
                ExDoctorId = System.Guid.NewGuid().ToString(),
                GP = new data.common.Users.HealthTeamsUser
                {
                    FullName = "Jackson Marvoritz",
                    Email = "<EMAIL>",
                    ProviderNumber = "38392729",
                    HealthLinkEDI = "22939372"
                },

                Specialist = new data.common.Users.HealthTeamsUser
                {
                    FullName = "Dr. ABC Hunt",
                    Email = "<EMAIL>",
                    ProviderNumber = "38392729",
                    HealthLinkEDI = "22939372"
                },
                EnduringGuardian = new data.common.Users.HealthTeamsUser
                {
                    FullName = "Jacinta Borgen",
                    Email = "<EMAIL>",
                    MobileCC = "+61",
                    Mobile = "0400 607 893"
                },
                FamilyMembers = new List<data.common.Users.HealthTeamsUser>
                {
                 new data.common.Users.HealthTeamsUser
                {
                    FullName = "Jacinta Borgen",
                    Email = "<EMAIL>",
                    MobileCC = "+61",
                    Mobile = "0433 444 555",
                    
                    Relationship="son",
                    TelehealthPreferences = new TelehealthUserPreferences
                    {
                        ContactPref = "Mobile",
                        Email = "<EMAIL>",
                        Mobile = "0433 444 555",
                        Device = "iPad"
                    }
                }
            }

            }
        };
    }
}

