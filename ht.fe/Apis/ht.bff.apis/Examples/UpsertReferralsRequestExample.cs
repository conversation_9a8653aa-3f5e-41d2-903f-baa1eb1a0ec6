﻿using ht.data.common.Telehealth;
using ht.data.common.Users;
using Swashbuckle.AspNetCore.Filters;

namespace ht.bff.apis.Examples;

public class UpsertReferralsRequestExample : IExamplesProvider<UpsertReferralsRequest>
{
    
    public UpsertReferralsRequest GetExamples()
    {
        return new UpsertReferralsRequest
        {
            Referral = new TelehealthReferral
            {
                ProviderId = System.Guid.NewGuid().ToString(),
                ProviderName = "Dr. Madhav Mojad",
                ReferredforId = System.Guid.NewGuid().ToString(),
                Referredfor="Resident One",
                ReferToId = System.Guid.NewGuid().ToString(),
                ReferredTo = "Dr. Mick B",
                ReferralDateUtc = System.DateTime.UtcNow,
                Type = "Geriatrician Referral",
                ReferralDetails = "New referral details of Resident one",
                RefStatus = "Acknowledged"
               
            }
        };
    }
}

