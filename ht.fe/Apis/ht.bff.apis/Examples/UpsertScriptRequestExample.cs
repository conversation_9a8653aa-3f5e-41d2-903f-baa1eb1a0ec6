﻿using ht.data.common.Telehealth;
using ht.data.common.Users;
using Swashbuckle.AspNetCore.Filters;

using System.Collections.Generic;

namespace ht.bff.apis.Examples;

public class UpsertScriptRequestExample : IExamplesProvider<UpsertScriptsRequest>
{
    
    public UpsertScriptsRequest GetExamples()
    {
        return new UpsertScriptsRequest
        {
            Script = new TelehealthScript
            {
                ExDoctorId = System.Guid.NewGuid().ToString(),
                ExResidentId= System.Guid.NewGuid().ToString(),
                ExpiryUtc = System.DateTime.UtcNow,
                ScriptDateUtc = System.DateTime.UtcNow,
                PrescriptionName= "Prescription 1123",
                PrescribingDoctor = "Dr. Mick B",
                Prescriptions = new List<PrescriptionItem>
                {
                    new PrescriptionItem
                    {
                        Item ="Roximosfan",
                        Strength="50ug",
                        Frequency="Daily",
                        Duration="1 week",
                        Repeats="1",
                        Instructions="With metals"
                    },
                    new PrescriptionItem
                    {
                        Item ="Dexoradison",
                        Strength="100ug",
                        Frequency="Twice Daily",
                        Duration="1 week",
                        Repeats="0",
                        Instructions=""
                    }
                },
                ScriptStatus = "Unfilled",

            }
        };
    }
}

