﻿using ht.data.common.Telehealth;
using ht.data.common.Users;

using Swashbuckle.AspNetCore.Filters;

namespace ht.bff.apis.Examples;

public class UpsertNoteRequestExample : IExamplesProvider<UpsertNotesRequest>
{

    public UpsertNotesRequest GetExamples()
    {
        return new UpsertNotesRequest
        {
            Note = new TelehealthProgressNote
            {
                ExDoctorId = System.Guid.NewGuid().ToString(),
                ExResidentId = System.Guid.NewGuid().ToString(),
                ApptDateUtc = System.DateTime.UtcNow,
                NoteDateUtc = System.DateTime.UtcNow,
                Title = "New Note for Resident one",
                NoteDetails = "Please use this sample note. Thank you.",
                SentTo = "Family",
                ExFacilityId = System.Guid.NewGuid().ToString()
            }
        };
    }
}

