﻿using ht.data.common.Tasks;
using ht.data.common.Users;
using Swashbuckle.AspNetCore.Filters;
using System;

namespace ht.bff.apis.Examples;

public class GetUsersSearchRequestExample : IExamplesProvider<GetUsersSearchRequest>
{
    
    public GetUsersSearchRequest GetExamples()
    {
        return new GetUsersSearchRequest
        {
             ExFacilityId=Guid.NewGuid().ToString(),
             Role="Doctor",
             SearchText = "Mi"
        };
    }
}

