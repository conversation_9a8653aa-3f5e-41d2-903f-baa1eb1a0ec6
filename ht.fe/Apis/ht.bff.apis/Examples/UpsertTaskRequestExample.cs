﻿using ht.data.common.Tasks;
using ht.data.common.Users;
using Swashbuckle.AspNetCore.Filters;

namespace ht.bff.apis.Examples;

public class UpsertTaskRequestExample : IExamplesProvider<UpsertTaskRequest>
{
    
    public UpsertTaskRequest GetExamples()
    {
        return new UpsertTaskRequest
        {
            Task = new HealthTeamsTask
            {
                TaskDateTimeUtc = System.DateTime.UtcNow,
                TaskLocalLocale = "AUS Eastern Standard Time",
                TaskName = "Telehealth appointment for <PERSON> with <PERSON><PERSON> <PERSON>",
                AssignedToId = System.Guid.NewGuid().ToString(),
                AssignedToName = "Nurse Ratchet",
                TaskType = TaskTypes.Telehealth,
                TaskStatus= "Pending",
                TaskDescription = "This is an EXAMPLE of a CREATE TASK",
                ExResidentId = System.Guid.NewGuid().ToString(),
                ExDoctorId = System.Guid.NewGuid().ToString(),
                VitalsToCapture = new System.Collections.Generic.List<string>
                {  "BP","HR","SPO2","Heart","Lungs","Ear","Throat","Cough","Glucose"},
                ExFacilityId = System.Guid.NewGuid().ToString(),
                IsVitalsRequired = false,
                TaskRepeats = new TaskRepeats
                {
                     StartUtc = System.DateTime.UtcNow,
                     EndUtc = System.DateTime.UtcNow.AddYears(3),
                     LocaleZone= "AUS Eastern Standard Time",
                     TaskFrquency="Daily/Weekly",
                     TaskWeekOfMonth="First",
                     WeekDays = new System.Collections.Generic.List<TaskRepeatDay>
                     {
                         new TaskRepeatDay
                         {
                              Day = 1,
                              DaySlots = new System.Collections.Generic.List<int>
                              {
                                  1,3,5
                              }
                         },
                         new TaskRepeatDay
                         {
                              Day = 3,
                              DaySlots = new System.Collections.Generic.List<int>
                              {
                                  2,4,6
                              }
                         },
                         new TaskRepeatDay
                         {
                              Day = 5,
                              DaySlots = new System.Collections.Generic.List<int>
                              {
                                  1,3,5
                              }
                         },
                     }
                }

            }
        };
    }
}

