﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>HT Meeting Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/ui/1.13.1/jquery-ui.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.5.17/dist/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@aspnet/signalr@1.1.2/dist/browser/signalr.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@0.18.0/dist/axios.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.3.7/dist/css/bootstrap.min.css" integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.3.7/dist/js/bootstrap.min.js" integrity="sha384-Tc5IQib027qvyjSMfHjOMaLkfuWVxZxUPnCJA7l2mCWNIpG9mGCD8wGNIcPD7Txa" crossorigin="anonymous"></script>
    <link href="https://getbootstrap.com/docs/3.3/examples/jumbotron/jumbotron.css" rel="stylesheet">
    <link href="https://getbootstrap.com/docs/3.3/examples/sticky-footer-navbar/sticky-footer-navbar.css" rel="stylesheet">

</head>
<body>

    <nav class="navbar navbar-inverse navbar-fixed-top">
      <div class="container">
        <div class="navbar-header">
          <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false" aria-controls="navbar">
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
          <a class="navbar-brand" href="#">Health Teams Group Calling Demo</a>
        </div>
        <div id="navbar" class="navbar-collapse collapse navbar-right">
          <form class="navbar-form">
            <div class="form-group">
                <input type="text" placeholder="Name" id="txtName" class="form-control">
                <input type="text" placeholder="UserExId" id="userExId" class="form-control">
                <button type="button" class="btn btn-success" onclick="joinmeeting();">Join Meeting</button>
            </div>
          </form>
          <ul class="nav navbar-nav">
              <li class="dropdown">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Role <span class="caret"></span></a>
                <ul class="dropdown-menu">
                  <li><a href="#">Doctor</a></li>
                  <li><a href="#">Nurse</a></li>
                  <li><a href="#">Family</a></li>
                  <li role="separator" class="divider"></li>
                  <li class="dropdown-header">Resident Options</li>
                  <li><a href="#">Resident</a></li>
                  <li><a href="#">Resident and Nurse together</a></li>
                </ul>
              </li>
            </ul>
        </div><!--/.navbar-collapse -->
      </div>
    </nav>

    <div class="container">
      <!-- Example row of columns -->
      <div class="row">

        <div class="col-md-8">
            <div class="row">
                <div class="col-md-6">
                      <h2>Doctor</h2>
                      <p>Donec id elit non mi porta gravida at eget metus. Fusce dapibus, tellus ac cursus commodo, tortor mauris condimentum nibh, ut fermentum massa justo sit amet risus. Etiam porta sem malesuada magna mollis euismod. Donec sed odio dui. </p>
                      <p><a class="btn btn-default" href="#" role="button">View details &raquo;</a></p>
                </div>
                <div class="col-md-6">
                    <h2>Nurse</h2>
                    <p>Donec id elit non mi porta gravida at eget metus. Fusce dapibus, tellus ac cursus commodo, tortor mauris condimentum nibh, ut fermentum massa justo sit amet risus. Etiam porta sem malesuada magna mollis euismod. Donec sed odio dui. </p>
                    <p><a class="btn btn-default" href="#" role="button">View details &raquo;</a></p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <h2>Family</h2>
                    <p>Donec sed odio dui. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Vestibulum id ligula porta felis euismod semper. Fusce dapibus, tellus ac cursus commodo, tortor mauris condimentum nibh, ut fermentum massa justo sit amet risus.</p>
                    <p><a class="btn btn-default" href="#" role="button">View details &raquo;</a></p>
                </div>
                <div class="col-md-4">
                    <h2>Resident</h2>
                    <p>Donec sed odio dui. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Vestibulum id ligula porta felis euismod semper. Fusce dapibus, tellus ac cursus commodo, tortor mauris condimentum nibh, ut fermentum massa justo sit amet risus.</p>
                    <p><a class="btn btn-default" href="#" role="button">View details &raquo;</a></p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
          <h2>Side Bar</h2>
          <p>Donec sed odio dui. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Vestibulum id ligula porta felis euismod semper. Fusce dapibus, tellus ac cursus commodo, tortor mauris condimentum nibh, ut fermentum massa justo sit amet risus.</p>
          <p><a class="btn btn-default" href="#" role="button">View details &raquo;</a></p>
        </div>
      </div>
      <hr>

      
    </div> <!-- /container -->
     <footer class="footer">
      <div class="container">
         <div class="panel panel-warning">
            <div class="panel-heading">
              <h3 class="panel-title">Events</h3>
            </div>
            <div class="panel-body" id="asyncmsgs">
              
            </div>
          </div>
      </div>
    </footer>







    <script>

        //Will-just playing with a few extra config values.
        //these are never on the client - the token is fetched via an API call - I'm sorting a few things out.
        //const connstr = "Endpoint=https://kol-dev.service.signalr.net;AccessKey=uKLgYeluGBwVHKMyTvx/dDrm+l9KacINsehcCVX/Vzk=;Version=1.0;";
        const tok = "eyJhbGciOiJIUzI1NiIsImtpZCI6Ii05MDcyMzE4OTciLCJ0eXAiOiJKV1QifQ.eyJhc3JzLnMudWlkIjoiMzM1RDdCRDAtNENFRS00RTIxLTgxQjYtMDA2MjE1ODJBRENEIiwiYXNycy5zLmF1dCI6IldlYkpvYnNBdXRoTGV2ZWwiLCJuYmYiOjE2NDYwMjQ0MTUsImV4cCI6MTY0NjAyODAxNSwiaWF0IjoxNjQ2MDI0NDE1LCJhdWQiOiJodHRwczovL2h0ZXZlbnRzZGV2LnNlcnZpY2Uuc2lnbmFsci5uZXQvY2xpZW50Lz9odWI9aHRldmVudHMifQ.0Q-iil97IE0C9mMJCYm9eb3l5HP2uUMYwH7-eEzqQpg";

        const baseUrl = "https://hteventsdev.service.signalr.net/client/?hub=htevents";

        const protocol = new signalR.JsonHubProtocol();
        const transport = signalR.HttpTransportType.LongPolling;
        const options = {
            transport,
            logMessageContent: true,
            logger: signalR.LogLevel.Trace,
            //skipNegotiation: true,
            accessTokenFactory: () => tok
        };

        var connection= null;
        
        function makeSignalRConnection() {

            connection= new signalR.HubConnectionBuilder()
                .withUrl(baseUrl, options)
                //.withAutomaticReconnect()
                .withHubProtocol(protocol)
                .configureLogging(signalR.LogLevel.Information)
                .build();

            console.log("connecting....");

            //var hub = connection.createHubProxy('htevents');
            connection.on("ClientNotify", (target, status, data) => {
                console.log('We got ', target, status, data);
            });

            connection.start()
                .then((response) => {
                    console.log('connection established ', response);
                });
        }

        function joinmeeting() {
            var role = $('.dropdown-menu li a.active').text();
            if ((role == null) || (role = '')) {
                logMsg('failed');
                alert('Please select a role');
                exit;
            }
            
        }

        $(".dropdown-menu li a").click(function () {
            var selText = $(this).text();///User selected value...****     
            $(this).addClass('active');
            $(this).parents('.btn-group').find('.dropdown-toggle').html(selText + ' <span class="caret"></span>');
        });

        function logMsg(msg) {
            $('#asyncmsgs').text(msg);
        }

    </script>
</body>
</html>