﻿using ht.bff.apis.Services;
using ht.data.common.Dashboards;
using ht.data.common.FacilitySetup;
using ht.data.common.Shared;
using ht.data.common.Users;
using Microsoft.AspNetCore.Mvc.Testing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Bff.APIs.Test
{
    public class TestFacilityController : TestFixture
    {
        private Mock mockapiService;
        private Mock mockSignalRService;
        private Mock mockEmailVerificationService;
        string token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjQ5MkMxRDdDMzg2QjZCNzg5MEE4QThDNjFGRUIxNEYyRDhGOTVFQUEiLCJ4NXQiOiJTU3dkZkRocmEzaVFxS2pHSC1zVTh0ajVYcW8ifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PdoFLyJp25RmZ7RCwwcvNLXyldzw_E7s2t8JB6tQrmAFt9IhvvOieCAyCvqfWe0cgAsn8oZAQcCStFFU3zA195evgKhuUtXp6og_iTSl5tOevAWzKVNL_qEyT5anRg3jDwSpQZICg-fcqS1ZeFUijVqZZkcfZPs-mdGSlopBvjXvhRY8CBnOcsdOzXLSdocUNTI1retIEcVxnTWyH6lUKe_x3OYNlJMKVcxW1NGUWXDbzn-cXzHjF7xHCm7erp-90q-7bYvZXLd1PS2D7rdFemaVrbIXMvbBf6goUcharaLtbN0Ifk8JRVegTxroDvc6WiYG7FVMzYfcgm_efE8-jg";

        public TestFacilityController(WebApplicationFactory<Startup> factory) : base(factory)
        {
            mockapiService = new Mock<HTBEApiService>();
            mockSignalRService = new Mock<SignalRService>();
            mockEmailVerificationService = new Mock<EmailVerificationService>();

        }

        [Fact]
        public async Task TestGetDashboard()
        {
            var content = JsonContent.Create<GetFacilityDashboardRequest>(new GetFacilityDashboardRequest
            {
                CurrentDate = System.DateTime.UtcNow,
                CurrentEndDate = System.DateTime.UtcNow,
                CurrentMonth = System.DateTime.UtcNow.Month.ToString(),
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                GetTaskDetails = true
            });

            var resp = await _client.PostAsync("/api/Facility/GetDashboard", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestUpsertFacility()
        {
            var content = JsonContent.Create<UpsertFacilityRequest>(new UpsertFacilityRequest
            {
                Facility = new HealthTeamsFacility
                {
                    LastModifiedUtc = System.DateTime.UtcNow.ToString(),
                    FacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    FacilityName = "Facility Name",
                    Description = "Facility Description",
                    Beds = 3,
                    DefaultPharmacy = "Default Pharmacy",
                    DefaultExPharmacyId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    FacilityManager = "Facility Manager",
                    FacilityExManagerId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    ManagerEmail = "<EMAIL>",
                    CountryCode = "62",
                    MobileNo = "**********",
                    ExAddressId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    Address = new Address
                    {
                        ExAddressId = "01",
                        LastModifiedUtc = DateTime.UtcNow,
                        BuildingName = "Tower byte",
                        Street1 = "56 Arthur Street",
                        City = "Randwick",
                        State = "nsw",
                        PostCode = "2000",
                        Country = "aust",
                        Long = "Unit 06",
                        Lat = "Unit 06",
                        MeshblockId = "01"
                    },
                    IsParentFacility = false,
                    ParentFacilityName = "Parent Facility Name",
                    ParentExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    MainPhone = "**********",
                    WebsiteUrl = "https://"
                }
            });

            var resp = await _client.PostAsync("/api/Facility/UpsertFacility", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }


        [Fact]
        public async Task TestGetFacilities()
        {
            var content = JsonContent.Create<GetFacilitiesRequest>(new GetFacilitiesRequest
            {
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"
            });

            var resp = await _client.PostAsync("/api/Facility/GetFacilities", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetFacility()
        {
            var content = JsonContent.Create<GetFacilityRequest>(new GetFacilityRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"
            });

            var resp = await _client.PostAsync("/api/Facility/GetFacility", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetFacilityDevices()
        {
            var content = JsonContent.Create<GetFacilityDevicesRequest>(new GetFacilityDevicesRequest
            {
                // FacilityId = 12345,
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"
            });

            var resp = await _client.PostAsync("/api/Facility/GetSelectedFacilityDevices", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task UpsertFacilityDevicesTest()
        {
            var content = JsonContent.Create<UpsertFacilityDevicesRequest>(new UpsertFacilityDevicesRequest
            {
                FacilityDevices = new List<HealthTeamsFacilityDevice>()
                {
                    new HealthTeamsFacilityDevice()
                    {
                         DateModifiedUtc = System.DateTime.UtcNow.ToString(),
                    ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    Name = "Facility Name",
                    Description = "Facility Description",
                    PartnerName = "Default Pharmacy",
                    DeviceId = 1,
                   
                    }
                }
            });

            var resp = await _client.PostAsync("/api/Facility/GetDashboard", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetFacilityDevicesTest()
        {
            var content = JsonContent.Create<GetFacilityDevicesRequest>(new GetFacilityDevicesRequest
            {
                // FacilityId = 12345,
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"
            });

            var resp = await _client.PostAsync("/api/Facility/GetSelectedFacilityDevices", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task GetParentFacilitiesTest()
        {
            var content = JsonContent.Create<GetFacilitiesRequest>(new GetFacilitiesRequest
            {
                // FacilityId = 12345,
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter=null,
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"
            });

            var resp = await _client.PostAsync("/api/Facility/GetParentFacilities", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task GetPharmaciesTest(string exfacilityId)
        {
          
            var resp = await _client.GetAsync($"/api/Facility/GetPharmacies/{exfacilityId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task UpsertPharmacyTest()
        {
            var content = JsonContent.Create<UpsertPharmacyRequest>(new UpsertPharmacyRequest
            {
                // FacilityId = 12345,
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
                Pharmacy = new HealthTeamsPharmacy()
                {
                    ExAddressId = "01",
                    PharmacyName = "Tower byte",
                    Address = new Address
                    {
                        ExAddressId = "01",
                        LastModifiedUtc = DateTime.UtcNow,
                        BuildingName = "Tower byte",
                        Street1 = "56 Arthur Street",
                        City = "Randwick",
                        State = "nsw",
                        PostCode = "2000",
                        Country = "aust",
                        Long = "Unit 06",
                        Lat = "Unit 06",
                        MeshblockId = "01"
                    },

                }
            });

            var resp = await _client.PostAsync("/api/Facility/UpsertPharmacy", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task GetFacilityAdminsTest(string exfacilityId)
        {

            var resp = await _client.GetAsync($"/api/Facility/GetFacilityAdmins/{exfacilityId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task GetRelationshipListTest()
        {

            var resp = await _client.GetAsync($"/api/Facility/GetRelationshipList");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task GetReportsTest(string exfacilityId)
        {

            var resp = await _client.GetAsync($"/api/Facility/GetReports/{exfacilityId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task GetFacilityCustomStylesTest(string exfacilityId)
        {

            var resp = await _client.GetAsync($"/api/Facility/GetFacilityCustomStyles/{exfacilityId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task UpsertFacilityCustomStylesTest()
        {
            var content = JsonContent.Create<UpsertCustomStylesRequest>(new UpsertCustomStylesRequest
            {
                // FacilityId = 12345,
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
                StyleJSON=null
            });

            var resp = await _client.PostAsync("/api/Facility/UpsertFacilityCustomStyles", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
    }
}
