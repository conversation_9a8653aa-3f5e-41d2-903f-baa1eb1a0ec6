﻿using ht.bff.apis.Services;
using Microsoft.AspNetCore.Mvc.Testing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Bff.APIs.Test
{
    public class TestScriptController : TestFixture
    {
        private Mock HTBEScriptsApiService;

        string token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjQ5MkMxRDdDMzg2QjZCNzg5MEE4QThDNjFGRUIxNEYyRDhGOTVFQUEiLCJ4NXQiOiJTU3dkZkRocmEzaVFxS2pHSC1zVTh0ajVYcW8ifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PdoFLyJp25RmZ7RCwwcvNLXyldzw_E7s2t8JB6tQrmAFt9IhvvOieCAyCvqfWe0cgAsn8oZAQcCStFFU3zA195evgKhuUtXp6og_iTSl5tOevAWzKVNL_qEyT5anRg3jDwSpQZICg-fcqS1ZeFUijVqZZkcfZPs-mdGSlopBvjXvhRY8CBnOcsdOzXLSdocUNTI1retIEcVxnTWyH6lUKe_x3OYNlJMKVcxW1NGUWXDbzn-cXzHjF7xHCm7erp-90q-7bYvZXLd1PS2D7rdFemaVrbIXMvbBf6goUcharaLtbN0Ifk8JRVegTxroDvc6WiYG7FVMzYfcgm_efE8-jg";

        public TestScriptController(WebApplicationFactory<Startup> factory) : base(factory)
        {
            HTBEScriptsApiService = new Mock<HTBEScriptsApiService>();
        }

        [Fact]
        public async Task TestGetScripts()
        {
            var content = JsonContent.Create<GetScriptsRequest>(new GetScriptsRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                FromDateUtc = DateTime.UtcNow,
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
            });
            var resp = await _client.PostAsync($"/api/Scripts/GetScripts", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task TestGetScriptDetails(string scriptId)
        {


            var resp = await _client.GetAsync($"/api/Scripts/GetScriptDetails/{scriptId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestUpsertScripts()
        {
            var content = JsonContent.Create<TelehealthScript>(new TelehealthScript
            {
                ExScriptId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Prescriptions = new List<PrescriptionItem>
                {
                    new PrescriptionItem{
                        Item ="Roximosfan",
                        Strength="50ug",
                        Frequency="Daily",
                        Duration="1 week",
                        Repeats="1",
                        Instructions="With metals"
                    },
                    new PrescriptionItem
                    {
                        Item ="Dexoradison",
                        Strength="100ug",
                        Frequency="Twice Daily",
                        Duration="1 week",
                        Repeats="0",
                        Instructions=""
                    }
                },
                ExpiryUtc = DateTime.UtcNow

            });

            var resp = await _client.PostAsync("/api/Scripts/UpsertScripts", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

    }
}
