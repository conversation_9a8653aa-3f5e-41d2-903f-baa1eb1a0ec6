<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="6.0.2" />
	  <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.9.0" />
	  <PackageReference Include="coverlet.collector" Version="6.0.2">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.8.0" />
	  <PackageReference Include="xunit" Version="2.8.0" />
	  <PackageReference Include="xunit.runner.visualstudio" Version="2.8.0">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="Moq" Version="4.20.70" />
	  <PackageReference Include="FluentAssertions" Version="6.12.0" />

  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Apis\ht.bff.apis\ht.bff.apis.csproj" />
  </ItemGroup>

</Project>
