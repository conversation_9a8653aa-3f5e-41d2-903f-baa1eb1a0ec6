﻿using ht.bff.apis.Services;
using ht.data.common.Shared;
using ht.data.common.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Users;
using Microsoft.AspNetCore.Mvc.Testing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Bff.APIs.Test
{
    public class TestTaskController : TestFixture
    {
        private Mock HTBETaskApiService;

        string token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjQ5MkMxRDdDMzg2QjZCNzg5MEE4QThDNjFGRUIxNEYyRDhGOTVFQUEiLCJ4NXQiOiJTU3dkZkRocmEzaVFxS2pHSC1zVTh0ajVYcW8ifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PdoFLyJp25RmZ7RCwwcvNLXyldzw_E7s2t8JB6tQrmAFt9IhvvOieCAyCvqfWe0cgAsn8oZAQcCStFFU3zA195evgKhuUtXp6og_iTSl5tOevAWzKVNL_qEyT5anRg3jDwSpQZICg-fcqS1ZeFUijVqZZkcfZPs-mdGSlopBvjXvhRY8CBnOcsdOzXLSdocUNTI1retIEcVxnTWyH6lUKe_x3OYNlJMKVcxW1NGUWXDbzn-cXzHjF7xHCm7erp-90q-7bYvZXLd1PS2D7rdFemaVrbIXMvbBf6goUcharaLtbN0Ifk8JRVegTxroDvc6WiYG7FVMzYfcgm_efE8-jg";

        public TestTaskController(WebApplicationFactory<Startup> factory) : base(factory)
        {
            HTBETaskApiService = new Mock<HTBETaskApiService>();
        }

        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task TestGetTask(string taskId)
        {
            var resp = await _client.GetAsync($"/api/Task/GetTask/{taskId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }



        [Fact]
        public async Task TestGetTasksList()
        {
            var content = JsonContent.Create<GetTaskListRequest>(new GetTaskListRequest
            {
                DateFromUtc = System.DateTime.UtcNow,
                DateToUtc = System.DateTime.UtcNow,
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExCarerId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExDoctorId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                TaskType = "Appointment"
            });

            var resp = await _client.PostAsync("/api/Task/GetTasksList", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestUpsertTask()
        {
            var content = JsonContent.Create<UpsertTaskRequest>(new UpsertTaskRequest
            {
                Task = new HealthTeamsTask
                {
                    AssignedToId = "",
                    TaskType = TaskTypes.CollectVitals,
                    TaskStatus = "Upcoming",
                    TaskDateTimeUtc = DateTime.UtcNow.AddDays(3),
                    TaskLocalLocale = "AUS Eastern Standard Time",
                    TaskName = "Collect vitals from Mr. Baskings",
                    ExTaskId = "",
                    AssignedToName = "Tristan Burgers",
                    AssignedToUser = new HealthTeamsUser
                    {
                        Email = "<EMAIL>",
                        Role = "Carer",
                        FullName = "Tristan Burgers",
                        Mobile = "**********",
                        PostCode = "2026",
                        UserCreatedUtc = DateTime.UtcNow
                    },
                    ExDoctorId = Guid.NewGuid().ToString(),
                    ExFacilityId = "",
                    ExResidentId = Guid.NewGuid().ToString(),
                    TaskDescription = "Tristan is to Collect Vitals from Mr. Baskings",
                    Resident = new HealthTeamsUser
                    {
                        Email = "<EMAIL>",
                        Role = "Resident",
                        FullName = "Janice Bauer",
                        Mobile = "0411 222 333",
                        PostCode = "2026",
                        ContactMethod = "Mobile",
                        VitalsLastCapturedUtc = DateTime.UtcNow.AddDays(-10),
                        Address = new Address
                        {
                            ExAddressId = "01",
                            LastModifiedUtc = DateTime.UtcNow,
                            BuildingName = "Tower byte",
                            Street1 = "56 Arthur Street",
                            City = "Randwick",
                            State = "nsw",
                            PostCode = "2000",
                            Country = "aust",
                            Long = "Unit 06",
                            Lat = "Unit 06",
                            MeshblockId = "01"
                        }
                    },
                    VitalsToCapture = new List<string>
                        {
                            "Blood Pressure","SpO2","Temperature","Glucose","Weight","Heartrate","UTI"
                        }
                }
            });

            var resp = await _client.PostAsync("/api/Task/UpsertTask", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestGetTasksSearch()
        {
            var content = JsonContent.Create<GetTasksSearchRequest>(new GetTasksSearchRequest
            {
                CurrentDate = DateTime.UtcNow,
                CurrentEndDate = DateTime.UtcNow,
                CurrentMonth = "",
                Filter = null,
                GetTaskDetails = true,
                IsShowAll = false,
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
            });

            var resp = await _client.PostAsync("/api/Task/GetTasksSearch", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TesGetVitalsList()
        {
            var content = JsonContent.Create<GetVitalsListRequest>(new GetVitalsListRequest
            {
                UserExId= "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
            });

            var resp = await _client.PostAsync("/api/Task/GetVitalsList", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
    }
}
