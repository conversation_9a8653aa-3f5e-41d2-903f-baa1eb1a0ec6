﻿using ht.bff.apis;
using Microsoft.AspNetCore.Mvc.Testing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Bff.APIs.Test
{
    public abstract class TestFixture: IClassFixture<WebApplicationFactory<Startup>>
    {
       

        public HttpClient _client;
        protected TestFixture(WebApplicationFactory<Startup> factory)
        {
            _client =  factory.CreateClient();
        }

    }
}
