﻿using ht.bff.apis.Services;
using ht.data.common.Surveys;
using Microsoft.AspNetCore.Mvc.Testing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Bff.APIs.Test
{
    public class TestSurveyController : TestFixture
    {
        private Mock HTBESurveyApiService;

        string token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjQ5MkMxRDdDMzg2QjZCNzg5MEE4QThDNjFGRUIxNEYyRDhGOTVFQUEiLCJ4NXQiOiJTU3dkZkRocmEzaVFxS2pHSC1zVTh0ajVYcW8ifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PdoFLyJp25RmZ7RCwwcvNLXyldzw_E7s2t8JB6tQrmAFt9IhvvOieCAyCvqfWe0cgAsn8oZAQcCStFFU3zA195evgKhuUtXp6og_iTSl5tOevAWzKVNL_qEyT5anRg3jDwSpQZICg-fcqS1ZeFUijVqZZkcfZPs-mdGSlopBvjXvhRY8CBnOcsdOzXLSdocUNTI1retIEcVxnTWyH6lUKe_x3OYNlJMKVcxW1NGUWXDbzn-cXzHjF7xHCm7erp-90q-7bYvZXLd1PS2D7rdFemaVrbIXMvbBf6goUcharaLtbN0Ifk8JRVegTxroDvc6WiYG7FVMzYfcgm_efE8-jg";

        public TestSurveyController(WebApplicationFactory<Startup> factory) : base(factory)
        {
            HTBESurveyApiService = new Mock<HTBESurveyApiService>();
        }
        [Fact]
        public async Task TestGetSurveyListForFacility()
        {
            var content = JsonContent.Create<GetSurveyListRequest>(new GetSurveyListRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                DateFromUtc = DateTime.UtcNow,
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
            });
            var resp = await _client.PostAsync($"/api/Survey/GetSurveyListForFacility", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetSurvey()
        {
            var content = JsonContent.Create<GetSurveyRequest>(new GetSurveyRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExSurveyId = "1",
                Filter = null
            });
            var resp = await _client.PostAsync($"/api/Survey/GetSurvey", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestUpsertSurveyForResident()
        {
            var content = JsonContent.Create<UpsertSurveyRequest>(new UpsertSurveyRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                SurveyResults = new HTSurvey()
                {
                    ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    ExSurveyId = "1",
                    ExSurveyResponseId = "1",
                    Questions = new List<Question>()
                    {

                    },
                    ResidentName = "test",
                    SelectedQuestions = new List<SelectedQuestion>(),
                    SurveyDescription = "test",
                    SurveyFrequency = "1",
                    SurveyName = "test",
                    SurveyResponseDate = DateTime.Now,
                    SurveyStatus = "test"
                },
                Filter = null
            });
            var resp = await _client.PostAsync($"/api/Survey/UpsertSurveyForResident", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestUpsertSurveyForFacility()
        {
            var content = JsonContent.Create<UpsertSurveyRequest>(new UpsertSurveyRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                SurveyResults = new HTSurvey()
                {
                    ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    ExSurveyId = "1",
                    ExSurveyResponseId = "1",
                    Questions = new List<Question>()
                    {

                    },
                    ResidentName = "test",
                    SelectedQuestions = new List<SelectedQuestion>(),
                    SurveyDescription = "test",
                    SurveyFrequency = "1",
                    SurveyName = "test",
                    SurveyResponseDate = DateTime.Now,
                    SurveyStatus = "test"
                },
                Filter = null
            });
            var resp = await _client.PostAsync($"/api/Survey/UpsertSurveyForFacility", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestGetSurveyResultsListForResident()
        {
            var content = JsonContent.Create<GetSurveyListRequest>(new GetSurveyListRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                DateFromUtc = DateTime.UtcNow,
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
            });
            var resp = await _client.PostAsync($"/api/Survey/GetSurveyResultsListForResident", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestGetSurveyResultsListForFacility()
        {
            var content = JsonContent.Create<GetSurveyListRequest>(new GetSurveyListRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                DateFromUtc = DateTime.UtcNow,
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
            });
            var resp = await _client.PostAsync($"/api/Survey/GetSurveyResultsListForFacility", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task TestGetSurveyResult(string exFacilityId)
        {
            var resp = await _client.GetAsync($"/api/Survey/GetSurveyResult/{exFacilityId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task TestGetAnswerList(string exFacilityId)
        {
            var resp = await _client.GetAsync($"/api/Survey/GetAnswerList/{exFacilityId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestUpsertQuestionForFacility()
        {
            var content = JsonContent.Create<UpsertQuestionRequest>(new UpsertQuestionRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
               question=new Questionsjson()
               {
                   AnswersJson=new List<Answersjson>(),
                   DefaultAnswerId=0,
                   ExQuestionId="1",
                   IsOpenAnswer=false,
                   OpenAnswerResponse="1",
                   QuestionAnswers="1",
                   QuestionId=1,
                   QuestionType=QuestionType.Graphical,
                   SelectedAnswerId=0,
                   Text = "1"
               },
                Filter = null,
            });
            var resp = await _client.PostAsync($"/api/Survey/UpsertQuestionForFacility", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestUpdateSurveyStatus()
        {
            var content = JsonContent.Create<UpdateSurveyStatusRequest>(new UpdateSurveyStatusRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExSurveyId="1",
                SurveyStatus="",
                Filter = null,
            });
            var resp = await _client.PostAsync($"/api/Survey/UpdateSurveyStatus", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
    }
}
