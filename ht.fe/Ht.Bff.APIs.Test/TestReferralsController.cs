﻿using ht.bff.apis.Services;
using Microsoft.AspNetCore.Mvc.Testing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Bff.APIs.Test
{
    public class TestReferralsController : TestFixture
    {
        private Mock HTBEReferralApiService;
        private Mock mockSignalRService;
        private Mock mockEmailVerificationService;
        string token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjQ5MkMxRDdDMzg2QjZCNzg5MEE4QThDNjFGRUIxNEYyRDhGOTVFQUEiLCJ4NXQiOiJTU3dkZkRocmEzaVFxS2pHSC1zVTh0ajVYcW8ifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PdoFLyJp25RmZ7RCwwcvNLXyldzw_E7s2t8JB6tQrmAFt9IhvvOieCAyCvqfWe0cgAsn8oZAQcCStFFU3zA195evgKhuUtXp6og_iTSl5tOevAWzKVNL_qEyT5anRg3jDwSpQZICg-fcqS1ZeFUijVqZZkcfZPs-mdGSlopBvjXvhRY8CBnOcsdOzXLSdocUNTI1retIEcVxnTWyH6lUKe_x3OYNlJMKVcxW1NGUWXDbzn-cXzHjF7xHCm7erp-90q-7bYvZXLd1PS2D7rdFemaVrbIXMvbBf6goUcharaLtbN0Ifk8JRVegTxroDvc6WiYG7FVMzYfcgm_efE8-jg";

        public TestReferralsController(WebApplicationFactory<Startup> factory) : base(factory)
        {
            HTBEReferralApiService = new Mock<HTBEReferralApiService>();
            mockSignalRService = new Mock<SignalRService>();
            mockEmailVerificationService = new Mock<EmailVerificationService>();

        }
        [Fact]
        public async Task TestGetReferrals()
        {
            var content = JsonContent.Create<GetReferralRequest>(new GetReferralRequest
            {
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                FromDateUtc = DateTime.UtcNow,
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"
            });

            var resp = await _client.PostAsync("/api/Referrals/GetReferrals", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetReferralDetails()
        {
            string exreferralId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c";

            var resp = await _client.GetAsync($"/api/Referrals/GetReferralDetails/{exreferralId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestUpsertReferral()
        {
            var content = JsonContent.Create<TelehealthReferral>(new TelehealthReferral
            {
                ExReferralId = "",
                Date = DateTime.UtcNow,
                Type = "Type of Referral",
                ReferredTo = "Referred To Person"
            });

            var resp = await _client.PostAsync("/api/Referrals/UpsertReferrals", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestSendtoSpecialist()
        {
            var content = JsonContent.Create<SendtoSpecialistRequest>(new SendtoSpecialistRequest
            {
                ExReferralId = "",
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null
            });

            var resp = await _client.PostAsync("/api/Referrals/SendtoSpecialist", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
    }
}
