﻿using ht.bff.apis.Services;
using ht.data.common.Dashboards;
using Microsoft.AspNetCore.Mvc.Testing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Bff.APIs.Test
{
    public class FamilyControllerTest:TestFixture
    {
        private Mock mockapiService;
        private Mock mockSignalRService;
        private Mock mockEmailVerificationService;
        string token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjQ5MkMxRDdDMzg2QjZCNzg5MEE4QThDNjFGRUIxNEYyRDhGOTVFQUEiLCJ4NXQiOiJTU3dkZkRocmEzaVFxS2pHSC1zVTh0ajVYcW8ifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PdoFLyJp25RmZ7RCwwcvNLXyldzw_E7s2t8JB6tQrmAFt9IhvvOieCAyCvqfWe0cgAsn8oZAQcCStFFU3zA195evgKhuUtXp6og_iTSl5tOevAWzKVNL_qEyT5anRg3jDwSpQZICg-fcqS1ZeFUijVqZZkcfZPs-mdGSlopBvjXvhRY8CBnOcsdOzXLSdocUNTI1retIEcVxnTWyH6lUKe_x3OYNlJMKVcxW1NGUWXDbzn-cXzHjF7xHCm7erp-90q-7bYvZXLd1PS2D7rdFemaVrbIXMvbBf6goUcharaLtbN0Ifk8JRVegTxroDvc6WiYG7FVMzYfcgm_efE8-jg";

        public FamilyControllerTest(WebApplicationFactory<Startup> factory) : base(factory)
        {
            mockapiService = new Mock<HTBEApiService>();
            mockSignalRService = new Mock<SignalRService>();
            mockEmailVerificationService = new Mock<EmailVerificationService>();

        }

        [Fact]
        public async Task TestGetDashboard()
        {
            var content = JsonContent.Create<GetDoctorDashboardRequest>(new GetDoctorDashboardRequest
            {
                CurrentDate = System.DateTime.UtcNow,
                CurrentEndDate = System.DateTime.UtcNow,
                CurrentMonth = System.DateTime.UtcNow.Month.ToString(),
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                GetTaskDetails = true
            });

            var resp = await _client.PostAsync("/api/Familymember/GetDashboard", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task GetResidentsforFamilyTest(string exFamilyId)
        {

            var resp = await _client.GetAsync($"/api/Familymember/GetResidentsforFamily/{exFamilyId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
    }
}
