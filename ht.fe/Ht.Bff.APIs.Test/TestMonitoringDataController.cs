﻿using ht.bff.apis.Services;
using ht.data.common.Users;
using ht.data.common.Wounds;
using Microsoft.AspNetCore.Mvc.Testing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Bff.APIs.Test
{
    public class TestMonitoringDataController : TestFixture
    {
        private Mock mockapiService;
        private Mock mockSignalRService;
        private Mock mockEmailVerificationService;
        string token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjQ5MkMxRDdDMzg2QjZCNzg5MEE4QThDNjFGRUIxNEYyRDhGOTVFQUEiLCJ4NXQiOiJTU3dkZkRocmEzaVFxS2pHSC1zVTh0ajVYcW8ifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PdoFLyJp25RmZ7RCwwcvNLXyldzw_E7s2t8JB6tQrmAFt9IhvvOieCAyCvqfWe0cgAsn8oZAQcCStFFU3zA195evgKhuUtXp6og_iTSl5tOevAWzKVNL_qEyT5anRg3jDwSpQZICg-fcqS1ZeFUijVqZZkcfZPs-mdGSlopBvjXvhRY8CBnOcsdOzXLSdocUNTI1retIEcVxnTWyH6lUKe_x3OYNlJMKVcxW1NGUWXDbzn-cXzHjF7xHCm7erp-90q-7bYvZXLd1PS2D7rdFemaVrbIXMvbBf6goUcharaLtbN0Ifk8JRVegTxroDvc6WiYG7FVMzYfcgm_efE8-jg";

        public TestMonitoringDataController(WebApplicationFactory<Startup> factory) : base(factory)
        {
            mockapiService = new Mock<HTBEApiService>();
            mockSignalRService = new Mock<SignalRService>();
            mockEmailVerificationService = new Mock<EmailVerificationService>();

        }
        [Fact]
        public async Task TestGetMonitoringData()
        {
            string userId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c";

            var resp = await _client.GetAsync($"/api/Monitoringdata/GetMonitoringData/{userId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestUpsertMonitoringData()
        {
            var content = JsonContent.Create<UpsertMonitoringDataRequest>(new UpsertMonitoringDataRequest
            {
                Data = new TelehealthMonitorData
                {
                    UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    DateCreatedUtc = DateTime.UtcNow,
                    DateModifiedUtc = DateTime.UtcNow,
                    Readings = new List<TelehealthDataItem>{
                        new TelehealthDataItem
                        {
                            UserExId="98125",
                            ReadDateUtc=DateTime.UtcNow.AddHours(2),
                            ItemType  =MonitorType.BPDiastolic,
                            ReadValue ="71",
                            ReadUnits="",
                            ReadDesc="",
                            AssessColour="Red",
                            ReadDeviceId="",
                            ReadLat="",
                            ReadLon =""
                        },
                        new TelehealthDataItem
                        {
                            UserExId="98125",
                            ReadDateUtc=DateTime.UtcNow.AddHours(2),
                            ItemType  =MonitorType.SpO2,
                            ReadValue ="90",
                            ReadUnits="",
                            ReadDesc="",
                            AssessColour="",
                            ReadDeviceId="",
                            ReadLat="",
                            ReadLon =""
                        }
                    },
                    Thresholds = new MonitoringThresholds
                    {

                    }
                }
            });

            var resp = await _client.PostAsync("/api/Monitoringdata/UpsertMonitoringData", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task GetMonitoringDataListTest()
        {
            var content = JsonContent.Create<GetMonitoringDataListRequest>(new GetMonitoringDataListRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
               FromDateUtc=DateTime.UtcNow,
               UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter =null
            });
            var resp = await _client.PostAsync("/api/Monitoringdata/GetMonitoringDataList", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task GetUserLastVitalsTest(string UserExId)
        {

            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            var resp = await _client.GetAsync($"/api/Monitoringdata/GetUserLastVitals/{UserExId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            resp.EnsureSuccessStatusCode();

            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task GetResidentThresholdsTest(string exResidnetId)
        {

            var resp = await _client.GetAsync($"/api/Monitoringdata/GetResidentThresholds/{exResidnetId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            resp.EnsureSuccessStatusCode();

            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task GetMonitoringTypesTest()
        {

            var resp = await _client.GetAsync($"/api/Monitoringdata/GetMonitoringTypes");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            resp.EnsureSuccessStatusCode();

            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task GetResidentWoundsTest()
        {
            var content = JsonContent.Create<GetWoundRequest>(new GetWoundRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExWoundId = "",
                WoundId = 0,
                Filter = null
            });
            var resp = await _client.PostAsync("/api/Monitoringdata/GetResidentWounds", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task GetWoundDetailsTest()
        {
            var content = JsonContent.Create<GetWoundRequest>(new GetWoundRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExWoundId = "",
                WoundId = 0,
                Filter = null
            });
            var resp = await _client.PostAsync("/api/Monitoringdata/GetWoundDetails", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task UpsertWoundDetailsTest()
        {
            var content = JsonContent.Create<UpsertWoundDetailsRequest>(new UpsertWoundDetailsRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Wound = new Wound()
                {
                    
                },
                 
                Filter = null
            });
            var resp = await _client.PostAsync("/api/Monitoringdata/UpsertWoundDetails", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
    }
}
