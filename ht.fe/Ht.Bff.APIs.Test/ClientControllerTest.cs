﻿using bt.bff.common.Models;
using ht.bff.apis;
using ht.bff.apis.Examples;
using ht.bff.apis.Services;
using ht.data.common.EmailVerification;
using ht.data.common.Users;
using Microsoft.AspNetCore.Mvc.Testing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using YamlDotNet.Core.Tokens;

namespace Ht.Bff.APIs.Test
{
    public class ClientControllerTest : TestFixture
    {
        private Mock mockapiService;
        private Mock mockSignalRService;
        private Mock mockEmailVerificationService;
        string token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjQ5MkMxRDdDMzg2QjZCNzg5MEE4QThDNjFGRUIxNEYyRDhGOTVFQUEiLCJ4NXQiOiJTU3dkZkRocmEzaVFxS2pHSC1zVTh0ajVYcW8ifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PdoFLyJp25RmZ7RCwwcvNLXyldzw_E7s2t8JB6tQrmAFt9IhvvOieCAyCvqfWe0cgAsn8oZAQcCStFFU3zA195evgKhuUtXp6og_iTSl5tOevAWzKVNL_qEyT5anRg3jDwSpQZICg-fcqS1ZeFUijVqZZkcfZPs-mdGSlopBvjXvhRY8CBnOcsdOzXLSdocUNTI1retIEcVxnTWyH6lUKe_x3OYNlJMKVcxW1NGUWXDbzn-cXzHjF7xHCm7erp-90q-7bYvZXLd1PS2D7rdFemaVrbIXMvbBf6goUcharaLtbN0Ifk8JRVegTxroDvc6WiYG7FVMzYfcgm_efE8-jg";

        public ClientControllerTest(WebApplicationFactory<Startup> factory) : base(factory)
        {
            mockapiService = new Mock<HTBEApiService>();
            mockSignalRService = new Mock<SignalRService>();
            mockEmailVerificationService = new Mock<EmailVerificationService>();

        }


        [Fact]
        public async Task TestDiscover()
        {

            var content = JsonContent.Create<DiscoveryRequest>(new DiscoveryRequest
            {
                AppName = "HealthTeams",
                Platform = "Web"
            });
            //var token = TokenGenerator.GenerateToken("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c");

            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            var resp = await _client.GetAsync("/api/client/Discover");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestLogin()
        {
            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            var resp = await _client.GetAsync("/api/client/Login");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());

        }

        [Fact]
        public async Task UpsertUserTest()
        {
            var content = new UpsertUserRequest
            {
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                MeetingId = "MeetingId",
                FirstName = "john",
                LastName = "call",
                Role = "Resident"
            };
            var resp = await _client.PostAsJsonAsync("/api/client/UpsertUser", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());

        }

        public async Task SecureCallToBackendTest()
        {

            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

            var resp = await _client.GetAsync("/api/client/SecureCallToBackend");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());

        }


        [Theory]
        [InlineData("E895C3BA-BEFE-49A1-BD56-84470AF6D07F")]
        public async Task GetInvitedUsersTest(string exfacilityId)
        {
            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

            var resp = await _client.GetAsync($"/api/client/GetInvitedUsers/{exfacilityId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task GetUserProfileByExternalIdTest(string externalId)
        {
            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

            var resp = await _client.GetAsync($"/api/client/GetUserProfileByExternalId/{externalId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task InviteUserRequestTest()
        {
            var content = new InviteUserRequest
            {
                ExResidentID = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                LastName = "test-call",
                Role = "Resident",
                Email= "<EMAIL>",
                ExFacilityId= "E895C3BA-BEFE-49A1-BD56-84470AF6D07F",
                FullMobile= "0405386732",
                NotifyUser=false,
                RelationShip="",
                Filter=null
            };
            var resp = await _client.PostAsJsonAsync("/api/client/InviteUser", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());

        }
        [Fact]
        public async Task ValidateEmailTest()
        {
            var content = new EmailVerificationRequest
            {
              
                Email = "<EMAIL>",
                ExFacilityId = "E895C3BA-BEFE-49A1-BD56-84470AF6D07F",
              
                Filter = null
            };
            var resp = await _client.PostAsJsonAsync("/api/client/ValidateEmail", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());

        }

        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task GetLoggedInUserACSTokenTest(string UserExId)
        {
            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

            var resp = await _client.GetAsync($"/api/client/GetLoggedInUserACSToken/{UserExId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task InviteUserOnCallTest()
        {
            var content = new InviteUserOnCallRequest
            {
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                LastName = "test-call",
                Role = "Resident",
                Email = "<EMAIL>",
                ExFacilityId = "E895C3BA-BEFE-49A1-BD56-84470AF6D07F",
                
                Filter = null
            };
            var resp = await _client.PostAsJsonAsync("/api/client/InviteUserOnCall", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());

        }
    }
}
