﻿using ht.bff.apis.Services;
using ht.data.common.Dashboards;
using ht.data.common.partner;
using ht.data.common.Vitals;
using Microsoft.AspNetCore.Mvc.Testing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Bff.APIs.Test
{
    public class TestResidentController : TestFixture
    {
        private Mock HTBEResidentApiService;

        string token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjQ5MkMxRDdDMzg2QjZCNzg5MEE4QThDNjFGRUIxNEYyRDhGOTVFQUEiLCJ4NXQiOiJTU3dkZkRocmEzaVFxS2pHSC1zVTh0ajVYcW8ifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PdoFLyJp25RmZ7RCwwcvNLXyldzw_E7s2t8JB6tQrmAFt9IhvvOieCAyCvqfWe0cgAsn8oZAQcCStFFU3zA195evgKhuUtXp6og_iTSl5tOevAWzKVNL_qEyT5anRg3jDwSpQZICg-fcqS1ZeFUijVqZZkcfZPs-mdGSlopBvjXvhRY8CBnOcsdOzXLSdocUNTI1retIEcVxnTWyH6lUKe_x3OYNlJMKVcxW1NGUWXDbzn-cXzHjF7xHCm7erp-90q-7bYvZXLd1PS2D7rdFemaVrbIXMvbBf6goUcharaLtbN0Ifk8JRVegTxroDvc6WiYG7FVMzYfcgm_efE8-jg";

        public TestResidentController(WebApplicationFactory<Startup> factory) : base(factory)
        {
            HTBEResidentApiService = new Mock<HTBEResidentApiService>();


        }
        [Fact]
        public async Task TestGetDashboard()
        {
            var content = JsonContent.Create<GetResidentDashboardRequest>(new GetResidentDashboardRequest
            {
                CurrentDate = System.DateTime.UtcNow,
                CurrentEndDate = System.DateTime.UtcNow,
                CurrentMonth = System.DateTime.UtcNow.Month.ToString(),
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                GetTaskDetails = true
            });

            var resp = await _client.PostAsync("/api/Resident/GetDashboard", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task GetVitalsListForResidentGraphsTest(string exResidentId)
        {
            var resp = await _client.GetAsync($"/api/Resident/GetVitalsListForResidentGraphs/{exResidentId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            resp.EnsureSuccessStatusCode();
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestSendErrorLogDetails()
        {
            var content = JsonContent.Create<VitalsErrorLogDetailsRequest>(new VitalsErrorLogDetailsRequest
            {
                Filter = null,
                ErrorLogDetails = new ErrorLogDetail()
                {

                },
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"

            });

            var resp = await _client.PostAsync("/api/Resident/SendErrorLogDetails", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetResidentPartnerEventDetails()
        {
            var content = JsonContent.Create<GetResidentPartnerEventDetailsRequest>(new GetResidentPartnerEventDetailsRequest
            {
                Filter = null,
                EventName = "Test",
                PartnerId = "",
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                FromDateUtc = DateTime.UtcNow,
                PartnerName = "Test",
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"

            });

            var resp = await _client.PostAsync("/api/Resident/GetResidentPartnerEventDetails", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestGetResidentPartnerMappingDetails()
        {
            var content = JsonContent.Create<GetPartnerIntegrationMappingDetailsRequest>(new GetPartnerIntegrationMappingDetailsRequest
            {
                Filter = null,
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                PartnerExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                PartnerId = 0,
                ExPartnerIntegrationId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExPartnerMapId = "",
                PartnerIntegrationId = 0,
                PartnerMapId = 0,
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"

            });

            var resp = await _client.PostAsync("/api/Resident/GetResidentPartnerMappingDetails", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
    }
}
