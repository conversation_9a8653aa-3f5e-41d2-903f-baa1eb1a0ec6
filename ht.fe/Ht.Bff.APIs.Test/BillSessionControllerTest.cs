﻿using Azure;
using ht.bff.apis;
using ht.bff.apis.Services;
using Microsoft.AspNetCore.Mvc.Testing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Bff.APIs.Test
{
    public class BillSessionControllerTest : TestFixture
    {


        private Mock<HTBEBillsessionApiService> _mockSessionApiService;
        public BillSessionControllerTest(WebApplicationFactory<Startup> factory) : base(factory)
        {
            _mockSessionApiService = new Mock<HTBEBillsessionApiService>();
        }


        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task GetBillsessionTest_ReturnsOk_WhenTokenProvided(string UserExId)
        {
            var token = TokenGenerator.GenerateToken(UserExId);

            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            var resp = await _client.GetAsync($"/api/Billsession/GetBillsession/{UserExId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            resp.EnsureSuccessStatusCode();

            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task GetBillsessionDetailsTest(string exbillId)
        {
            var token = TokenGenerator.GenerateToken(exbillId);

            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            var resp = await _client.GetAsync($"/api/Billsession/GetBillsessionDetails/{exbillId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            resp.EnsureSuccessStatusCode();

            Trace.Write(await resp.Content.ReadAsStringAsync());
        }


        [Fact]
        public async Task UpsertBillSessionTest()
        {
            var content = JsonContent.Create<UpsertBillSessionRequest>(new UpsertBillSessionRequest
            {
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                FullName = "",
                DateofSessionUTC = DateTime.UtcNow,
                TimeofSessionUTC = DateTime.UtcNow,
                BillableItems = new List<BillableItem> { },
                Total = 123,
                Gap = 123,
                ProviderName = "",
                ProviderNumber = "",
                Signature = "",
                IsBilled = true,
                SentTo = ""
            });


            var token = TokenGenerator.GenerateToken("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c");

            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

            var resp = await _client.PostAsync("/api/Billsession/UpsertBillsession", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
    }
}
