﻿using ht.bff.apis.Services;
using ht.data.common.Users;
using Microsoft.AspNetCore.Mvc.Testing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Bff.APIs.Test
{
    public class TestUserController : TestFixture
    {
        private Mock HTBEApiService;

        string token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjQ5MkMxRDdDMzg2QjZCNzg5MEE4QThDNjFGRUIxNEYyRDhGOTVFQUEiLCJ4NXQiOiJTU3dkZkRocmEzaVFxS2pHSC1zVTh0ajVYcW8ifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PdoFLyJp25RmZ7RCwwcvNLXyldzw_E7s2t8JB6tQrmAFt9IhvvOieCAyCvqfWe0cgAsn8oZAQcCStFFU3zA195evgKhuUtXp6og_iTSl5tOevAWzKVNL_qEyT5anRg3jDwSpQZICg-fcqS1ZeFUijVqZZkcfZPs-mdGSlopBvjXvhRY8CBnOcsdOzXLSdocUNTI1retIEcVxnTWyH6lUKe_x3OYNlJMKVcxW1NGUWXDbzn-cXzHjF7xHCm7erp-90q-7bYvZXLd1PS2D7rdFemaVrbIXMvbBf6goUcharaLtbN0Ifk8JRVegTxroDvc6WiYG7FVMzYfcgm_efE8-jg";

        public TestUserController(WebApplicationFactory<Startup> factory) : base(factory)
        {
            HTBEApiService = new Mock<HTBEApiService>();
        }
        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task TestGetUserDetails(string UserExId)
        {
            var resp = await _client.GetAsync($"/api/User/GetUserDetails/{UserExId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestUploadProfilePic()
        {


            var resp = await _client.PostAsync("/api/User/UploadProfilePic", null);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }


        [Fact]
        public async Task TestAskForAccess()
        {

            var content = JsonContent.Create<AskForAccessRequest>(new AskForAccessRequest
            {
                Files = new List<HealthTeamsFile>()
               {
                  new HealthTeamsFile()
                  {
                      
                  },

               },
                
            });

            var resp = await _client.PostAsync("/api/User/AskForAccess", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetUsersSearchBrief()
        {

            var content = JsonContent.Create<GetUsersSearchRequest>(new GetUsersSearchRequest
            {
                ExDoctorId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExFamilyId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExCarerId = "",
                Filter = null,
                IsShowAll = false,
                Role = "Test",
                SearchText = "Test",
                Status = "Test"
            });

            var resp = await _client.PostAsync("/api/User/GetUsersSearchBrief", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetUsersSearch()
        {

            var content = JsonContent.Create<GetUsersSearchRequest>(new GetUsersSearchRequest
            {
                ExDoctorId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExFamilyId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExCarerId = "",
                Filter = null,
                IsShowAll = false,
                Role = "Test",
                SearchText = "Test",
                Status = "Test"
            });

            var resp = await _client.PostAsync("/api/User/GetUsersSearch", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestGetResident()
        {

            var content = JsonContent.Create<GetResidentRequest>(new GetResidentRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,

            });

            var resp = await _client.PostAsync("/api/User/GetResident", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestGetFacilities()
        {

            var content = JsonContent.Create<GetFacilitiesRequest>(new GetFacilitiesRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,

            });

            var resp = await _client.PostAsync("/api/User/GetFacilities", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetResidentsSearch()
        {

            var content = JsonContent.Create<GetResidentsSearchRequest>(new GetResidentsSearchRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,

            });

            var resp = await _client.PostAsync("/api/User/GetResidentsSearch", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetCountries()
        {


            var resp = await _client.GetAsync("/api/User/GetCountries");

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestDisconnectFacility()
        {

            var content = JsonContent.Create<DisconnectFacilityRequest>(new DisconnectFacilityRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,

            });

            var resp = await _client.PostAsync("/api/User/DisconnectFacility", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task TestGetUserConnectedFacilities(string UserExId)
        {
            var resp = await _client.GetAsync($"/api/User/GetUserConnectedFacilities/{UserExId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task TestGetNewFacilityRequests(string UserExId)
        {
            var resp = await _client.GetAsync($"/api/User/GetNewFacilityRequests/{UserExId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestConfirmFacilityRequest()
        {

            var content = JsonContent.Create<GetFacilityConnectRequests>(new GetFacilityConnectRequests
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,

            });

            var resp = await _client.PostAsync("/api/User/ConfirmFacilityRequest", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetFacilitybyInvitecode()
        {

            var content = JsonContent.Create<GetFacilityConnectRequests>(new GetFacilityConnectRequests
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,

            });

            var resp = await _client.PostAsync("/api/User/GetFacilitybyInvitecode", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestUserConnectFacility()
        {

            var content = JsonContent.Create<GetFacilityConnectRequests>(new GetFacilityConnectRequests
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,

            });

            var resp = await _client.PostAsync("/api/User/UserConnectFacility", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestUpsertResident()
        {

            var content = JsonContent.Create<UpsertResidentRequest>(new UpsertResidentRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                resident = new ResidentUser()
                {

                },
                Filter = null,

            });

            var resp = await _client.PostAsync("/api/User/UpsertResident", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetResidentMonitoringTimes()
        {

            var content = JsonContent.Create<GetResidentMonitoringTimesRequest>(new GetResidentMonitoringTimesRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExUserId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,

            });

            var resp = await _client.PostAsync("/api/User/GetResidentMonitoringTimes", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetResidentLimitsNotifications()
        {

            var content = JsonContent.Create<GetResidentMonitoringLimitsNotificationsRequest>(new GetResidentMonitoringLimitsNotificationsRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExUserId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,

            });

            var resp = await _client.PostAsync("/api/User/GetResidentLimitsNotifications", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestUpsertResidentMonitoringTimes()
        {

            var content = JsonContent.Create<UpsertResidentMonitoringTimesRequest>(new UpsertResidentMonitoringTimesRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExUserId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
                Details = new MonitoringResidentTimes(),


            });

            var resp = await _client.PostAsync("/api/User/UpsertResidentMonitoringTimes", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestGetResidentAlertContactList()
        {

            var content = JsonContent.Create<GetAlertContactListRequest>(new GetAlertContactListRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"
            });

            var resp = await _client.PostAsync("/api/User/GetResidentAlertContactList", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetAlertResidentList()
        {

            var content = JsonContent.Create<GetAlertResidentListRequest>(new GetAlertResidentListRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"
            });

            var resp = await _client.PostAsync("/api/User/GetAlertResidentList", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestUpdateResidentAlertStatus()
        {

            var content = JsonContent.Create<UpdateResidentAlertStatusRequest>(new UpdateResidentAlertStatusRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
                AckComments = null,
                AcknowledgedDateUtc = DateTime.UtcNow,
                AcknowledgedUserId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExAlertId = new List<string>()
            });

            var resp = await _client.PostAsync("/api/User/UpdateResidentAlertStatus", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task TestActivateUser(string UserExId)
        {
            var resp = await _client.GetAsync($"/api/User/ActivateUser/{UserExId}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetResidentsLastVitalsUpdateReport()
        {

            var content = JsonContent.Create<GetResidentsLastVitalCaptureReportRequest>(new GetResidentsLastVitalCaptureReportRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
                EndDate = DateTime.UtcNow,
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"
            });

            var resp = await _client.PostAsync("/api/User/GetResidentsLastVitalsUpdateReport", content);

            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
    }
}
