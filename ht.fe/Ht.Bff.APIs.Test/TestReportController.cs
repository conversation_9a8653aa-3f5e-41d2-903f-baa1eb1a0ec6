﻿using ht.bff.apis.Services;
using ht.data.common;
using ht.data.common.Reports;
using Microsoft.AspNetCore.Mvc.Testing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Bff.APIs.Test
{
    public class TestReportController:TestFixture
    {
        private Mock HTBEApiService;
  
        string token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjQ5MkMxRDdDMzg2QjZCNzg5MEE4QThDNjFGRUIxNEYyRDhGOTVFQUEiLCJ4NXQiOiJTU3dkZkRocmEzaVFxS2pHSC1zVTh0ajVYcW8ifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PdoFLyJp25RmZ7RCwwcvNLXyldzw_E7s2t8JB6tQrmAFt9IhvvOieCAyCvqfWe0cgAsn8oZAQcCStFFU3zA195evgKhuUtXp6og_iTSl5tOevAWzKVNL_qEyT5anRg3jDwSpQZICg-fcqS1ZeFUijVqZZkcfZPs-mdGSlopBvjXvhRY8CBnOcsdOzXLSdocUNTI1retIEcVxnTWyH6lUKe_x3OYNlJMKVcxW1NGUWXDbzn-cXzHjF7xHCm7erp-90q-7bYvZXLd1PS2D7rdFemaVrbIXMvbBf6goUcharaLtbN0Ifk8JRVegTxroDvc6WiYG7FVMzYfcgm_efE8-jg";

        public TestReportController(WebApplicationFactory<Startup> factory) : base(factory)
        {
            HTBEApiService = new Mock<HTBEApiService>();
            

        }

        [Fact]
        public async Task TestGetReport()
        {
            var content = JsonContent.Create<GetReportRequest>(new GetReportRequest
            {
               Filter=null,
               Props=null,
               ReportName="test",
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"
            });

            var resp = await _client.PostAsync("/api/Report/GetReport", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetReporList()
        {
            var content = JsonContent.Create<baseRequest>(new baseRequest
            {
                Filter = null,
                
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"
            });

            var resp = await _client.PostAsync("/api/Report/GetReportList", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
    }
}
