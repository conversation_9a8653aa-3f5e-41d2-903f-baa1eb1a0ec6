﻿using ht.bff.apis.Services;
using Microsoft.AspNetCore.Mvc.Testing;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Diagnostics;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Bff.APIs.Test
{
    public class TestTeleHealthController:TestFixture
    {
        private Mock HTBETeleHealthApiService;

        string token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjQ5MkMxRDdDMzg2QjZCNzg5MEE4QThDNjFGRUIxNEYyRDhGOTVFQUEiLCJ4NXQiOiJTU3dkZkRocmEzaVFxS2pHSC1zVTh0ajVYcW8ifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PdoFLyJp25RmZ7RCwwcvNLXyldzw_E7s2t8JB6tQrmAFt9IhvvOieCAyCvqfWe0cgAsn8oZAQcCStFFU3zA195evgKhuUtXp6og_iTSl5tOevAWzKVNL_qEyT5anRg3jDwSpQZICg-fcqS1ZeFUijVqZZkcfZPs-mdGSlopBvjXvhRY8CBnOcsdOzXLSdocUNTI1retIEcVxnTWyH6lUKe_x3OYNlJMKVcxW1NGUWXDbzn-cXzHjF7xHCm7erp-90q-7bYvZXLd1PS2D7rdFemaVrbIXMvbBf6goUcharaLtbN0Ifk8JRVegTxroDvc6WiYG7FVMzYfcgm_efE8-jg";

        public TestTeleHealthController(WebApplicationFactory<Startup> factory) : base(factory)
        {
            HTBETeleHealthApiService = new Mock<HTBETeleHealthApiService>();
        }
        [Fact]
        public async Task TestGetTeleHealthDetails()
        {
            var content = JsonContent.Create<GetTeleHealthDetailsRequest>(new GetTeleHealthDetailsRequest
            {
                CurrentDate = DateTime.Now,
                ExFacilityId= "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter=null,
                ExResidentId= "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExTaskId="",
                ExTransactionId="",
                TabName="",
                Role = "Resident"
            });

            var resp = await _client.PostAsync("/api/TeleHealth/GetTeleHealthDetails", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestLaunchMeeting()
        {
            var content = JsonContent.Create<LaunchMeetingRequest>(new LaunchMeetingRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
               ExMeetingId="1",
               Role="Test",
               ServerCallId=""
                
            });

            var resp = await _client.PostAsync("/api/TeleHealth/LaunchMeeting", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestConvertTaskToMeeting()
        {
            var content = JsonContent.Create<ConvertTaskToMeetingRequest>(new ConvertTaskToMeetingRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
                ExTaskId = "",
                PrevalidateOnly = true

            });

            var resp = await _client.PostAsync("/api/TeleHealth/ConvertTaskToMeeting", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestGetMeetingId()
        {
            var content = JsonContent.Create<GetMeetingIdRequest>(new GetMeetingIdRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
                ExTaskId = "",

            });

            var resp = await _client.PostAsync("/api/TeleHealth/GetMeetingId", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestGetResidentVitalGraphData()
        {
            var content = JsonContent.Create<GetResidentVitalGraphDataRequest>(new GetResidentVitalGraphDataRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
               ExResidentId= "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
               NoOfWeeks = 1,
               RoleRequested="",
               Vital="",
               VitalSubType=""
               

            });

            var resp = await _client.PostAsync("/api/TeleHealth/GetResidentVitalGraphData", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestStartRecording()
        {
            var content = JsonContent.Create<LaunchMeetingRequest>(new LaunchMeetingRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
                 ExMeetingId="",
                 Role="Test",
                 ServerCallId="1"


            });

            var resp = await _client.PostAsync("/api/TeleHealth/StartRecording", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestPauseRecording()
        {
            var content = JsonContent.Create<LaunchMeetingRequest>(new LaunchMeetingRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
                ExMeetingId = "",
                Role = "Test",
                ServerCallId = "1"


            });

            var resp = await _client.PostAsync("/api/TeleHealth/PauseRecording", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestResumeRecording()
        {
            var content = JsonContent.Create<LaunchMeetingRequest>(new LaunchMeetingRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
                ExMeetingId = "",
                Role = "Test",
                ServerCallId = "1"


            });

            var resp = await _client.PostAsync("/api/TeleHealth/ResumeRecording", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestStopRecording()
        {
            var content = JsonContent.Create<LaunchMeetingRequest>(new LaunchMeetingRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
                ExMeetingId = "",
                Role = "Test",
                ServerCallId = "1"


            });

            var resp = await _client.PostAsync("/api/TeleHealth/StopRecording", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        [Fact]
        public async Task TestGetRecordingState()
        {
            var content = JsonContent.Create<LaunchMeetingRequest>(new LaunchMeetingRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Filter = null,
                ExMeetingId = "",
                Role = "Test",
                ServerCallId = "1"


            });

            var resp = await _client.PostAsync("/api/TeleHealth/GetRecordingState", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Theory]
        [InlineData("abcf2d0e-7a69-4cc3-9c58-5849fbe1769c")]
        public async Task TestGetSelectionsByType(string selectionType)
        {
            var resp = await _client.GetAsync($"/api/TeleHealth/GetSelectionsByType/{selectionType}");
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
    }
}
