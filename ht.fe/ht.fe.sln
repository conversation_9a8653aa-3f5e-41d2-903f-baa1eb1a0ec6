﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31512.422
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "APIs", "APIs", "{1C6DBBC6-EDF5-40EF-8F46-AB6CD0ECD7A3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Clients", "Clients", "{0A7A5BD5-D8AF-49B0-B10B-E950DE92A077}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ht.bff.apis", "Apis\ht.bff.apis\ht.bff.apis.csproj", "{EC8AE8B0-4DC8-4100-A9CD-3DFB96702EFF}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{D0383B23-B2B6-4970-B42D-22CD798643FF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "bt.bff.common", "Common\bt.bff.common\bt.bff.common.csproj", "{9DD84E71-AC25-49B1-9238-74E1B5C87E48}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Examples", "Examples", "{4905F2EC-81A3-421A-BDCD-51454BC0D9AE}"
	ProjectSection(SolutionItems) = preProject
		htMeetingPage.html = htMeetingPage.html
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BFF.Secure.Client", "Examples\BFF.Secure.Client\BFF.Secure.Client.csproj", "{FC78C09D-2B04-49D5-8E44-8145317C6348}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "HT.MonitorData.Device.Sender", "Examples\HT.MonitorData.Device.Sender\HT.MonitorData.Device.Sender.csproj", "{327C413C-ED1D-4BC8-9F37-8EF957E239D2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{5FBE782B-D5B9-41AB-9EC3-34B92BEB86EC}"
	ProjectSection(SolutionItems) = preProject
		NuGet.config = NuGet.config
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{A65273C5-0AFB-4AB0-B1E8-A659476A5104}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Ht.Bff.APIs.Test", "Ht.Bff.APIs.Test\Ht.Bff.APIs.Test.csproj", "{230125E9-E725-408F-A0D4-21D0A536C234}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{EC8AE8B0-4DC8-4100-A9CD-3DFB96702EFF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EC8AE8B0-4DC8-4100-A9CD-3DFB96702EFF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EC8AE8B0-4DC8-4100-A9CD-3DFB96702EFF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EC8AE8B0-4DC8-4100-A9CD-3DFB96702EFF}.Release|Any CPU.Build.0 = Release|Any CPU
		{9DD84E71-AC25-49B1-9238-74E1B5C87E48}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9DD84E71-AC25-49B1-9238-74E1B5C87E48}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9DD84E71-AC25-49B1-9238-74E1B5C87E48}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9DD84E71-AC25-49B1-9238-74E1B5C87E48}.Release|Any CPU.Build.0 = Release|Any CPU
		{FC78C09D-2B04-49D5-8E44-8145317C6348}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FC78C09D-2B04-49D5-8E44-8145317C6348}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FC78C09D-2B04-49D5-8E44-8145317C6348}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FC78C09D-2B04-49D5-8E44-8145317C6348}.Release|Any CPU.Build.0 = Release|Any CPU
		{327C413C-ED1D-4BC8-9F37-8EF957E239D2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{327C413C-ED1D-4BC8-9F37-8EF957E239D2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{327C413C-ED1D-4BC8-9F37-8EF957E239D2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{327C413C-ED1D-4BC8-9F37-8EF957E239D2}.Release|Any CPU.Build.0 = Release|Any CPU
		{230125E9-E725-408F-A0D4-21D0A536C234}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{230125E9-E725-408F-A0D4-21D0A536C234}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{230125E9-E725-408F-A0D4-21D0A536C234}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{230125E9-E725-408F-A0D4-21D0A536C234}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{EC8AE8B0-4DC8-4100-A9CD-3DFB96702EFF} = {1C6DBBC6-EDF5-40EF-8F46-AB6CD0ECD7A3}
		{9DD84E71-AC25-49B1-9238-74E1B5C87E48} = {D0383B23-B2B6-4970-B42D-22CD798643FF}
		{FC78C09D-2B04-49D5-8E44-8145317C6348} = {4905F2EC-81A3-421A-BDCD-51454BC0D9AE}
		{327C413C-ED1D-4BC8-9F37-8EF957E239D2} = {4905F2EC-81A3-421A-BDCD-51454BC0D9AE}
		{230125E9-E725-408F-A0D4-21D0A536C234} = {A65273C5-0AFB-4AB0-B1E8-A659476A5104}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {70972633-29F0-414C-A1D3-E7CAC3AF16D8}
	EndGlobalSection
EndGlobal
