# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

trigger:
- uat

pool:
  vmImage: windows2022

variables:
  buildConfiguration: 'Debug'
  dotNetFramework: 'net8.0'
  dotNetVersion: '8.0.x'
  targetRuntime: 'win-x86'
  azureSPNName: 'YOUR-SPN-NAME' #get it from your AzureDevOps portal
  azureAppServiceName: 'Your-Azure-AppService-Name' #get it from your Azure portal

steps:
- task: UseDotNet@2
  inputs:
    version: $(dotNetVersion)
    includePreviewVersions: true
- script: dotnet build --configuration $(buildConfiguration)
  displayName: 'Build .NET 8 Application'

# Publish it as .NET 8 self-contained application for linux runtime
- task: DotNetCoreCLI@2
  inputs:
    command: publish
    publishWebProjects: True
    arguments: '--configuration $(BuildConfiguration) --framework $(dotNetFramework) --runtime $(targetRuntime) --self-contained --output $(Build.ArtifactStagingDirectory)'
    zipAfterPublish: True

# Package the file and uploads them as an artifact of the build
- task: PublishPipelineArtifact@1
  inputs:
    targetPath: '$(Build.ArtifactStagingDirectory)' 
    artifactName: 'MinimalAPI'

#Publish it to the Azure App Service
- task: AzureWebApp@1
  inputs:
    appType: webAppLinux
    azureSubscription: $(azureSPNName) #this is the name of the SPN
    appName: $(azureAppServiceName) #App Service's unique name
    package: $(Build.ArtifactStagingDirectory)/**/*.zip

