<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <ProduceReferenceAssembly>true</ProduceReferenceAssembly>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AsyncAwaitBestPractices" Version="6.0.0" />
    <PackageReference Include="Nancy" Version="2.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="RestSharp" Version="106.12.0" />
    <PackageReference Include="Xamarin.Forms" Version="5.0.0.2083" />  
    <PackageReference Include="Xamarin.Essentials" Version="1.7.0" />  
    <PackageReference Include="Xamarin.Forms.Visual.Material" Version="5.0.0.2083" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Pages\StartupPage.xaml.cs">
      <DependentUpon>StartupPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Pages\TeleHeathPage.xaml.cs">
      <DependentUpon>TeleHeathPage.xaml</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Views\InfoView.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\MonitoringView.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\NotesView.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\ReferralsView.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\SummaryView.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <None Update="Vitals.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>