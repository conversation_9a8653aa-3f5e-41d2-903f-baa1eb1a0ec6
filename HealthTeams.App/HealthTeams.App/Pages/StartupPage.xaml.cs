﻿using System;
using System.ComponentModel;
using System.Threading.Tasks;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace HealthTeams.App.Pages
{
	public partial class StartupPage : ContentPage
	{
		public StartupPage()
		{
			InitializeComponent();
		}

		protected async override void OnAppearing()
		{
			base.OnAppearing();

			await CheckLogin();
		}

		private async Task CheckLogin()
		{
			// should check for valid login instead
			await Task.Delay(2000);

			// only open Login page when no valid login
			await Shell.Current.GoToAsync($"//{nameof(LoginPage)}");
		}
	}
}