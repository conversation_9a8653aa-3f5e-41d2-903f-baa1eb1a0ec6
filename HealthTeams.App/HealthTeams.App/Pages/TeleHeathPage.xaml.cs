﻿using AsyncAwaitBestPractices;
using HealthTeams.App.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Threading.Tasks;
using Xamarin.Essentials;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace HealthTeams.App.Pages
{
	public partial class TeleHeathPage : ContentPage
    {
        //private readonly string _groupCallId = "29228d3e-040e-4656-a70e-890ab4e173e5";

        //private readonly IACSCallingManager _callingManager;
        //private string _token;
        //private readonly UserService _restClient;


        private TeleHeathViewModel _teleHeathViewModel;
		public TeleHeathViewModel TeleHeathViewModel
        {
            get
            {
				if (_teleHeathViewModel == null)
				{
                    _teleHeathViewModel = BindingContext as TeleHeathViewModel;

                }
                return _teleHeathViewModel;
            }
        }

		public TeleHeathPage()
		{
			InitializeComponent();

			var vehicleTypes = new List<string>() { "Summary", "Monitoring", "Info", "Notes", "Referrals", "Scripts", "ETS", "Analyse", "Bill" };
			segment.Children = vehicleTypes;
		}

		private void segment_SelectedItemChanged(object sender, SelectedItemChangedEventArgs e)
		{
			ItemSelectedText.Text = $"{e.SelectedItem}";
		}
	}
}