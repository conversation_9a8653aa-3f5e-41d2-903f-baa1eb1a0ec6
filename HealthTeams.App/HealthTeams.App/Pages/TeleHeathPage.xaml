﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="HealthTeams.App.Pages.TeleHeathPage"
             xmlns:vm="clr-namespace:HealthTeams.App.ViewModels" 
			 xmlns:local="clr-namespace:HealthTeams.App.Views"
			 BindingContext="{Binding TeleHeathViewModel, Source={StaticResource ViewModelLocator}}">

	<ContentPage.Resources>
		<ResourceDictionary>
			<Color x:Key="Accent">#96d1ff</Color>
		</ResourceDictionary>
	</ContentPage.Resources>

	<ContentPage.ToolbarItems>
		<ToolbarItem Name="CallGroup" IconImageSource="Vitals.png" Order="Primary" Priority="0" />
		<!--<ToolbarItem Name="Menu2" Activated="OnClick" Order="Primary" Priority="1" />-->
	</ContentPage.ToolbarItems>

	<Grid RowDefinitions="300,*" Padding="10,10,10,10">

		<Frame Grid.Row="0" Grid.ColumnSpan="4" Grid.Column="0" 
			   BackgroundColor="Black" Opacity="1" CornerRadius="15" Background="Red"
               VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand"
               x:Name="Spinner" IsVisible="{Binding IsConnecting}">
			<ActivityIndicator IsRunning="True" VerticalOptions="Center" Color="White" HorizontalOptions="Center"></ActivityIndicator>
		</Frame>
		<Frame Grid.Row="0" Grid.ColumnSpan="4" Grid.Column="0" 
			   VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand"
			   IsClippedToBounds="True" HasShadow="False"
			   Padding="0" CornerRadius="15" >

			<ContentView x:Name="MainVideoView" BackgroundColor="Black"
					 Content="{Binding MainVideoView}"/>

		</Frame>
		<ScrollView Grid.Row="1" Grid.ColumnSpan="4"
			   VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand">
			<ScrollView.Content>
				<Grid RowDefinitions="90,*" ColumnDefinitions="*,*,*,*" x:Name="SecondaryVideoView">
					<Frame CornerRadius="15" Grid.Row="0" VerticalOptions="Start"
						   Grid.Column="0" IsClippedToBounds="True" HasShadow="False"
						   Padding="0">

						<ContentView HorizontalOptions="Start" VerticalOptions="FillAndExpand" WidthRequest="90" HeightRequest="90"
								 x:Name="LocalVideoView" Content="{Binding LocalVideoView}"
								 Grid.Column="0" BackgroundColor="Black"/>
					</Frame>
					<Frame CornerRadius="15" Grid.Row="0" VerticalOptions="Start"
						   Grid.Column="1" IsClippedToBounds="True" HasShadow="False"
						   Padding="0">

						<ContentView HorizontalOptions="Start" VerticalOptions="FillAndExpand"  WidthRequest="90" HeightRequest="90"
								 x:Name="FirstRemoteVideoView" Content="{Binding FirstRemoteVideoView}"
								 Grid.Column="0" BackgroundColor="Black"/>

					</Frame>
					<Frame CornerRadius="15" Grid.Row="0" VerticalOptions="Start"
						   Grid.Column="2" IsClippedToBounds="True" HasShadow="False"
						   Padding="0">
						<ContentView HorizontalOptions="Start" VerticalOptions="FillAndExpand" WidthRequest="90" HeightRequest="90"
								 x:Name="SecondRemoteVideoView" Content="{Binding SecondRemoteVideoView}"
								 Grid.Column="0" BackgroundColor="Black" />
					</Frame>
					<ImageButton Source="callstart.png"
						HorizontalOptions="Fill"
						VerticalOptions="Fill" Padding="15"
						Grid.Column="3" BackgroundColor="{StaticResource GreenColor}" IsVisible="{Binding IsDisconnected}" Command="{Binding StartCommand}" CornerRadius="15">

					</ImageButton>
					<ImageButton Source="callend.png"
						HorizontalOptions="Fill"
						VerticalOptions="Fill" Padding="15"
						Grid.Column="3" BackgroundColor="{StaticResource RedColor}" IsVisible="{Binding IsConnected}" Command="{Binding HangupCallCommand}" CornerRadius="15">
					</ImageButton>
					<StackLayout Grid.Row="1" Grid.ColumnSpan="4" Margin="0,15,0,0">

						<local:SegmentedBarControl SelectedItemChanged="segment_SelectedItemChanged" SelectedItemChangedCommand="{Binding SelectedItemChangedCommand}"
                             x:Name="segment" 
                             AutoScroll="true"
                             TextColor="Black" StyleClass=""
                             SelectedTextColor="{StaticResource Primary}"
                             SelectedLineColor="{StaticResource Primary}"/>
						
						<Label IsVisible="false" FontAttributes="Bold" FontSize="Large" x:Name="ItemSelectedText" HorizontalOptions="CenterAndExpand" VerticalOptions="CenterAndExpand"/>
						<ContentView x:Name="tabContent" Content="{Binding SelectedTabView}"></ContentView>
					</StackLayout>
					<Label Grid.Row="1" Grid.ColumnSpan="4" IsVisible="false"
						   TextColor="Black" Text="Hello everyone! I'm trying to create my first Xamarin.Forms app. I have some data recieved from api and it is a quite long string. When I'm displaying it in label it only shows 1/5 of the string. There is like two threads about this problem I could barely found in google. I saw answers about custom rendering and changing max lines of Label but it's not easy way to resolve my problem. I made same app in android studio but there is no limit in label. Thanks for all your answers! Hello everyone! I'm trying to create my first Xamarin.Forms app. I have some data recieved from api and it is a quite long string. When I'm displaying it in label it only shows 1/5 of the string. There is like two threads about this problem I could barely found in google. I saw answers about custom rendering and changing max lines of Label but it's not easy way to resolve my problem. I made same app in android studio but there is no limit in label. Thanks for all your answers!Hello everyone! I'm trying to create my first Xamarin.Forms app. I have some data recieved from api and it is a quite long string. When I'm displaying it in label it only shows 1/5 of the string. There is like two threads about this problem I could barely found in google. I saw answers about custom rendering and changing max lines of Label but it's not easy way to resolve my problem. I made same app in android studio but there is no limit in label. Thanks for all your answers!Hello everyone! I'm trying to create my first Xamarin.Forms app. I have some data recieved from api and it is a quite long string. When I'm displaying it in label it only shows 1/5 of the string. There is like two threads about this problem I could barely found in google. I saw answers about custom rendering and changing max lines of Label but it's not easy way to resolve my problem. I made same app in android studio but there is no limit in label. Thanks for all your answers!"></Label>
				</Grid>
			</ScrollView.Content>
		</ScrollView>
	</Grid>
</ContentPage>
