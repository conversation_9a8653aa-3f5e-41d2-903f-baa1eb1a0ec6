﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="HealthTeams.App.Pages.StartupPage"
             Shell.NavBarIsVisible="False"
			 Visual="Material"
             Title="{Binding Title}">
    
    <ContentPage.Resources>
        <ResourceDictionary>
			<Color x:Key="Accent">#2FC4CA</Color>
        </ResourceDictionary>
    </ContentPage.Resources>

	<ContentPage.Content>
		<StackLayout VerticalOptions="Center">
			<ActivityIndicator IsRunning="True" />
		</StackLayout>
	</ContentPage.Content>

</ContentPage>
