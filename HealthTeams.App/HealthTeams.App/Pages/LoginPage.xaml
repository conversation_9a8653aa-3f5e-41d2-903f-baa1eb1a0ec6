﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			 mc:Ignorable="d"
             x:Class="HealthTeams.App.Pages.LoginPage"
             Shell.NavBarIsVisible="False"
			 BindingContext="{Binding LoginViewModel, Source={StaticResource ViewModelLocator}}">

	<ContentPage.Content>
		<Grid Padding="10,100,10,0" RowDefinitions="50,50,50,50,*" ColumnDefinitions="*,*">
			<Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2" HorizontalTextAlignment="Center" Text="Welcome to Health Teams"></Label>
			<Entry Grid.Row="1" Grid.Column="0" Placeholder="First Name" Text="{Binding FirstName}"></Entry>
			<Entry Grid.Row="1" Grid.Column="1" Placeholder="Last Name" Text="{Binding LastName}"></Entry>
			<Button Grid.Row="2" Grid.Column="0" VerticalOptions="Center" Text="Doctor" Command="{Binding RoleCommand}" CommandParameter="Doctor" />
			<Button Grid.Row="2" Grid.Column="1" VerticalOptions="Center" Text="Patient" Command="{Binding RoleCommand}" CommandParameter="Resident" />
			<Button Grid.Row="3" Grid.Column="0" VerticalOptions="Center" Text="Nurse" Command="{Binding RoleCommand}" CommandParameter="Nurse" />
			<Button Grid.Row="3" Grid.Column="1" VerticalOptions="Center" Text="Family Member" Command="{Binding RoleCommand}" CommandParameter="Family" />
		</Grid>
    </ContentPage.Content>
</ContentPage>