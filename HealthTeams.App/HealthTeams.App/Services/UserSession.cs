﻿using HealthTeams.App.Models;

namespace HealthTeams.App.Services
{
	public class UserSession
	{
		public string FirstName { get; internal set; }
		public string LastName { get; internal set; }
		public string Role { get; internal set; }
		public string UserExId
		{
			get
			{
				// for testing only, should be removed
				if (Role == UserType.Doctor)
					return "005D8218-8BD4-4BB5-8D68-4FFBDA9DA566";

				if (Role == UserType.Resident)
					return "D14EB53C-F2F5-4BD3-9442-84F1DEC3D693";

				if (Role == UserType.Nurse)
					return "35AEABF6-F3FA-43F6-B09D-6F94D5020BB2";

				if (Role == UserType.Family)
					return "A61D7615-C519-4456-A8A0-98E368B178E3";

				return string.Empty;
			}
		}

		public string GroupCallId { get; internal set; } = "8d23d5d6-2ea7-401c-8417-c1b08f06f192";//"29228d3e-040e-4656-a70e-890ab4e173e5";

		public User User { get; internal set; }
	}
}