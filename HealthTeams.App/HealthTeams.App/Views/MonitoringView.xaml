﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="HealthTeams.App.Views.MonitoringView">
  <ContentView.Content>
		<Grid Margin="0,15,0,0" RowDefinitions="auto,90,90,auto,90,90,*" ColumnDefinitions="*,*,*,*,*" RowSpacing="15">
			<Label Grid.ColumnSpan="5">
				<Label.FormattedText>
					<FormattedString>
						<Span Text="Captured " TextColor="Gray" FontSize="Small" />
						<Span Text="02/06/2021" TextColor="{StaticResource Primary}" FontSize="Small" />
						<Span Text=" at " TextColor="Gray" FontSize="Small" />
						<Span Text="9:00am" TextColor="{StaticResource Primary}" FontSize="Small" />

					</FormattedString>
				</Label.FormattedText>
			</Label>
			<Grid Grid.ColumnSpan="5" Grid.Row="1" ColumnDefinitions="*,*,*,*,*">
				<Grid RowDefinitions="Auto,*" Grid.Column="0">
					<Label Grid.Row="0" Text="BP Systolic" FontSize="Micro" HorizontalTextAlignment="Center"></Label>
					<Frame Grid.Row="1" CornerRadius="15" BackgroundColor="#22DC04" HasShadow="False" Margin="8" Padding="0" IsClippedToBounds="True">
						<Label Text="110" FontSize="Micro" TextColor="White" 
							   HorizontalTextAlignment="Center" VerticalTextAlignment="Center"></Label>
					</Frame>
				</Grid>
				<Grid RowDefinitions="Auto,*" Grid.Column="1">
					<Label Grid.Row="0" Text="BP Diastolic" FontSize="Micro" HorizontalTextAlignment="Center"></Label>
					<Frame Grid.Row="1" CornerRadius="15" BackgroundColor="#22DC04" HasShadow="False" Margin="8" Padding="0" IsClippedToBounds="True">
						<Label Text="73" FontSize="Micro" TextColor="White" 
							   HorizontalTextAlignment="Center" VerticalTextAlignment="Center"></Label>
					</Frame>
				</Grid>
				<Grid RowDefinitions="Auto,*" Grid.Column="2">
					<Label Grid.Row="0" Text="SpO2" FontSize="Micro" HorizontalTextAlignment="Center"></Label>
					<Frame Grid.Row="1" CornerRadius="15" BackgroundColor="#FFA800" HasShadow="False" Margin="8" Padding="0" IsClippedToBounds="True">
						<Label Text="100" FontSize="Micro" TextColor="White" 
							   HorizontalTextAlignment="Center" VerticalTextAlignment="Center"></Label>
					</Frame>
				</Grid>
				<Grid RowDefinitions="Auto,*" Grid.Column="3">
					<Label Grid.Row="0" Text="Temp" FontSize="Micro" HorizontalTextAlignment="Center"></Label>
					<Frame Grid.Row="1" CornerRadius="15" BackgroundColor="#22DC04" HasShadow="False" Margin="8" Padding="0" IsClippedToBounds="True">
						<Label Text="37" FontSize="Micro" TextColor="White" 
							   HorizontalTextAlignment="Center" VerticalTextAlignment="Center"></Label>
					</Frame>
				</Grid>
				<Grid RowDefinitions="Auto,*" Grid.Column="4">
					<Label Grid.Row="0" Text="Glucose" FontSize="Micro" HorizontalTextAlignment="Center"></Label>
					<Frame Grid.Row="1" CornerRadius="15" BackgroundColor="#FFA800" HasShadow="False" Margin="8" Padding="0" IsClippedToBounds="True">
						<Label Text="170" FontSize="Micro" TextColor="White" 
							   HorizontalTextAlignment="Center" VerticalTextAlignment="Center"></Label>
					</Frame>
				</Grid>
			</Grid>
			<Grid Grid.ColumnSpan="5" Grid.Row="2" ColumnDefinitions="*,*,*,*,*">
				<Grid RowDefinitions="Auto,*" Grid.Column="0">
					<Label Grid.Row="0" Text="BP Systolic" FontSize="Micro" HorizontalTextAlignment="Center"></Label>
					<Frame Grid.Row="1" CornerRadius="15" BackgroundColor="#22DC04" HasShadow="False" Margin="8" Padding="0" IsClippedToBounds="True">
						<Label Text="61" FontSize="Micro" TextColor="White" 
							   HorizontalTextAlignment="Center" VerticalTextAlignment="Center"></Label>
					</Frame>
				</Grid>
				<Grid RowDefinitions="Auto,*" Grid.Column="1">
					<Label Grid.Row="0" Text="BP Systolic" FontSize="Micro" HorizontalTextAlignment="Center"></Label>
					<Frame Grid.Row="1" CornerRadius="15" BackgroundColor="#22DC04" HasShadow="False" Margin="8" Padding="0" IsClippedToBounds="True">
						<Label Text="82" FontSize="Micro" TextColor="White" 
							   HorizontalTextAlignment="Center" VerticalTextAlignment="Center"></Label>
					</Frame>
				</Grid>
				<Grid RowDefinitions="Auto,*" Grid.Column="2">
					<Label Grid.Row="0" Text="BP Systolic" FontSize="Micro" HorizontalTextAlignment="Center"></Label>
					<Frame Grid.Row="1" CornerRadius="15" BackgroundColor="#22DC04" HasShadow="False" Margin="8" Padding="0" IsClippedToBounds="True">
						<Label Text="No" FontSize="Micro" TextColor="White" 
							   HorizontalTextAlignment="Center" VerticalTextAlignment="Center"></Label>
					</Frame>
				</Grid>
			</Grid>
			<Label Grid.ColumnSpan="5" Grid.Row="3">
				<Label.FormattedText>
					<FormattedString>
						<Span Text="Captured " TextColor="Gray" FontSize="Small" />
						<Span Text="02/06/2021" TextColor="{StaticResource Primary}" FontSize="Small" />
						<Span Text=" at " TextColor="Gray" FontSize="Small" />
						<Span Text="9:00am" TextColor="{StaticResource Primary}" FontSize="Small" />

					</FormattedString>
				</Label.FormattedText>
			</Label>
			<Grid Grid.ColumnSpan="5" Grid.Row="4" ColumnDefinitions="*,*,*,*,*">
				<Grid RowDefinitions="Auto,*" Grid.Column="0">
					<Label Grid.Row="0" Text="BP Systolic" FontSize="Micro" HorizontalTextAlignment="Center"></Label>
					<Frame Grid.Row="1" CornerRadius="15" BackgroundColor="#22DC04" HasShadow="False" Margin="8" Padding="0" IsClippedToBounds="True">
						<Label Text="110" FontSize="Micro" TextColor="White" 
							   HorizontalTextAlignment="Center" VerticalTextAlignment="Center"></Label>
					</Frame>
				</Grid>
				<Grid RowDefinitions="Auto,*" Grid.Column="1">
					<Label Grid.Row="0" Text="BP Diastolic" FontSize="Micro" HorizontalTextAlignment="Center"></Label>
					<Frame Grid.Row="1" CornerRadius="15" BackgroundColor="#22DC04" HasShadow="False" Margin="8" Padding="0" IsClippedToBounds="True">
						<Label Text="73" FontSize="Micro" TextColor="White" 
							   HorizontalTextAlignment="Center" VerticalTextAlignment="Center"></Label>
					</Frame>
				</Grid>
				<Grid RowDefinitions="Auto,*" Grid.Column="2">
					<Label Grid.Row="0" Text="SpO2" FontSize="Micro" HorizontalTextAlignment="Center"></Label>
					<Frame Grid.Row="1" CornerRadius="15" BackgroundColor="#FFA800" HasShadow="False" Margin="8" Padding="0" IsClippedToBounds="True">
						<Label Text="100" FontSize="Micro" TextColor="White" 
							   HorizontalTextAlignment="Center" VerticalTextAlignment="Center"></Label>
					</Frame>
				</Grid>
				<Grid RowDefinitions="Auto,*" Grid.Column="3">
					<Label Grid.Row="0" Text="Temp" FontSize="Micro" HorizontalTextAlignment="Center"></Label>
					<Frame Grid.Row="1" CornerRadius="15" BackgroundColor="#22DC04" HasShadow="False" Margin="8" Padding="0" IsClippedToBounds="True">
						<Label Text="37" FontSize="Micro" TextColor="White" 
							   HorizontalTextAlignment="Center" VerticalTextAlignment="Center"></Label>
					</Frame>
				</Grid>
				<Grid RowDefinitions="Auto,*" Grid.Column="4">
					<Label Grid.Row="0" Text="Glucose" FontSize="Micro" HorizontalTextAlignment="Center"></Label>
					<Frame Grid.Row="1" CornerRadius="15" BackgroundColor="#FFA800" HasShadow="False" Margin="8" Padding="0" IsClippedToBounds="True">
						<Label Text="170" FontSize="Micro" TextColor="White" 
							   HorizontalTextAlignment="Center" VerticalTextAlignment="Center"></Label>
					</Frame>
				</Grid>
			</Grid>
			<Grid Grid.ColumnSpan="5" Grid.Row="5" ColumnDefinitions="*,*,*,*,*">
				<Grid RowDefinitions="Auto,*" Grid.Column="0">
					<Label Grid.Row="0" Text="BP Systolic" FontSize="Micro" HorizontalTextAlignment="Center"></Label>
					<Frame Grid.Row="1" CornerRadius="15" BackgroundColor="#22DC04" HasShadow="False" Margin="8" Padding="0" IsClippedToBounds="True">
						<Label Text="61" FontSize="Micro" TextColor="White" 
							   HorizontalTextAlignment="Center" VerticalTextAlignment="Center"></Label>
					</Frame>
				</Grid>
				<Grid RowDefinitions="Auto,*" Grid.Column="1">
					<Label Grid.Row="0" Text="BP Systolic" FontSize="Micro" HorizontalTextAlignment="Center"></Label>
					<Frame Grid.Row="1" CornerRadius="15" BackgroundColor="#22DC04" HasShadow="False" Margin="8" Padding="0" IsClippedToBounds="True">
						<Label Text="82" FontSize="Micro" TextColor="White" 
							   HorizontalTextAlignment="Center" VerticalTextAlignment="Center"></Label>
					</Frame>
				</Grid>
				<Grid RowDefinitions="Auto,*" Grid.Column="2">
					<Label Grid.Row="0" Text="BP Systolic" FontSize="Micro" HorizontalTextAlignment="Center"></Label>
					<Frame Grid.Row="1" CornerRadius="15" BackgroundColor="#22DC04" HasShadow="False" Margin="8" Padding="0" IsClippedToBounds="True">
						<Label Text="No" FontSize="Micro" TextColor="White" 
							   HorizontalTextAlignment="Center" VerticalTextAlignment="Center"></Label>
					</Frame>
				</Grid>
			</Grid>
		</Grid>
	</ContentView.Content>
</ContentView>