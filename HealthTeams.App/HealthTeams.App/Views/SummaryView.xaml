﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="HealthTeams.App.Views.SummaryView">
  <ContentView.Content>
		<Grid RowDefinitions="auto,auto,auto,auto,auto,auto,auto,auto,auto,*" RowSpacing="9">
			<Label Grid.ColumnSpan="5" Grid.Row="0" Text="Summary" Margin="0,14"></Label>

			<Label  Grid.ColumnSpan="5" Grid.Row="1" Text="Chronic Diseases" TextColor="{StaticResource Primary}" FontSize="Micro"></Label>
			<Label  Grid.ColumnSpan="5" Grid.Row="2" Text="None" TextColor="{StaticResource BlackFontColor}" FontSize="Micro"></Label>
			<Label  Grid.ColumnSpan="5" Grid.Row="3" Text="Allergies" TextColor="{StaticResource Primary}" FontSize="Micro"></Label>
			<Label  Grid.ColumnSpan="5" Grid.Row="4" Text="Penicilin" TextColor="{StaticResource BlackFontColor}" FontSize="Micro"></Label>
			<Label  Grid.ColumnSpan="5" Grid.Row="5" Text="Medication" TextColor="{StaticResource Primary}" FontSize="Micro"></Label>
			<Label  Grid.ColumnSpan="5" Grid.Row="6" Text="Med 1, Med 2, Med 3" TextColor="{StaticResource BlackFontColor}" FontSize="Micro"></Label>
			
			<Label Grid.ColumnSpan="5" Grid.Row="7" Text="Diagnostic Reports" Margin="0,14"></Label>
			<Grid Grid.ColumnSpan="5" Grid.Row="8" RowDefinitions="auto,auto,auto,auto,*" ColumnDefinitions="*,*,*,*">
				<Label Grid.Column="0" Grid.Row="0" Text="Date" TextColor="{StaticResource Primary}" FontSize="Micro"></Label>
				<Label Grid.Column="1" Grid.Row="0" Text="Type" TextColor="{StaticResource Primary}" FontSize="Micro"></Label>
				<Label Grid.Column="2" Grid.Row="0" Text="Provider" TextColor="{StaticResource Primary}" FontSize="Micro"></Label>
				<Label Grid.Column="3" Grid.Row="0" Text="Subject" TextColor="{StaticResource Primary}" FontSize="Micro"></Label>

				<Label Grid.Column="0" Grid.Row="1" Text="24/03/2020" TextColor="{StaticResource BlackFontColor}" FontSize="Micro"></Label>
				<Label Grid.Column="1" Grid.Row="1" Text="Radiology" TextColor="{StaticResource BlackFontColor}" FontSize="Micro"></Label>
				<Label Grid.Column="2" Grid.Row="1" Text="Sydney XRay" TextColor="{StaticResource BlackFontColor}" FontSize="Micro"></Label>
				<Label Grid.Column="3" Grid.Row="1" Text="CT Abdomen" TextColor="{StaticResource BlackFontColor}" FontSize="Micro"></Label>

				<Label Grid.Column="0" Grid.Row="2" Text="24/03/2020" TextColor="{StaticResource BlackFontColor}" FontSize="Micro"></Label>
				<Label Grid.Column="1" Grid.Row="2" Text="Radiology" TextColor="{StaticResource BlackFontColor}" FontSize="Micro"></Label>
				<Label Grid.Column="2" Grid.Row="2" Text="Sydney XRay" TextColor="{StaticResource BlackFontColor}" FontSize="Micro"></Label>
				<Label Grid.Column="3" Grid.Row="2" Text="CT Abdomen" TextColor="{StaticResource BlackFontColor}" FontSize="Micro"></Label>

				<Label Grid.Column="0" Grid.Row="3" Text="24/03/2020" TextColor="{StaticResource BlackFontColor}" FontSize="Micro"></Label>
				<Label Grid.Column="1" Grid.Row="3" Text="Radiology" TextColor="{StaticResource BlackFontColor}" FontSize="Micro"></Label>
				<Label Grid.Column="2" Grid.Row="3" Text="Sydney XRay" TextColor="{StaticResource BlackFontColor}" FontSize="Micro"></Label>
				<Label Grid.Column="3" Grid.Row="3" Text="CT Abdomen" TextColor="{StaticResource BlackFontColor}" FontSize="Micro"></Label>
			</Grid>
		</Grid>
  </ContentView.Content>
</ContentView>