﻿using HealthTeams.App.Services;
using Nancy.TinyIoc;
using System;
using System.Collections.Generic;
using System.Text;

namespace HealthTeams.App.ViewModels
{
	public class ViewModelLocator
	{
		private TinyIoCContainer _container;
		public ViewModelLocator()
		{
			_container = new TinyIoCContainer();
			_container.Register<UserSession>().AsSingleton();
			_container.Register<LoginViewModel>();
			_container.Register<TeleHeathViewModel>();
		}

		public LoginViewModel LoginViewModel => _container.Resolve<LoginViewModel>();
		
		public TeleHeathViewModel TeleHeathViewModel => _container.Resolve<TeleHeathViewModel>();
	}
}
