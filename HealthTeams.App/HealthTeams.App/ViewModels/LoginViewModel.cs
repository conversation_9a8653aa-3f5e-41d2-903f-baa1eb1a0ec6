﻿using HealthTeams.App.Models;
using HealthTeams.App.Services;
using HealthTeams.App.Pages;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;

namespace HealthTeams.App.ViewModels
{
	public class LoginViewModel : BaseViewModel
	{
		public LoginViewModel(UserSession userSession)
		{
			_userSession = userSession;

			RoleCommand = new Command<string>(async (role) =>
			{
				Role = role;
				await OnLoginClicked();
			});
		}

		private readonly UserSession _userSession;

		string _firstName = "";
		public string FirstName
		{
			get { return _firstName; }
			set { SetProperty(ref _firstName, value); }
		}

		string _lastName = "";
		public string LastName
		{
			get { return _lastName; }
			set { SetProperty(ref _lastName, value); }
		}
		public string Role { get; private set; }
		public ICommand RoleCommand { get; set; }
		public UserType UserType { get; set; }


		private async Task OnLoginClicked()
		{
			_userSession.FirstName = FirstName;
			_userSession.LastName = LastName;
			_userSession.Role = Role;
			// Prefixing with `//` switches to a different navigation stack instead of pushing to the active one
			await Shell.Current.GoToAsync($"//{nameof(TeleHeathPage)}");
		}
	}
}
