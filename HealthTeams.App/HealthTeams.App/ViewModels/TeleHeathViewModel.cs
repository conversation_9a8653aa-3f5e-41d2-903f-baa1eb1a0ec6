﻿using AsyncAwaitBestPractices;
using HealthTeams.App.Models;
using HealthTeams.App.Services;
using HealthTeams.App.Views;
using System;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;

namespace HealthTeams.App.ViewModels
{
	public class TeleHeathViewModel : BaseViewModel
	{
		public TeleHeathViewModel(UserSession userSession)
		{
			_userSession = userSession;
			Title = "Tele Health";
			CallEchoCommand = new Command(async () => await CallEchoService());
			CallEchoCommand = new Command(async () => await StartGroupCall(userSession.GroupCallId));
			StartPhoneCommand = new Command<string>(async (string number) => await StartPhoneCall(number));
			HangupCallCommand = new Command(() => HangupCall());
			StartCommand = new Command(async () =>
			{
				if (await GetTokenAndInitACSAsync())
				{
					await StartGroupCall(userSession.GroupCallId);
				}
			});
			SelectedItemChangedCommand = new Command<string>((selectedItem) => OnSelectedItemChanged(selectedItem));


			_userService = new UserService();
			CallingManager = DependencyService.Get<IACSCallingManager>();
			CallingManager.LocalVideoAdded += delegate (object sender, View view)
			{
				Device.BeginInvokeOnMainThread(() => LocalVideoView = view);
			};
			CallingManager.RemoteVideoAdded += async delegate (object sender, RemoteVideoData remoteVideoData)
			{
				var userScreenType = await GetUserScreenType(remoteVideoData.AcsUserId);
				Device.BeginInvokeOnMainThread(() =>
				{
					switch (userScreenType)
					{
						case ScreenType.Main:
							MainVideoView = remoteVideoData.FormsView;
							break;
						case ScreenType.Secondary:
							{
								if (FirstRemoteVideoView == null)
								{
									FirstRemoteVideoView = remoteVideoData.FormsView;
								}
								else if (SecondRemoteVideoView == null)
								{
									SecondRemoteVideoView = remoteVideoData.FormsView;
								}
							}
							break;
					}
				});
			};
			CallingManager.RemoteVideoRemoved += delegate (object sender, RemoteVideoData remoteVideoData)
			{
				Device.BeginInvokeOnMainThread(() =>
				{
					if (MainVideoView != null && MainVideoView == remoteVideoData.FormsView)
					{
						MainVideoView = null;
					}
					else if (FirstRemoteVideoView != null && FirstRemoteVideoView == remoteVideoData.FormsView)
					{
						FirstRemoteVideoView = null;
					}
					else if (SecondRemoteVideoView != null && SecondRemoteVideoView == remoteVideoData.FormsView)
					{
						SecondRemoteVideoView = null;
					}
				});
			};
		}

		private async Task<ScreenType> GetUserScreenType(string acsUserId)
		{
			var userRole = await GetUserRole(acsUserId, "005D8218-8BD4-4BB5-8D68-4FFBDA9DA566"
					, "D14EB53C-F2F5-4BD3-9442-84F1DEC3D693"
					, "35AEABF6-F3FA-43F6-B09D-6F94D5020BB2"
					, "A61D7615-C519-4456-A8A0-98E368B178E3");
			switch (userRole)
			{
				case UserType.Resident:
					return ScreenType.Main;

				case UserType.Doctor:
					return _userSession.Role == UserType.Resident ? ScreenType.Main : ScreenType.Secondary;

				case UserType.Nurse:
				case UserType.Family:
					return ScreenType.Secondary;

				default:
					return ScreenType.Main;
			}
		}

		private async Task<string> GetUserRole(string acsUserId, params string[] ourUsers)
		{
			var meeting = await _userService.GetUserList(_userSession.GroupCallId);
			var meetUsers = meeting?.MeetUsers?.Where(u => ourUsers.Contains(u.UserId));
			var meetUser = meetUsers?.FirstOrDefault(u => u.AcsUserId == acsUserId);
			return meetUser?.Role;
		}

		public ICommand CallEchoCommand { get; }
		public ICommand StartGroupCommand { get; }
		public ICommand StartPhoneCommand { get; }
		public ICommand HangupCallCommand { get; }
		public ICommand StartCommand { get; }
		public ICommand SelectedItemChangedCommand { get; }

		public IACSCallingManager CallingManager { get; }


		private View _localVideoView;
		public View LocalVideoView
		{
			get
			{
				return _localVideoView;
			}
			private set
			{
				SetProperty(ref _localVideoView, value);
			}
		}

		private View _mainVideoView;
		public View MainVideoView
		{
			get
			{
				return _mainVideoView;
			}
			private set
			{
				SetProperty(ref _mainVideoView, value);
			}
		}

		private View _firstRemoteVideoView;
		public View FirstRemoteVideoView
		{
			get
			{
				return _firstRemoteVideoView;
			}
			private set
			{
				SetProperty(ref _firstRemoteVideoView, value);
			}
		}

		private View _secondRemoteVideoView;
		public View SecondRemoteVideoView
		{
			get
			{
				return _secondRemoteVideoView;
			}
			private set
			{
				SetProperty(ref _secondRemoteVideoView, value);
			}
		}


		bool _isConnecting = false;
		public bool IsConnecting
		{
			get => _isConnecting;
			private set => SetProperty(ref _isConnecting, value);
		}

		bool _isConnected = false;
		public bool IsConnected
		{
			get => _isConnected;
			private set
			{
				IsDisconnected = !value;
				SetProperty(ref _isConnected, value);
			}
		}

		bool _isDisconnected = true;
		public bool IsDisconnected
		{
			get => _isDisconnected;
			private set => SetProperty(ref _isDisconnected, value);
		}

		private readonly UserSession _userSession;
		private UserService _userService;
		private string _token;

		View _selectedTabView;
		public View SelectedTabView
		{
			get => _selectedTabView;
			private set => SetProperty(ref _selectedTabView, value);
		}


		private async Task<bool> GetTokenAndInitACSAsync()
		{
			IsConnecting = true;
			try
			{
				try
				{
					var createUser = new CreateUser
					{
						firstName = _userSession.FirstName,
						lastName = _userSession.LastName,
						role = _userSession.Role,
						userExId = _userSession.UserExId,
						meetingId = _userSession.GroupCallId,
					};
					_userSession.User = await _userService.UpsertUser(createUser);
					_token = _userSession.User?.acsToken;
				}
				catch (Exception e)
				{
					Debug.WriteLine($"Failed to get token: {e.Message}");
					return false;
				}

				try
				{
					await CallingManager.Init(_token);
				}
				catch (Exception e)
				{
					Debug.WriteLine($"Failed to init ACS: {e.Message}");
					return false;
				}
				Debug.WriteLine("Got token and initialized ACS");
				return true;
			}
			finally
			{
				IsConnecting = false;
			}
		}


		private void GetToken()
		{
			GetTokenAndInitACSAsync().SafeFireAndForget();
		}


		private async Task CallEchoService()
		{
			await AskForPermissions();

			var videoCalling = DependencyService.Get<IACSCallingManager>();
			videoCalling.CallEchoService();
		}

		private async Task StartGroupCall(string groupId)
		{
			if (await AskForPermissions())
			{
				var groupIdGuid = new Guid(groupId);

				await CallingManager.JoinGroup(groupIdGuid);

				IsConnected = true;
			}
		}

		private async Task StartPhoneCall(string number)
		{
			await AskForPermissions();
			var phoneNumber = number;

			CallingManager.CallPhone(phoneNumber);

		}

		private void HangupCall()
		{
			LocalVideoView = null;
			MainVideoView = null;
			FirstRemoteVideoView = null;
			SecondRemoteVideoView = null;

			CallingManager.Hangup();

			IsConnected = false;
		}

		private async Task<bool> AskForPermissions()
		{
			var permissionService = new PermissionService();
			return await permissionService.CheckAndAskForCameraPermission()
				&& await permissionService.CheckAndAskForLocationPermission()
				&& await permissionService.CheckAndAskForMicrophonePermission();
		}


		public View SummaryView => new SummaryView();
		public View InfoView => new InfoView();
		public View NotesView => new NotesView();
		public View ReferralsView => new ReferralsView();
		public View MonitoringView => new MonitoringView();

		private void OnSelectedItemChanged(string selectedItem)
		{
			switch (selectedItem)
			{
				case "Summary":
					SelectedTabView = SummaryView;
					break;
				case "Info":
					SelectedTabView = InfoView;
					break;
				case "Notes":
					SelectedTabView = NotesView;
					break;
				case "Referrals":
					SelectedTabView = ReferralsView;
					break;
				case "Monitoring":
					SelectedTabView = MonitoringView;
					break;
				case "Scripts":
				case "ETS":
				case "Analyse":
				case "Bill":
				default:
					SelectedTabView = null;
					break;
			}
		}

	}
}