﻿using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using HealthTeams.App.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
//using RestSharp;

namespace HealthTeams.App
{
	public class UserService
    {
        public UserService()
        {

        }

        private const string ApiUrl = "tokens/";
        private const string Ip = "************";//"**************";
        private static readonly string Server = $"https://{Ip}:81/";

        private JsonSerializerSettings _jsonSerializerSettings = new JsonSerializerSettings
        {
            ContractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            }
        };



		//public async Task<AzureCommunicationToken> GetToken(string userId)
		//{
		//	var url = $"{Server}{ApiUrl}provisionUser?userId={HttpUtility.UrlEncode(userId)}";
		//	HttpClientHandler clientHandler = new HttpClientHandler();
		//	clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };

		//	using var wc = new HttpClient(clientHandler);
		//	var message = new HttpRequestMessage(HttpMethod.Post, url);
		//	message.Headers.Add("Accept", "application/json, text/plain, */*");
		//	//message.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/json");

		//	var response = await wc.SendAsync(message);
		//	var body = await response.Content.ReadAsStringAsync();
		//	return JsonConvert.DeserializeObject<AzureCommunicationToken>(body, _jsonSerializerSettings);
		//}

		//public class AzureCommunicationToken
		//{
		//	public string Token { get; set; }
		//	public string Expiration { get; set; }
		//}

		private static readonly string _baseUrl = $"https://ht-bff-api-uat-asp.azurewebsites.net/";
        private string _baseClientUrl = _baseUrl + "api/Client";
        private string _baseTelehealthUrl = _baseUrl + "api/Telehealth";

        public async Task<User> UpsertUser(CreateUser createUser)
        {
            var requestData = JsonConvert.SerializeObject(createUser);

            var url = $"{_baseClientUrl}/UpsertUser";

			HttpClientHandler clientHandler = new HttpClientHandler();
			clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };

			using var wc = new HttpClient(clientHandler);
            wc.DefaultRequestHeaders
                .Accept
                .Add(new MediaTypeWithQualityHeaderValue("application/json"));

            var message = new HttpRequestMessage(HttpMethod.Post, url);
            message.Content = new StringContent(requestData, Encoding.UTF8, "application/json");

            var response = await wc.SendAsync(message);
			var body = await response.Content.ReadAsStringAsync();
            var user = JsonConvert.DeserializeObject<User>(body, _jsonSerializerSettings);
            return user;
        }


        public async Task<MeetingDetails> GetUserList(string meetingId)
        {
            var url = $"{_baseTelehealthUrl}/GetMeetingDetails/{meetingId}";

            HttpClientHandler clientHandler = new HttpClientHandler();
            clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };

            using var wc = new HttpClient(clientHandler);
            var message = new HttpRequestMessage(HttpMethod.Get, url);
            message.Headers.Add("Accept", "application/json");

            var response = await wc.SendAsync(message);
            var body = await response.Content.ReadAsStringAsync();
            var metting = JsonConvert.DeserializeObject<MeetingDetails>(body, _jsonSerializerSettings);
            return metting;
        }
    }
}