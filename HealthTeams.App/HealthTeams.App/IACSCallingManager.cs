using System;
using System.Threading.Tasks;
using Xamarin.Forms;


public interface IACSCallingManager
{
	Task<bool> Init(string token);
    Task JoinGroup(Guid groupID);
    void Hangup();
    void CallEchoService();
    event EventHandler<View> LocalVideoAdded;
    event EventHandler<RemoteVideoData> RemoteVideoAdded;
    event EventHandler<RemoteVideoData> RemoteVideoRemoved;
    void CallPhone(string phoneNumber);
}
