﻿<?xml version="1.0" encoding="utf-8" ?>
<Application xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
			 xmlns:vm="clr-namespace:HealthTeams.App.ViewModels"
			 x:Class="HealthTeams.App.App">
    <!--
        Define global resources and styles here, that apply to all pages in your app.
    -->
    <Application.Resources>
        <ResourceDictionary>
			<Color x:Key="Primary">#2FC4CA</Color>
			<Color x:Key="AppBgColor">#FFFFFF</Color>
			<Color x:Key="ScreenBgColor">#F7F7F7</Color>
			<Color x:Key="BlackFontColor">#1E1E1E</Color>
			<Color x:Key="RedColor">#c2334d</Color>
			<Color x:Key="GreenColor">#22DC04</Color>
			<Style TargetType="Button">
                <Setter Property="TextColor" Value="White"></Setter>
                <Setter Property="VisualStateManager.VisualStateGroups">
                    <VisualStateGroupList>
                        <VisualStateGroup x:Name="CommonStates">
                            <VisualState x:Name="Normal">
                                <VisualState.Setters>
                                    <Setter Property="BackgroundColor" Value="{StaticResource Primary}" />
                                </VisualState.Setters>
                            </VisualState>
                            <VisualState x:Name="Disabled">
                                <VisualState.Setters>
                                    <Setter Property="BackgroundColor" Value="#332196F3" />
                                </VisualState.Setters>
                            </VisualState>
                        </VisualStateGroup>
                    </VisualStateGroupList>
                </Setter>
            </Style>
		</ResourceDictionary>
		<vm:ViewModelLocator x:Key="ViewModelLocator" />
	</Application.Resources>
</Application>
