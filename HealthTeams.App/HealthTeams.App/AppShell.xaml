<?xml version="1.0" encoding="UTF-8"?>
<Shell xmlns="http://xamarin.com/schemas/2014/forms" 
       xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
       xmlns:local="clr-namespace:HealthTeams.App.Pages"
       Title="HealthTeams.App"
       x:Class="HealthTeams.App.AppShell" 	   >
	<Shell.TitleView>
		<Image Source="HealthTeamsLogo.png"></Image>
	</Shell.TitleView>
	<!--
        The overall app visual hierarchy is defined here, along with navigation.
    
        https://docs.microsoft.com/xamarin/xamarin-forms/app-fundamentals/shell/
    -->

    <Shell.Resources>
        <ResourceDictionary>
            <Style x:Key="BaseStyle" TargetType="Element">
                <Setter Property="Shell.BackgroundColor" Value="{StaticResource AppBgColor}" />
				<Setter Property="Shell.ForegroundColor" Value="{StaticResource Primary}" />
				<Setter Property="Shell.TitleColor" Value="{StaticResource Primary}" />
                <Setter Property="Shell.DisabledColor" Value="#B4FFFFFF" />
                <Setter Property="Shell.UnselectedColor" Value="#95FFFFFF" />
                <Setter Property="Shell.TabBarBackgroundColor" Value="{StaticResource Primary}" />
                <Setter Property="Shell.TabBarForegroundColor" Value="White"/>
                <Setter Property="Shell.TabBarUnselectedColor" Value="#95FFFFFF"/>
                <Setter Property="Shell.TabBarTitleColor" Value="White"/>
            </Style>
            <Style TargetType="TabBar" BasedOn="{StaticResource BaseStyle}" />
            <Style TargetType="FlyoutItem" BasedOn="{StaticResource BaseStyle}" />

            <!--
            Default Styles for all Flyout Items
            https://docs.microsoft.com/xamarin/xamarin-forms/app-fundamentals/shell/flyout#flyoutitem-and-menuitem-style-classes
            -->
            <Style Class="FlyoutItemLabelStyle" TargetType="Label">
                <Setter Property="TextColor" Value="White"></Setter>
            </Style>
            <Style Class="FlyoutItemLayoutStyle" TargetType="Layout" ApplyToDerivedTypes="True">
                <Setter Property="VisualStateManager.VisualStateGroups">
                    <VisualStateGroupList>
                        <VisualStateGroup x:Name="CommonStates">
                            <VisualState x:Name="Normal">
                                <VisualState.Setters>
                                    <Setter Property="BackgroundColor" Value="{x:OnPlatform UWP=Transparent, iOS=White}" />
                                    <Setter TargetName="FlyoutItemLabel" Property="Label.TextColor" Value="{StaticResource Primary}" />
                                </VisualState.Setters>
                            </VisualState>
                            <VisualState x:Name="Selected">
                                <VisualState.Setters>
                                    <Setter Property="BackgroundColor" Value="{StaticResource Primary}" />
                                </VisualState.Setters>
                            </VisualState>
                        </VisualStateGroup>
                    </VisualStateGroupList>
                </Setter>
            </Style>

            <!--
            Custom Style you can apply to any Flyout Item
            -->
            <Style Class="MenuItemLayoutStyle" TargetType="Layout" ApplyToDerivedTypes="True">
                <Setter Property="VisualStateManager.VisualStateGroups">
                    <VisualStateGroupList>
                        <VisualStateGroup x:Name="CommonStates">
                            <VisualState x:Name="Normal">
                                <VisualState.Setters>
                                    <Setter TargetName="FlyoutItemLabel" Property="Label.TextColor" Value="{StaticResource Primary}" />
                                </VisualState.Setters>
                            </VisualState>
                        </VisualStateGroup>
                    </VisualStateGroupList>
                </Setter>
            </Style>
        </ResourceDictionary>
    </Shell.Resources>

    <!-- 
        When the Flyout is visible this defines the content to display in the flyout.
        FlyoutDisplayOptions="AsMultipleItems" will create a separate flyout item for each child element    
        https://docs.microsoft.com/dotnet/api/xamarin.forms.shellgroupitem.flyoutdisplayoptions?view=xamarin-forms
    -->
    <!--<FlyoutItem Title="About" Icon="icon_about.png">
        <ShellContent Route="AboutPage" ContentTemplate="{DataTemplate local:AboutPage}" />
    </FlyoutItem>
    <FlyoutItem Title="Browse" Icon="icon_feed.png">
        <ShellContent Route="ItemsPage" ContentTemplate="{DataTemplate local:ItemsPage}" />
	</FlyoutItem>-->

	<TabBar>
		<ShellContent Route="LoginPage"                  
					  Shell.FlyoutBehavior="Disabled"
					  ContentTemplate="{DataTemplate local:LoginPage}" />
	</TabBar>
	<TabBar>
		<ShellContent Route="StartupPage"                  
					  Shell.FlyoutBehavior="Disabled"
					  ContentTemplate="{DataTemplate local:StartupPage}" />
	</TabBar>

	<FlyoutItem Title="Tele Health" Icon="icon_about.png">
		<ShellContent Route="TeleHeathPage" ContentTemplate="{DataTemplate local:TeleHeathPage}" />
	</FlyoutItem>

	<!-- When the Flyout is visible this will be a menu item you can tie a click behavior to  -->
	<!--<MenuItem Text="Hangup" StyleClass="MenuItemLayoutStyle" Clicked="OnMenuItemClicked">
    </MenuItem>-->

    <!--
        TabBar lets you define content that won't show up in a flyout menu. When this content is active
        the flyout menu won't be available. This is useful for creating areas of the application where 
        you don't want users to be able to navigate away from. If you would like to navigate to this 
        content you can do so by calling 
        await Shell.Current.GoToAsync("//LoginPage");
    -->
	<!-- Optional Templates 
    // These may be provided inline as below or as separate classes.

    // This header appears at the top of the Flyout.
    // https://docs.microsoft.com/xamarin/xamarin-forms/app-fundamentals/shell/flyout#flyout-header
    <Shell.FlyoutHeaderTemplate>
        <DataTemplate>
            <Grid>ContentHere</Grid>
        </DataTemplate>
    </Shell.FlyoutHeaderTemplate>

    // ItemTemplate is for ShellItems as displayed in a Flyout
    // https://docs.microsoft.com/xamarin/xamarin-forms/app-fundamentals/shell/flyout#define-flyoutitem-appearance
    <Shell.ItemTemplate>
        <DataTemplate>
            <ContentView>
                Bindable Properties: Title, Icon
            </ContentView>
        </DataTemplate>
    </Shell.ItemTemplate>

    // MenuItemTemplate is for MenuItems as displayed in a Flyout
    // https://docs.microsoft.com/xamarin/xamarin-forms/app-fundamentals/shell/flyout#define-menuitem-appearance
    <Shell.MenuItemTemplate>
        <DataTemplate>
            <ContentView>
                Bindable Properties: Text, Icon
            </ContentView>
        </DataTemplate>
    </Shell.MenuItemTemplate>

    -->

</Shell>
