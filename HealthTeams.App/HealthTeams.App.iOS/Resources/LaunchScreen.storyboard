<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="6245" systemVersion="13F34" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" initialViewController="X5k-f2-b5h">
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="6238"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="gAE-YM-kbH">
            <objects>
                <viewController id="X5k-f2-b5h" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="Y8P-hJ-Z43"/>
                        <viewControllerLayoutGuide type="bottom" id="9ZL-r4-8FZ"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="yd7-JS-zBw">
                        <rect key="frame" x="0.0" y="0.0" width="600" height="600"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView userInteractionEnabled="NO" contentMode="scaleToFill" misplaced="YES" image="Icon-60.png" translatesAutoresizingMaskIntoConstraints="NO" id="23">
                                <rect key="frame" x="270" y="270" width="60" height="60"/>
                                <rect key="contentStretch" x="0.0" y="0.0" width="0.0" height="0.0"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" red="0.18359375" green="0.765625" blue="0.7890625" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="23" firstAttribute="centerY" secondItem="yd7-JS-zBw" secondAttribute="centerY" priority="1" id="39"/>
                            <constraint firstItem="23" firstAttribute="centerX" secondItem="yd7-JS-zBw" secondAttribute="centerX" priority="1" id="41"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="XAI-xm-WK6" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="349" y="339"/>
        </scene>
    </scenes>
    <resources>
        <image name="Icon-60.png" width="180" height="180"/>
    </resources>
</document>
