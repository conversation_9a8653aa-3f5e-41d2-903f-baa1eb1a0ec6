<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer>
		<integer>2</integer>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>MinimumOSVersion</key>
	<string>13.0</string>
	<key>CFBundleDisplayName</key>
	<string>HealthTeams</string>
	<key>CFBundleIdentifier</key>
	<string>au.com.healthteams.app</string>
	<key>CFBundleVersion</key>
	<string>1.0</string>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>CFBundleName</key>
	<string>HealthTeams.App</string>
	<key>XSAppIconAssets</key>
	<string>Assets.xcassets/AppIcon.appiconset</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Location is needed to identify which CPR session a caller is working on</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Location is needed to identify which CPR session a caller is working on</string>
	<key>NSLocationUsageDescription</key>
	<string>Location is needed to identify which CPR session a caller is working on</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Location is needed to identify which CPR session a caller is working on</string>
	<key>NSCalendarsUsageDescription</key>
	<string>Looking up a location may access the calendar</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>We use the microphone during an emergency to communicate with a dispatcher.</string>
	<key>NSCameraUsageDescription</key>
	<string>We use the camera during an emergency to communicate with a dispatcher</string>
</dict>
</plist>
