﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">iPhoneSimulator</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{91878048-568C-41E8-83CA-EABD0B3FB965}</ProjectGuid>
    <ProjectTypeGuids>{FEACFBD2-3405-455C-9665-78FE426C6842};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <TemplateGuid>{89a4fe7c-635d-49c9-8d8c-5cd363c0d68d}</TemplateGuid>
    <OutputType>Exe</OutputType>
    <RootNamespace>HealthTeams.App.iOS</RootNamespace>
    <IPhoneResourcePrefix>Resources</IPhoneResourcePrefix>
    <AssemblyName>HealthTeams.App.iOS</AssemblyName>
    <MtouchEnableSGenConc>true</MtouchEnableSGenConc>
    <MtouchHttpClientHandler>NSUrlSessionHandler</MtouchHttpClientHandler>
    <ProvisioningType>automatic</ProvisioningType>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|iPhoneSimulator' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\iPhoneSimulator\Debug</OutputPath>
    <DefineConstants>DEBUG</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <MtouchArch>x86_64</MtouchArch>
    <MtouchLink>None</MtouchLink>
    <MtouchDebug>true</MtouchDebug>
    <CodesignKey>Apple Development: John Emil (F56PRJ64FQ)</CodesignKey>
    <CodesignProvision>VS: WildCard Development</CodesignProvision>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|iPhoneSimulator' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\iPhoneSimulator\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <MtouchLink>None</MtouchLink>
    <MtouchArch>x86_64</MtouchArch>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|iPhone' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\iPhone\Debug</OutputPath>
    <DefineConstants>DEBUG</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <MtouchArch>ARM64</MtouchArch>
    <CodesignKey>Apple Development: John Emil (F56PRJ64FQ)</CodesignKey>
    <MtouchDebug>true</MtouchDebug>
    <CodesignEntitlements>Entitlements.plist</CodesignEntitlements>
    <MtouchLink>None</MtouchLink>
    <MtouchInterpreter>-all</MtouchInterpreter>
    <CodesignProvision>VS: WildCard Development</CodesignProvision>
    <BuildIpa>true</BuildIpa>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|iPhone' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\iPhone\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <MtouchArch>ARM64</MtouchArch>
    <CodesignKey>iPhone Developer</CodesignKey>
    <CodesignEntitlements>Entitlements.plist</CodesignEntitlements>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(RunConfiguration)' == 'Default' ">
    <AppExtensionDebugBundleId />
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="ACS\CallAgentDelegate.cs" />
    <Compile Include="ACS\CallDelegate.cs" />
    <Compile Include="ACS\CallingCallbackManager.cs" />
    <Compile Include="ACS\CallingManager.cs" />
    <Compile Include="ACS\RemoteParticipantDelegate.cs" />
    <Compile Include="Main.cs" />
    <Compile Include="AppDelegate.cs" />
    <None Include="Entitlements.plist" />
    <None Include="Info.plist" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon1024.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon180.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon167.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon152.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon120.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon87.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon80.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon76.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon60.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon58.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon40.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon29.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon20.png">
      <Visible>false</Visible>
    </ImageAsset>
    <BundleResource Include="Resources\icon_about.png" />
    <BundleResource Include="Resources\icon_about%402x.png" />
    <BundleResource Include="Resources\icon_about%403x.png" />
    <BundleResource Include="Resources\icon_feed.png" />
    <BundleResource Include="Resources\icon_feed%402x.png" />
    <BundleResource Include="Resources\icon_feed%403x.png" />
    <BundleResource Include="Resources\xamarin_logo.png" />
    <BundleResource Include="Resources\xamarin_logo%402x.png" />
    <BundleResource Include="Resources\xamarin_logo%403x.png" />
    <InterfaceDefinition Include="Resources\LaunchScreen.storyboard" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="Xamarin.iOS" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Xamarin.AzureCommunicationCalling.iOS">
      <Version>1.1.0-beta.1.1</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.Forms" Version="5.0.0.2083" />
    <PackageReference Include="Xamarin.Essentials" Version="1.7.0" />
    <PackageReference Include="Xamarin.Forms.Visual.Material">
      <Version>5.0.0.2083</Version>
    </PackageReference>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\iOS\Xamarin.iOS.CSharp.targets" />
  <ItemGroup>
    <ProjectReference Include="..\HealthTeams.App\HealthTeams.App.csproj">
      <Project>{F16F4B99-1356-406D-B465-7BEB0562360D}</Project>
      <Name>HealthTeams.App</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\Vitals.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\HealthTeamsLogo.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\callend.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\callstart.png" />
  </ItemGroup>
</Project>