﻿using System;
using Xamarin.AzureCommunicationCalling.iOS;

namespace HealthTeams.App.iOS.ACS
{
	public class CallingCallbackManager
    {
        public CallingCallbackManager(Action<ACSRemoteVideoStream, string> remoteVideoStreamAdded, Action<ACSRemoteVideoStream, string> remoteVideoStreamRemoved)
        {
            RemoteVideoStreamAdded = remoteVideoStreamAdded;
			RemoteVideoStreamRemoved = remoteVideoStreamRemoved;
		}

        public Action<ACSRemoteVideoStream, string> RemoteVideoStreamAdded { get; }
		public Action<ACSRemoteVideoStream, string> RemoteVideoStreamRemoved { get; }

		internal void HandleRemoteParticipantAdded(ACSRemoteParticipant remoteParticipant)
        {
            foreach (var videoStream in remoteParticipant.VideoStreams)
            {
                RemoteVideoStreamAdded?.Invoke(videoStream, (remoteParticipant.Identifier as CommunicationUserIdentifier).Identifier);
            }
        }

		internal void HandleRemoteParticipantRemoved(ACSRemoteParticipant remoteParticipant)
		{
            foreach (var videoStream in remoteParticipant.VideoStreams)
            {
                RemoteVideoStreamRemoved.Invoke(videoStream, (remoteParticipant.Identifier as CommunicationUserIdentifier).Identifier);
            }
        }
	}
}