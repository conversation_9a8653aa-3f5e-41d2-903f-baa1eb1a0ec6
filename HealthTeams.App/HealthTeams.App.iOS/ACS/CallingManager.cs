﻿using Foundation;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Xamarin.AzureCommunicationCalling.iOS;
using Xamarin.Forms;
using Xamarin.Forms.Platform.iOS;

[assembly:Dependency(typeof(HealthTeams.App.iOS.ACS.CallingManager))]
namespace HealthTeams.App.iOS.ACS
{
    public class CallingManager : IACSCallingManager
    {
        private ACSCallClient _callClient;
        private ACSDeviceManager _deviceManager;
        private ACSCallAgent _callAgent;
        private ACSCall _call;
        private ACSLocalVideoStream _localVideoStream;
        private ACSVideoStreamRenderer _localVideoStreamRenderer;
        private readonly CallingCallbackManager _videoCallbackManager;
        private readonly List<ACSRemoteVideoData> _remoteVideoStreams;

        public CallingManager()
        {
            _remoteVideoStreams = new List<ACSRemoteVideoData>();
            _videoCallbackManager = new CallingCallbackManager(RemoteVideoStreamAdded, RemoteVideoStreamRemoved);
        }

        private void RemoteVideoStreamAdded(ACSRemoteVideoStream remoteVideoStream, string acsUserId)
        {
            if (!remoteVideoStream.IsAvailable ||
                _remoteVideoStreams.Any(s => s.RemoteVideoStream.Id == remoteVideoStream.Id))
            {
                return;
            }

            var remoteVideoData = new RemoteVideoData(acsUserId);
            Device.BeginInvokeOnMainThread(() =>
            {
                var renderer = new ACSVideoStreamRenderer(remoteVideoStream, out var rendererError);
                ThrowIfError(rendererError);
                var renderingOptions = new ACSCreateViewOptions(ACSScalingMode.Crop);
                var nativeView = renderer.CreateViewWithOptions(renderingOptions, out var createViewError);
                ThrowIfError(createViewError);
                var formsView = nativeView.ToView();
                remoteVideoData.FormsView = formsView;
                RemoteVideoAdded?.Invoke(this, remoteVideoData);
            });
            _remoteVideoStreams.Add(new ACSRemoteVideoData(remoteVideoStream, remoteVideoData));
        }

        private void RemoteVideoStreamRemoved(ACSRemoteVideoStream remoteVideoStream, string acsUserId)
        {
            if (remoteVideoStream.IsAvailable ||
                _remoteVideoStreams.Any(s => s.RemoteVideoStream.Id == remoteVideoStream.Id) == false)
            {
                return;
            }

            var x = _remoteVideoStreams.FirstOrDefault(s => s.RemoteVideoStream.Id == remoteVideoStream.Id);
            Device.BeginInvokeOnMainThread(() =>
            {
                RemoteVideoRemoved?.Invoke(this, x.RemoteVideoData);
            });
            _remoteVideoStreams.Remove(x);
        }

        private void ThrowIfError(NSError rendererError)
        {
            if (!string.IsNullOrEmpty(rendererError?.Description))
            {
                throw new Exception(rendererError.Description);
            }
        }

        public Task<bool> Init(string token)
        {
            var initTask = new TaskCompletionSource<bool>();
            _callClient = new ACSCallClient();

            void OnCallAgentCreated(ACSCallAgent callAgent, NSError callAgentError)
            {
                if (callAgentError != null)
                {
                    initTask.TrySetCanceled();
                    throw new Exception(callAgentError.Description);
                }
                _callAgent = callAgent;
                _callAgent.Delegate = new CallAgentDelegate(_videoCallbackManager);

                // Create device manager AFTER call agent
                void OnDeviceManagerCreated(ACSDeviceManager deviceManager, NSError deviceManagerError)
                {
                    if (deviceManagerError != null)
                    {
                        throw new Exception(deviceManagerError.Description);
                    }
                    _deviceManager = deviceManager;
                    initTask.TrySetResult(true);
                }
                _callClient.GetDeviceManagerWithCompletionHandler(OnDeviceManagerCreated);
            }

            var credentials = new CommunicationTokenCredential(token, out var nSError);
            if (nSError != null)
            {
                initTask.TrySetCanceled();
                throw new Exception(nSError.Description);
            }
            _callClient.CreateCallAgent(credentials, OnCallAgentCreated);

            return initTask.Task;
        }

        public void CallEchoService()
        {
            var callOptions = new ACSStartCallOptions();
            var camera = _deviceManager.Cameras.First(c => c.CameraFacing == ACSCameraFacing.Front);
            callOptions.AudioOptions = new ACSAudioOptions();

            //callOptions.AudioOptions.Muted = true;
            var localVideoStream = new ACSLocalVideoStream(camera);
            callOptions.VideoOptions = new ACSVideoOptions(new[] { localVideoStream });

            var receivers = new CommunicationIdentifier[] { new CommunicationUserIdentifier("8:echo123") };

            _callAgent.StartCall(receivers, callOptions, null);
        }

        public event EventHandler<View> LocalVideoAdded = delegate { };
        public event EventHandler<RemoteVideoData> RemoteVideoAdded = delegate { };
        public event EventHandler<RemoteVideoData> RemoteVideoRemoved = delegate { };

        public void CallPhone(string phoneNumber)
        {
            var acsNumber = new PhoneNumberIdentifier(phoneNumber, phoneNumber);
            var receivers = new CommunicationIdentifier[] { acsNumber };
            var callOptions = new ACSStartCallOptions
            {
                AudioOptions = new ACSAudioOptions()
            };
            _callAgent.StartCall(receivers, callOptions, callCompleted);
        }

        private void callCompleted(ACSCall call, NSError err)
        {
            _call = call;
            // Respond to changes
            _call.Delegate = new CallDelegate(_videoCallbackManager);
            // Setup initial streams. This is clumsy and probably an API flaw...
            // IMHO delegate should be called after setting it on existing state...
            foreach (var remoteParticipant in _call.RemoteParticipants)
            {
                remoteParticipant.Delegate = new RemoteParticipantDelegate(_videoCallbackManager);
                foreach (var remoteVideoStream in remoteParticipant.VideoStreams)
                {
                    RemoteVideoStreamAdded(remoteVideoStream, (remoteParticipant.Identifier as CommunicationUserIdentifier).Identifier);
                }
            }

            _call.StartVideo(_localVideoStream, OnVideoStarted);
        }

        public async Task JoinGroup(Guid groupID)
        {
            var camera = _deviceManager
                .Cameras.First(c => c.CameraFacing == ACSCameraFacing.Front);
            _localVideoStream = new ACSLocalVideoStream(camera);
            _localVideoStreamRenderer = new ACSVideoStreamRenderer(_localVideoStream, out var rendererError);
            ThrowIfError(rendererError);
            var renderingOptions = new ACSCreateViewOptions(ACSScalingMode.Crop);
            var nativeView = _localVideoStreamRenderer.CreateViewWithOptions(renderingOptions, out var viewError);
            ThrowIfError(viewError);
            var formsView = nativeView.ToView();

            LocalVideoAdded?.Invoke(this, formsView);

            var groupCallLocator = new ACSGroupCallLocator(new NSUuid(groupID.ToString()));
            var callOptions = new ACSJoinCallOptions
            {
                AudioOptions = new ACSAudioOptions(),
                VideoOptions = new ACSVideoOptions(new[] { _localVideoStream })
            };
            //callOptions.AudioOptions.Muted = true;
            _callAgent.JoinWithMeetingLocator(groupCallLocator, callOptions, callCompleted);
        }

        void OnVideoStarted(NSError error)
        {
            if (error != null) throw new Exception(error.Description);
        }

        public void Hangup()
        {
            _call?.HangUp(new ACSHangUpOptions(), OnVideoHangup);
            _localVideoStreamRenderer?.Dispose();
            _localVideoStream?.Dispose();
            _localVideoStream = null;
            _remoteVideoStreams.Clear();
            _call?.Dispose();
        }

        private void OnVideoHangup(NSError nsError)
        {
            if (nsError != null)
            {
                throw new Exception(nsError.ToString());
            }
        }
    }

    class ACSRemoteVideoData {
		public ACSRemoteVideoData(ACSRemoteVideoStream remoteVideoStream, RemoteVideoData remoteVideoData)
		{
			RemoteVideoStream = remoteVideoStream;
			RemoteVideoData = remoteVideoData;
		}

		public ACSRemoteVideoStream RemoteVideoStream { get; }
		public RemoteVideoData RemoteVideoData { get; }
	}
}