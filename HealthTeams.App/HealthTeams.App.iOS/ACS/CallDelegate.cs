﻿using System;
using System.Collections.Generic;
using Xamarin.AzureCommunicationCalling.iOS;

namespace HealthTeams.App.iOS.ACS
{
    public class CallDelegate : ACSCallDelegate
    {
        private CallingCallbackManager _videoCallbackManager;
        private List<ACSRemoteParticipant> _remoteParticipants = new List<ACSRemoteParticipant>();

        public CallDelegate(CallingCallbackManager videoCallbackManager)
        {
            _videoCallbackManager = videoCallbackManager;
        }

        public override void OnStateChanged(ACSCall call, ACSPropertyChangedEventArgs args)
        {
            Logger.Debug($"Call state changed: {call.State}");
        }

        public override void OnLocalVideoStreamsUpdated(ACSCall call, ACSLocalVideoStreamsUpdatedEventArgs args)
        {
            Logger.Debug($"Local video streams changed");
        }

        public override void OnRemoteParticipantsUpdated(ACSCall call, ACSParticipantsUpdatedEventArgs args)
        {
            Logger.Debug($"Remote participants changed");
            foreach (var participant in args.AddedParticipants)
            {
                _videoCallbackManager.HandleRemoteParticipantAdded(participant);
                participant.Delegate = new RemoteParticipantDelegate(_videoCallbackManager);
                _remoteParticipants.Add(participant);
            }
            foreach (var participant in args.RemovedParticipants)
            {
                _videoCallbackManager.HandleRemoteParticipantRemoved(participant);
                participant.Delegate = new RemoteParticipantDelegate(_videoCallbackManager);
                _remoteParticipants.Add(participant);
            }

        }
    }
}
