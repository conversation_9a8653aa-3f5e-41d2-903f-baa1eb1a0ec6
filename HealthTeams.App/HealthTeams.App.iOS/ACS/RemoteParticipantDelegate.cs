﻿using Xamarin.AzureCommunicationCalling.iOS;

namespace HealthTeams.App.iOS.ACS
{
    public class RemoteParticipantDelegate : ACSRemoteParticipantDelegate
    {
        private readonly CallingCallbackManager _videoCallbackManager;

        public RemoteParticipantDelegate(CallingCallbackManager videoCallbackManager)
        {
            _videoCallbackManager = videoCallbackManager;
        }

        public override void OnStateChanged(ACSRemoteParticipant remoteParticipant, ACSPropertyChangedEventArgs args)
        {
            foreach (var remoteVideoStream in remoteParticipant.VideoStreams)
            {
                _videoCallbackManager.RemoteVideoStreamAdded?.Invoke(remoteVideoStream, (remoteParticipant.Identifier as CommunicationUserIdentifier).Identifier);
            }
        }

        public override void OnVideoStreamsUpdated(ACSRemoteParticipant remoteParticipant, ACSRemoteVideoStreamsEventArgs args)
        {
            foreach (var remoteVideoStream in args.AddedRemoteVideoStreams)
            {
                _videoCallbackManager.RemoteVideoStreamAdded?.Invoke(remoteVideoStream, (remoteParticipant.Identifier as CommunicationUserIdentifier).Identifier);
            }
            foreach (var remoteVideoStream in args.RemovedRemoteVideoStreams)
            {
                _videoCallbackManager.RemoteVideoStreamRemoved?.Invoke(remoteVideoStream, (remoteParticipant.Identifier as CommunicationUserIdentifier).Identifier);
            }
        }
    }
}